{"name": "phase", "version": "0.2.4", "private": true, "description": "Frontend for Phase", "type": "module", "installConfig": {"hoistingLimits": "workspaces"}, "dependencies": {"@apollo/client": "^3.7.7", "@babel/core": "^7.26.9", "@babel/plugin-transform-react-constant-elements": "^7.9.0", "@babel/preset-react": "^7.9.4", "@graphql-codegen/cli": "^2.16.2", "@graphql-codegen/introspection": "^2.2.3", "@graphql-codegen/typescript": "^2.8.6", "@graphql-codegen/typescript-operations": "^2.5.11", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@growthbook/growthbook-react": "^0.26.0", "@jam.dev/sdk": "^0.0.5", "@liveblocks/client": "^2.4.0", "@liveblocks/react": "^2.4.0", "@lottiefiles/react-lottie-player": "3.5.3", "@phase-software/animation-composer": "workspace:^", "@phase-software/code-exporter": "workspace:^", "@phase-software/data-store": "workspace:^", "@phase-software/data-utils": "workspace:^", "@phase-software/event-manager": "workspace:^", "@phase-software/input-system": "workspace:^", "@phase-software/lottie-exporter": "workspace:^", "@phase-software/lottie-importer": "workspace:^", "@phase-software/phase-importer": "workspace:^", "@phase-software/renderer": "workspace:^", "@phase-software/svg-importer": "workspace:^", "@phase-software/transition-manager": "workspace:^", "@popperjs/core": "^2.11.5", "@sentry/integrations": "^7.119.2", "@sentry/react": "^8.34.0", "@sentry/types": "^8.34.0", "@sentry/wasm": "^8.34.0", "@tanstack/react-virtual": "^3.0.0-beta.54", "aws-rum-web": "^1.14.0", "crypto-js": "^4.2.0", "graphql": "^16.6.0", "i18next": "^23.11.5", "i18next-http-backend": "^2.5.2", "jotai": "^2.8.3", "jotai-devtools": "^0.10.0", "jszip": "^3.5.0", "lib0": "^0.2.98", "lodash": "^4.17.5", "moment": "^2.22.2", "nats.ws": "^1.10.2", "normalizr": "^3.6.0", "prismjs": "^1.30.0", "prop-types": "^15.7.2", "qr-code-styling": "^1.9.2", "query-string": "^6.2.0", "react": "^18.3.1", "react-content-loader": "^6.2.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.2", "react-popper": "^2.3.0", "react-router-dom": "^6.23.0", "react-transition-group": "^4.4.5", "socket.io-client": "^4.8.1", "styled-components": "^6.1.11", "styled-system": "^5.1.5", "subscriptions-transport-ws": "^0.11.0", "uuid": "^11.0.5", "workbox-window": "^7.1.0", "y-utility": "^0.1.3", "yjs": "^13.6.7"}, "scripts": {"postinstall": "bash ./bin/postinstall.sh ", "shell": "bash", "lint": "eslint \"./src/**/*.js\"", "lint:changed": "node ./bin/eslint", "dev": "NODE_OPTIONS=--openssl-legacy-provider vite", "start": "NODE_OPTIONS=--openssl-legacy-provider vite", "build": "tsc --noEmit && NODE_OPTIONS='--openssl-legacy-provider --max-old-space-size=6144' vite build --profile", "preview": "yarn build --mode preview && vite preview", "test": "TZ=UTC vitest", "test:unit": "TZ=UTC vitest run --coverage", "test:debug": "TZ=UTC vitest --inspect-brk  --no-cache --runInBand", "vite-bundle-analyzer": "mkdir -p viz && npx vite-bundle-visualizer -o viz/treemap.html && npx vite-bundle-visualizer -t sunburst -o viz/sunburst.html && npx vite-bundle-visualizer -t network -o viz/network.html", "storybook": "TAILWIND_MODE=watch NODE_OPTIONS=--openssl-legacy-provider storybook dev -p 6006 --https --ssl-cert app.local.phase.com.pem --ssl-key app.local.phase.com-key.pem", "build:storybook": "storybook build -o build/storybook", "build:static": "yarn run build && yarn run build:storybook", "fontImport": "cd src/tools && ./fontBase64Convert.sh", "docs": "esdoc -c .esdoc.json", "deploy:dev": "bin/deploy_dev.sh", "dcr": "bin/dcr_prep.sh && node bin/dcr.js", "chromatic": "chromatic", "build-storybook": "yarn run build:storybook", "codegen": "yarn codegen:graphql && yarn codegen:api && yarn codegen:api:presence && yarn codegen:api:liveblocks", "codegen:graphql": "dotenv -- cross-var graphql-codegen --config ./hasura/config.yaml", "codegen:api": "dotenv -- cross-var openapi-generator-cli generate -i %API_SWAGGER% -g typescript-fetch -c openapi-config.json -o src/generated/api", "codegen:api:presence": "dotenv -- cross-var openapi-generator-cli generate -i %PRESENCE_API_SWAGGER% -g typescript-fetch -c openapi-config.json -o src/generated/api/presence", "codegen:api:liveblocks": "dotenv -- cross-var openapi-generator-cli generate -i %LIVEBLOCKS_API_SWAGGER% -g typescript-fetch -c openapi-config.json -o src/generated/api/liveblocks"}, "author": "Undefined OÜ", "devDependencies": {"@chromatic-com/storybook": "^1", "@graphql-codegen/cli": "^2.16.2", "@graphql-codegen/introspection": "^2.2.3", "@graphql-codegen/typescript": "^2.8.6", "@graphql-codegen/typescript-operations": "^2.5.11", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@openapitools/openapi-generator-cli": "^2.7.0", "@storybook/addon-actions": "^8.0.10", "@storybook/addon-essentials": "^8.0.10", "@storybook/addon-links": "^8.0.10", "@storybook/addon-mdx-gfm": "^8.0.10", "@storybook/manager-api": "^8.0.10", "@storybook/node-logger": "^8.0.10", "@storybook/preview-api": "^8.1.1", "@storybook/react": "^8.0.10", "@storybook/react-vite": "^8.0.10", "@storybook/theming": "^8.0.10", "@tailwindcss/aspect-ratio": "^0.4.2", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^15.0.5", "@testing-library/user-event": "^14.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/crypto-js": "^4.2.2", "@types/enzyme": "^3.10.13", "@types/jest": "^29.5.3", "@types/node": "^20.12.12", "@types/prismjs": "^1.26.5", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.5", "@types/react-transition-group": "^4.4.10", "@types/styled-components": "^5.1.34", "@types/styled-system": "^5.1.22", "@types/uuid": "^9.0.3", "@typescript-eslint/parser": "^7.7.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.4.0", "autoprefixer": "^10.4.19", "babel-polyfill": "^6.26.0", "brfs": "^2.0.2", "chai": "^4.1.2", "chokidar": "^3.5.2", "chromatic": "^11.3.0", "cross-var": "^1.1.0", "dotenv": "^16.4.5", "dotenv-cli": "^6.0.0", "esdoc": "^1.1.0", "esdoc-ecmascript-proposal-plugin": "^1.0.0", "esdoc-react-plugin": "^1.0.1", "esdoc-standard-plugin": "^1.0.0", "eslint": "^8.56.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^4.1.4", "graphql-tag": "^2.12.6", "js-file-downloader": "^1.1.15", "jsdom": "^24.0.0", "postcss": "^8.4.38", "pre-commit": "^1.2.2", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "shelljs": "^0.7.8", "storybook": "^8.0.10", "storybook-addon-apollo-client": "^6.0.0", "storybook-addon-pseudo-states": "^4.0.2", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^5.2.3", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "^0.20.1", "vite-plugin-svgr": "^4.2.0", "vitest": "^1.4.0"}, "homepage": "/", "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}