import { memo, useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { atom, useAtomValue, useSetAtom } from 'jotai'

// @ts-ignore TODO: create type definition file(.d.ts) for the types
import { MAX_DURATION_TIME } from '@phase-software/data-store'
import { clamp } from '@phase-software/data-utils/src/commons'
import { FrameType, KeyframeIndicatorType } from '@phase-software/types'
import { useResetAtom } from 'jotai/utils'

import {
  DragAction,
  ScrollDirection,
  activeKeyframeAtom,
  activeKeyframeTimeAtom,
  autoScrollDirectionAtom,
  dataStoreAtom,
  dragActionAtom,
  highlightSelectedKeyframesAtom,
  highlightedKeyframesAtom,
  selectedKeyframeIdListAtom,
  selectedKeyframeTimeRangeAtom
} from '../../../atoms'
import { isTransitionPlayingAtom, snapThresholdAtom } from '../../../atoms/ui/actionPanel'
import { ModalKey } from '../../../constants/global'
import useKeyframeManagementActions from '../../../hooks/dataStore/useKeyframeManagementActions'
import useSelectionActions from '../../../hooks/dataStore/useSelectionActions'
import { useTransitionActions } from '../../../hooks/dataStore/useTransitionActions'
import { UseDnDMeta, useDrag } from '../../../hooks/useDrag'
import { useModalContext } from '../../../providers/ModalContextProvider'
import { useInteractionActions } from '../../../providers/dataStore/InteractionProvider'
import { Keyframe as KeyframeComponent, Tooltip } from '../../shared'
import { easingTypeNameMap } from '../constant'
import { getScrollDirection, getSnappedTime, getTimeAtStep } from '../utils'
import { KeyframeStretchHandle } from './KeyframeStretchHandle'

type TrackKeyframeProps = {
  time: number
  pixelsPerMs: number
  trackId: string
  keyframeClassName?: string
}

export interface KeyframeProps {
  easingType: keyof typeof easingTypeNameMap
  frameType: FrameType
}

interface KeyFrame {
  easingType: keyof typeof easingTypeNameMap
  frameType: FrameType
}

const useZIndexAtom = (selected: boolean, time: number) => {
  return useMemo(
    () =>
      atom((get) => {
        const activeTime = get(activeKeyframeTimeAtom)
        const { start, end } = get(selectedKeyframeTimeRangeAtom)
        if (time === activeTime) {
          return 'z-40'
        }
        if (time === start || time === end) {
          return 'z-30'
        }
        if (selected) {
          return 'z-20'
        }
        return 'z-10'
      }),
    [time, selected]
  )
}

const TrackKeyframe = ({ time, pixelsPerMs, trackId }: TrackKeyframeProps) => {
  const dataStore = useAtomValue(dataStoreAtom)
  const { getAggregateKeyFrame, getSelectedKeyframesTimeRange, getAllKeyframeTimeList, getKeyframeIdList } =
    useInteractionActions()

  const { selectNewKeyframes, toggleKeyframeSelection, selectKeyframesAndSegments, getSelectedSegmentIds } =
    useSelectionActions()
  const { setSelectedKeyframeAndPresetByTime, stretchKeyframeList } = useKeyframeManagementActions()
  const { playAnimation } = useTransitionActions()

  const selectedKeyframeIdList = useAtomValue(selectedKeyframeIdListAtom)
  const isTransitionPlaying = useAtomValue(isTransitionPlayingAtom)

  const snapThreshold = useAtomValue(snapThresholdAtom)

  const { t } = useTranslation('file', { keyPrefix: 'easing_type' })
  const { openModal } = useModalContext()

  const keyframeIdList = getKeyframeIdList(trackId, time)
  const aggregateKeyframe = getAggregateKeyFrame(keyframeIdList) as KeyFrame // render (calc)

  const setAutoScrollDirection = useSetAtom(autoScrollDirectionAtom)
  const setActiveKeyframe = useSetAtom(activeKeyframeAtom)
  const setDragAction = useSetAtom(dragActionAtom)

  const highlightedKeyframes = useAtomValue(highlightedKeyframesAtom) // render (hook) <- root cause
  const resetHighlightedKeyframes = useResetAtom(highlightedKeyframesAtom)
  const highlightSelectedKeyframes = useSetAtom(highlightSelectedKeyframesAtom)

  const ref = useRef(null)
  const [showTip, setShowTip] = useState(true)

  // while dragging KF overlap another KF, should display selected style
  const isSelected = keyframeIdList.some((id: string) => selectedKeyframeIdList.includes(id))
  const zIndex = useAtomValue(useZIndexAtom(isSelected, time))

  const keyframePosition = time * pixelsPerMs
  const startStretchKeyframe = useDrag({
    containerSelector: '.pt-action-track-list',
    onStart: useCallback(
      (e: React.MouseEvent<HTMLElement>, meta: UseDnDMeta, setData: (data: any) => void) => {
        e.stopPropagation()

        if (e.button === 2) {
          return false
        }

        const snappableTimeList = getAllKeyframeTimeList(true)

        const trackList = meta.container
        const bounds = trackList
          ? trackList.getBoundingClientRect()
          : { left: 0, top: 0, width: window.innerWidth, height: window.innerHeight }

        const { min, max } = getSelectedKeyframesTimeRange()

        highlightSelectedKeyframes()

        const dir = e.currentTarget.dataset.dir || null
        setDragAction(DragAction.STRETCH)
        setActiveKeyframe({
          handle: dir,
          time: dir === 'left' ? min : max
        })
        setData({
          trackList,
          scrollLeft: trackList ? trackList.scrollLeft : 0,
          keyframeList: selectedKeyframeIdList,
          snappableTimeList,
          dir,
          bounds,
          startTime: min,
          endTime: max
        })
      },
      [
        setDragAction,
        getAllKeyframeTimeList,
        highlightSelectedKeyframes,
        getSelectedKeyframesTimeRange,
        selectedKeyframeIdList,
        setActiveKeyframe
      ]
    ),
    onUpdate: useCallback(
      (e: MouseEvent, meta: UseDnDMeta) => {
        const scrollDiff = meta.data.trackList?.scrollLeft - meta.data.scrollLeft
        const diffMs = getTimeAtStep((meta.dx + scrollDiff) / pixelsPerMs)

        if (meta.data.dir === 'left') {
          const startTime = getSnappedTime(
            meta.data.startTime + diffMs,
            e.shiftKey ? meta.data.snappableTimeList : [],
            snapThreshold
          )
          const endTime = meta.data.endTime
          setActiveKeyframe({
            handle: meta.data.dir,
            time: clamp(startTime, 0, MAX_DURATION_TIME)
          })
          stretchKeyframeList(meta.data.keyframeList, startTime, endTime, meta.finished)
        } else {
          const startTime = meta.data.startTime
          const endTime = getSnappedTime(
            meta.data.endTime + diffMs,
            e.shiftKey ? meta.data.snappableTimeList : [],
            snapThreshold
          )
          setActiveKeyframe({
            handle: meta.data.dir,
            time: clamp(endTime, 0, MAX_DURATION_TIME)
          })
          stretchKeyframeList(meta.data.keyframeList, startTime, endTime, meta.finished)
        }

        const direction = getScrollDirection(e, meta.data.bounds)
        setAutoScrollDirection(direction)
      },
      [setAutoScrollDirection, stretchKeyframeList, setActiveKeyframe, pixelsPerMs, snapThreshold]
    ),
    onEnd: useCallback(() => {
      setAutoScrollDirection(new Set<ScrollDirection>())
      resetHighlightedKeyframes()
      setDragAction(DragAction.NONE)
      setActiveKeyframe({ handle: null, time: null })

      dataStore.commitUndo()
    }, [setAutoScrollDirection, resetHighlightedKeyframes, setDragAction, setActiveKeyframe, dataStore])
  })

  const startMovingKeyframe = useDrag({
    containerSelector: '.pt-action-track-list',
    onStart: useCallback(
      (e: React.MouseEvent<HTMLElement>, meta: UseDnDMeta, setData: (data: any) => void) => {
        e.stopPropagation()
        const snappableTimeList = getAllKeyframeTimeList(true)

        // update selection
        let newSelectedKeyframeIdList = selectedKeyframeIdList
        if (e.metaKey || e.ctrlKey) {
          newSelectedKeyframeIdList = toggleKeyframeSelection(keyframeIdList)
        } else if (e.shiftKey) {
          newSelectedKeyframeIdList = selectNewKeyframes(keyframeIdList)
        } else if (!isSelected) {
          const { kfs } = selectKeyframesAndSegments(keyframeIdList, [])
          newSelectedKeyframeIdList = kfs
        }

        const selectedPresetIds = getSelectedSegmentIds()

        if (e.button === 2) {
          return false
        }

        const trackList = meta.container
        const bounds = trackList
          ? trackList.getBoundingClientRect()
          : { left: 0, top: 0, width: window.innerWidth, height: window.innerHeight }

        const scrollLeft = trackList?.scrollLeft || 0
        const wasPlaying = isTransitionPlaying

        setActiveKeyframe({ handle: 'keyframe', time })
        setDragAction(DragAction.MOVE)
        setShowTip(false)
        setData({
          wasPlaying,
          scrollLeft,
          trackList,
          snappableTimeList,
          snapThreshold,
          keyframeList: newSelectedKeyframeIdList,
          presetList: selectedPresetIds,
          bounds,
          prevClientX: e.clientX,
          delta: 0,
          startTime: time
        })
      },
      [
        getAllKeyframeTimeList,
        selectedKeyframeIdList,
        isSelected,
        getSelectedSegmentIds,
        isTransitionPlaying,
        setActiveKeyframe,
        time,
        setDragAction,
        snapThreshold,
        toggleKeyframeSelection,
        keyframeIdList,
        selectNewKeyframes,
        selectKeyframesAndSegments
      ]
    ),
    onUpdate: useCallback(
      (e: MouseEvent, meta: UseDnDMeta) => {
        const scrollDiff = meta.data.trackList?.scrollLeft - meta.data.scrollLeft
        const diffMs = getTimeAtStep((meta.dx + scrollDiff) / pixelsPerMs)

        const keyframeList = meta.data.keyframeList
        const presetList = meta.data.presetList

        const startTime = getSnappedTime(
          meta.data.startTime + diffMs,
          e.shiftKey ? meta.data.snappableTimeList : [],
          snapThreshold
        )

        setActiveKeyframe({ handle: 'keyframe', time: startTime })

        setSelectedKeyframeAndPresetByTime(keyframeList, presetList, diffMs, meta.finished)

        const direction = getScrollDirection(e, meta.data.bounds)
        setAutoScrollDirection(direction)
      },
      [pixelsPerMs, snapThreshold, setSelectedKeyframeAndPresetByTime, setActiveKeyframe, setAutoScrollDirection]
    ),
    onEnd: useCallback(
      // @ts-ignore
      (e: MouseEvent, meta: UseDnDMeta) => {
        const { wasPlaying } = meta.data
        setAutoScrollDirection(new Set<ScrollDirection>())
        resetHighlightedKeyframes()
        dataStore.commitUndo()
        setDragAction(DragAction.NONE)
        setActiveKeyframe({ handle: null, time: null })
        if (wasPlaying) {
          playAnimation()
        }
      },
      [setAutoScrollDirection, resetHighlightedKeyframes, dataStore, setDragAction, setActiveKeyframe, playAnimation]
    )
  })

  if (!aggregateKeyframe) {
    return null
  }

  // should display explicit style if Mix
  const aggregateKeyframeType =
    aggregateKeyframe.frameType === FrameType.INITIAL ? KeyframeIndicatorType.INITIAL : KeyframeIndicatorType.EXPLICIT

  const shouldHighlight = keyframeIdList.some((kfId: string) => highlightedKeyframes.has(kfId))

  return (
    <div
      ref={ref}
      className={`js-kf absolute left-0 flex items-center justify-center w-16 h-16 ${zIndex}`}
      data-time={time}
      data-track-id={trackId}
      data-test-id={`keyframe-indicator-${time}-${trackId}`}
      style={{ transform: `translateX(calc(${keyframePosition}px - 50%))` }}
      onContextMenu={() => openModal(ModalKey.KEYFRAME_CONTEXT_MENU, { trigger: ref })}
      onMouseDown={startMovingKeyframe}
    >
      {isSelected && <KeyframeStretchHandle dir="left" onDrag={startStretchKeyframe} time={time} />}
      <Tooltip content={showTip ? t(easingTypeNameMap[aggregateKeyframe.easingType]) : ''}>
        <KeyframeComponent type={aggregateKeyframeType} selected={isSelected} highlighted={shouldHighlight} />
      </Tooltip>
      {isSelected && <KeyframeStretchHandle dir="right" onDrag={startStretchKeyframe} time={time} />}
    </div>
  )
}

export default memo(TrackKeyframe)
