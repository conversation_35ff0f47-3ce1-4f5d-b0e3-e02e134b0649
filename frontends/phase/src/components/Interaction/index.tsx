import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useAtom, useAtomValue, useSetAtom } from 'jotai'

import { Player } from '@lottiefiles/react-lottie-player'

import {
  isAction<PERSON>ode<PERSON>tom,
  isEditingSelector<PERSON>tom,
  isEditingStateAtom,
  selectedActionIdAtom,
  selectedKeyframeIdListAtom,
  selectedResponseIdAtom
} from '../../atoms'
import { flattenElementListAtom, responseByIdAtomFamily } from '../../atoms/entities'
import {
  boundedScaleAtom,
  durationAtom,
  durationEffectsAtom,
  scaleAtom,
  timelineWidthAtom,
  undoableActionPanelVisibleAtom
} from '../../atoms/ui/actionPanel'
import { FILE_EDITOR_LAYOUT_AREAS } from '../../constants/global'
import { useUIActions } from '../../hooks/useUIActions'
import { useLayout } from '../../providers/LayoutProvider'
import { useUI } from '../../providers/dataStore/UIProvider'
import ActionBar from '../ActionBar'
import { ACTION_BAR_TYPE } from '../ActionBar/constant'
import ActionPanelHeader from './ActionPanelHeader'
import EasingPanel from './Easing/EasingPanel'
import SelectorBar from './SelectorBar'
import TimeRuler from './TimeRuler'
import { TrackInfoList, TrackKeyFrameList } from './TrackList'
import actionPanelAnimation from './actionPanelEmpty.json'
import { EASING_PANEL_WIDTH_PX, TIMELINE_HEADER_WIDTH_PX, TIMELINE_ZOOM_SCALE_MULTIPLIER } from './constant'
import { ScrollActionContext, ScrollContext } from './contexts'
import { SubscribeInputSystemModifierKeys, SubscribeSelectedKeyframeRange } from './subscribers'
import { getTimeAtStep } from './utils'

const computeScrollLeftAfterZoom = (
  mouseX: number,
  currentScale: number,
  nextScale: number,
  duration: number,
  timelineWidth: number,
  currentScrollLeft: number
) => {
  // Compute time under the mouse before zooming
  const focusedTime = getTimeAtStep(((mouseX + currentScrollLeft) * duration) / (timelineWidth * currentScale))

  // Compute new scroll position after zooming
  return (focusedTime * timelineWidth * nextScale) / duration - mouseX
}

const EmptyState = () => {
  const { t } = useTranslation('file')
  return (
    <div
      className="grid place-items-center relative pointer-events-none text-light-overlay-40 z-1 pt-16 pl-16 overflow-hidden"
      style={{ gridArea: 'keyframe' }}
    >
      <div className="flex flex-col items-center pt-action-empty-state">
        <Player
          autoplay
          loop
          src={actionPanelAnimation}
          style={{
            height: '160px',
            width: '240px'
          }}
        />
        <div className="pb-32">{t('empty_track_list_note')}</div>
      </div>
    </div>
  )
}

type ResponseProps = {
  id: string
}

export type cacheRefProps = {
  panel: null | string
  scalePosition: number
  scrollWidth: number
  playHeadPosition: number
}

const Response = ({ id }: ResponseProps) => {
  const { actionHeight } = useLayout()
  const response = useAtomValue(responseByIdAtomFamily(id))
  const duration = useAtomValue(durationAtom)

  const [scale, setScale] = useAtom(scaleAtom)
  const boundedScale = useAtomValue(boundedScaleAtom)
  const flattenElementList = useAtomValue(flattenElementListAtom)
  const isActionPanelVisible = useAtomValue(undoableActionPanelVisibleAtom)
  const isEditingSelector = useAtomValue(isEditingSelectorAtom)
  const selectedKeyframeIdList = useAtomValue(selectedKeyframeIdListAtom)
  const timelineWidth = useAtomValue(timelineWidthAtom)
  const subscribeToEffects = useSetAtom(durationEffectsAtom)

  const trackList = flattenElementList.filter((elementId) => response?.elementTrackMap.has(elementId))

  const lastAppliedScale = useRef<null | number>(null)
  const trackKeyframeRef = useRef<HTMLDivElement>(null)
  const rulerRef = useRef<HTMLDivElement>(null)

  const trackInfoRef = useRef()
  const playHeadRef = useRef(null)
  const ref = useRef<HTMLDivElement>(null)
  const cacheRef = useRef({ panel: null, scalePosition: 0.5, scrollWidth: 0, playHeadPosition: 0 })

  const shouldShowActionPanel = useMemo(
    () => !isEditingSelector && isActionPanelVisible,
    [isEditingSelector, isActionPanelVisible]
  )

  // FIXME: this keeps re-generate the handler while scaling
  // FIXME: consider create the setter atom to update the scale
  const handleWheel = useCallback(
    (e: WheelEvent) => {
      if (e.deltaY === 0) {
        return
      }
      if (!e.metaKey && !e.ctrlKey) {
        return
      }
      if (scale === lastAppliedScale.current) return
      e.stopPropagation()
      e.preventDefault()

      const scaleFactor = e.deltaY < 0 ? TIMELINE_ZOOM_SCALE_MULTIPLIER : 1 / TIMELINE_ZOOM_SCALE_MULTIPLIER
      const newScale = boundedScale(scale * scaleFactor)

      // Compute the new scroll position after zooming
      const newScrollLeft = computeScrollLeftAfterZoom(
        e.clientX,
        scale,
        newScale,
        duration,
        timelineWidth,
        rulerRef.current?.scrollLeft || 0
      )

      setScale(newScale)

      lastAppliedScale.current = newScale === scale ? null : scale

      // Update scroll positions
      if (rulerRef.current) rulerRef.current.scrollLeft = newScrollLeft
      if (trackKeyframeRef.current) trackKeyframeRef.current.scrollLeft = newScrollLeft
    },
    [scale, boundedScale, duration, timelineWidth, setScale]
  )

  useEffect(() => {
    const trackKeyframeNode = trackKeyframeRef.current
    const rulerNode = rulerRef.current
    trackKeyframeNode?.addEventListener('wheel', handleWheel, { passive: false })
    rulerNode?.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      trackKeyframeNode?.removeEventListener('wheel', handleWheel)
      rulerNode?.removeEventListener('wheel', handleWheel)
    }
  }, [shouldShowActionPanel, trackKeyframeRef, rulerRef, handleWheel])

  useEffect(() => {
    subscribeToEffects()
  }, [duration, isActionPanelVisible, subscribeToEffects])

  useEffect(() => {
    const node = ref.current
    const handleEnter = () => {
      node?.classList.add('active')
    }
    const handleLeave = () => {
      node?.classList.remove('active')
    }
    node?.addEventListener('mouseenter', handleEnter)
    node?.addEventListener('mouseleave', handleLeave)
    return () => {
      node?.removeEventListener('mouseenter', handleEnter)
      node?.removeEventListener('mouseleave', handleLeave)
    }
  }, [ref])

  if (!response) {
    return null
  }

  return (
    <div
      ref={ref}
      data-test-id="mode-wrapper"
      className="relative grid grid-rows-[40px,minmax(0,1fr)] bg-neutral-90 pt-action-panel js-action-panel border-t border-neutral-80 z-20 box-content"
      style={{
        gridArea: FILE_EDITOR_LAYOUT_AREAS.ACTION_PANEL,
        gridTemplateAreas: "'controls ruler easing' 'track keyframe easing'",
        gridTemplateColumns: `${TIMELINE_HEADER_WIDTH_PX}px minmax(0, 1fr) ${EASING_PANEL_WIDTH_PX}px`,
        height: shouldShowActionPanel ? actionHeight : 0,
        boxShadow: '0px -8px 16px 0px #00000033',
        willChange: 'height'
      }}
    >
      {shouldShowActionPanel && (
        <>
          <ActionPanelHeader playHeadRef={playHeadRef} responseId={id} />
          <TrackInfoList
            // @ts-ignore TODO: fix after refactor of TrackInfoList
            trackList={trackList}
            trackMap={response.elementTrackMap}
            cacheRef={cacheRef}
            ref={trackInfoRef}
            kfRef={trackKeyframeRef}
          />
          <SubscribeSelectedKeyframeRange />
          <SubscribeInputSystemModifierKeys />
          <TrackKeyFrameList
            // @ts-ignore TODO: fix after refactor of TrackKeyFrameList
            trackList={trackList}
            trackMap={response.elementTrackMap}
            cacheRef={cacheRef}
            ref={trackKeyframeRef}
            rulerRef={rulerRef}
            infoRef={trackInfoRef}
          />
          <TimeRuler ref={rulerRef} trackRef={trackKeyframeRef} cacheRef={cacheRef} playHeadRef={playHeadRef} />

          {response.elementTrackMap.size === 0 ? <EmptyState /> : null}

          <div
            className="absolute w-full h-full z-20 border-r border-neutral-80 pointer-events-none"
            style={{ gridArea: 'controls / controls / track / track' }}
          />
          <div className="relative z-20 border-l border-neutral-80 bg-neutral-90" style={{ gridArea: 'easing' }}>
            <EasingPanel keyframeIdList={selectedKeyframeIdList} />
          </div>
        </>
      )}
    </div>
  )
}

type ActionProps = {
  responseId: string
}

const Action = ({ responseId }: ActionProps) => {
  const [disableScrollIntoView, setDisableScrollIntoView] = useState(false)

  if (!responseId) {
    return null
  }

  return (
    <ScrollContext.Provider value={{ disableScrollIntoView }}>
      <ScrollActionContext.Provider value={{ setDisableScrollIntoView }}>
        <Response id={responseId} />
      </ScrollActionContext.Provider>
    </ScrollContext.Provider>
  )
}

const Interaction = () => {
  const { editOrigin } = useUI()
  const { cancelEditingSelector, confirmSelectorChange } = useUIActions()
  const { actionHeight } = useLayout()

  const isActionPanelVisible = useAtomValue(undoableActionPanelVisibleAtom)

  const selectedActionId = useAtomValue(selectedActionIdAtom)
  const selectedResponseId = useAtomValue(selectedResponseIdAtom)

  const isEditingSelector = useAtomValue(isEditingSelectorAtom)
  const isActionMode = useAtomValue(isActionModeAtom)
  const isEditingState = useAtomValue(isEditingStateAtom)

  const actionBarVisibility = isEditingSelector || editOrigin
  return (
    <>
      <div
        className="absolute left-1/2 text-white text-center z-10 js-action-bar"
        style={{
          bottom: (isActionMode && (isActionPanelVisible ? actionHeight : 0)) + 16,
          opacity: actionBarVisibility ? 0 : 1,
          transform: actionBarVisibility ? 'translateX(-50%) translateY(20px)' : 'translateX(-50%) translateY(0)',
          transition: 'opacity 400ms ease-out, transform 400ms ease-out, bottom 400ms ease-out',
          pointerEvents: actionBarVisibility ? 'none' : 'auto',
          willChange: 'opacity, transform, bottom'
        }}
      >
        {isEditingState && <ActionBar type={ACTION_BAR_TYPE.EDITOR} responseId={selectedResponseId} />}
      </div>
      {isEditingSelector && (
        <div className="absolute left-1/2 bottom-8 text-white text-center z-10 animate-slideUp">
          <SelectorBar onCancel={cancelEditingSelector} onConfirm={confirmSelectorChange} />
        </div>
      )}
      {selectedActionId && selectedResponseId && <Action responseId={selectedResponseId} />}
    </>
  )
}

export default Interaction
