import { useTranslation } from 'react-i18next'

import { divideBy100, formatAsPercentage, multiplyBy100 } from '../../../../utils/formatter'
import { percentageFormatValidator } from '../../../../utils/validator'
import { Icon } from '../../../shared'
import Property from '../Property'

const LineHeight = ({ disabled }: { disabled: boolean }) => {
  const { t } = useTranslation('file', { keyPrefix: 'property_editor.text_editor' })

  return (
    <Property
      tooltip={t('line_height_tooltip')}
      caption={<Icon interactive={false} name="LineHeight" />}
      field="lineHeight"
      data-test-id="line-height-input"
      toDisplay={multiplyBy100}
      toValue={divideBy100}
      formatter={formatAsPercentage}
      min={0}
      max={999}
      validator={percentageFormatValidator}
      disabled={disabled}
    />
  )
}

export default LineHeight
