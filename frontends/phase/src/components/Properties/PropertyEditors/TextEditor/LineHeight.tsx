import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { MIX_VALUE } from '@phase-software/data-utils'
import { useAtomValue } from 'jotai'
import { dataStoreAtom } from '../../../../atoms'

import useDataStoreActions from '../../../../hooks/dataStore/useDataStoreActions'
import { useEditor, useEditorActions } from '../../../../providers/dataStore/EditorProvider'
import { divideBy100, formatNumberWithUnit, multiplyBy100 } from '../../../../utils/formatter'
import { clamp, parseNumberWithUnit } from '../../../../utils/number'
import { percentageFormatValidator } from '../../../../utils/validator'
import { Icon, MixInput } from '../../../shared'

// regex to check if the value starts with 'a', 'au', 'aut, 'auto' and case-insensitive
const isValidAutoLineHeight = (val: string) => val.trim().length > 0 && /^\s*(a|au|aut|auto)\s*$/i.test(val);
const minLineHeight = 0
const maxLineHeight = 999

const LineHeight = ({ disabled }: { disabled: boolean }) => {
  const dataStore = useAtomValue(dataStoreAtom)
  const { t } = useTranslation('file', { keyPrefix: 'property_editor.text_editor' })
  const { setProperties } = useEditorActions()
  const lineHeight = useEditor((o) => o.lineHeight)
  const { commitUndo, debounceCommitUndo } = useDataStoreActions()
  const [actualLineHeight, setActualLineHeight] = useState('')

  const handleFormat = useCallback(
    (val: number | string) => {
      if (typeof val === 'string') {
        if (val === MIX_VALUE) {
          return val
        }
        const parsedNum = parseNumberWithUnit(val)
        if (parsedNum) {
          return formatNumberWithUnit(parsedNum, '%', 2)
        }
        if (isValidAutoLineHeight(val)) {
          return ''
        }
        if (lineHeight === -1) {
          return 'Auto'
        }
      }
      if (typeof val === 'number') {
        if (val >= 0) {
          return formatNumberWithUnit(val, '%', 2)
        }
        return 'Auto'
      }
      return val
    },
    [lineHeight]
  )

  const handleToDisplay = useCallback((val: number | string | typeof MIX_VALUE): any => {
    if (val === MIX_VALUE) {
      return val
    }
    if (typeof val === 'string') {
      if (val === 'Auto') {
        return ''
      }
    }
    if (typeof val === 'number') {
      if (val >= 0) {
        return multiplyBy100(val)
      }
      return ''
    }
    return val
  }, [])

  const handleChange = useCallback(
    (val: string | number) => {
      if (lineHeight === -1) {
        const autoHeight = dataStore.drawInfo?.getAutoHeightValue()
        setActualLineHeight(autoHeight + '%')
      } else {
        setActualLineHeight('')
      }
      if (typeof val === 'string') {
        const parsedNum = parseNumberWithUnit(val)
        if (parsedNum) {
          setProperties({ lineHeight: divideBy100(parsedNum) })
        }
        if (isValidAutoLineHeight(val)) {
          setProperties({ lineHeight: -1 })
        }
      } else if (typeof val === 'number') {
        const newValue = clamp(val, minLineHeight, maxLineHeight)
        setProperties({ lineHeight: divideBy100(newValue) })
      }
    },
    [lineHeight, setProperties]
  )

  const handleValidate = useCallback((val: string) => {
    if (isValidAutoLineHeight(val) || percentageFormatValidator(val) === '') {
      return ''
    }
    return 'Invalid'
  }, [])

  return (
    <div className={`grid grid-flow-col items-center`} style={{ gridTemplateColumns: 'auto auto' }}>
      <MixInput
        disabled={disabled}
        onChange={handleChange}
        onBlur={commitUndo}
        onStepChange={debounceCommitUndo}
        tooltip={t('line_height_tooltip')}
        type="text"
        value={lineHeight}
        placeholder={actualLineHeight}
        variant="normal"
        rightComponent={
          <div className={`min-w-16 text-12 text-center text-light-overlay-60 whitespace-nowrap`}>
            <Icon interactive={false} name="LineHeight" />
          </div>
        }
        toDisplay={handleToDisplay}
        formatter={handleFormat}
        validator={handleValidate}
        spinner
      />
    </div>
  )
}

export default LineHeight
