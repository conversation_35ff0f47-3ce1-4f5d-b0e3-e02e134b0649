import React, { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useHref } from 'react-router-dom'

import { IfFeatureEnabled } from '@growthbook/growthbook-react'

import { PROTOTYPE_MODE_PARAMETER_KEY, PROTOTYPE_MODE_PARAMETER_VALUE } from '../../constants'
import { DEFAULT_DIALOG_TRANSITION_TIME, ModalKey } from '../../constants/global'
import { FEATURE_KEYS } from '../../constants/growthbook-feature-keys'
import { FileFieldsFragment, ProjectFieldsFragment } from '../../generated/graphql'
import useFileActions from '../../hooks/useFileActions'
import useHeapAnalytics from '../../hooks/useHeapAnalytics'
import { useModalContext } from '../../providers/ModalContextProvider'
import { useSetNotification } from '../../providers/NotificationProvider'
import { useProfile } from '../../providers/ProfileProvider'
import { useWorkspaceContext } from '../../providers/WorkspaceContextProvider'
import { track } from '../../services/heapAnalytics'
import { isValidEmail } from '../../utils/validator'
import { Button, Dialog, Icon, LinkSwitcher, ScrollView, Select, TextArea } from '../shared'
import ListItemWithContent from '../shared/List/ListItemWithContent'
import { MenuOptionProps } from '../shared/Menu/Menu.types'
import { translateMenuOptions } from '../shared/Menu/utils'
import { TextAreaTagItemProps } from '../shared/TextArea'
import Tooltip from '../shared/Tooltip'
import BaseShareCollaboratorAction from './BaseShareCollaboratorAction'
import { PrototypeLinkQRCode } from './PrototypeLinkQRCode'
import {
  COLLABORATOR_PERMISSION_OPTIONS,
  DEFAULT_PERMISSION_OPTIONS_BY_TYPE,
  getShareTypeDisplayName
} from './shareHelper'
import { AllPermissionEnum, CollaboratorPermission, ShareDialogType } from './shareTypes'

export type BaseShareDialogProps = {
  collaboratorList: MenuOptionProps[]
  defaultPermission?: AllPermissionEnum
  isEditable?: boolean
  isPreviewLinkShareable?: boolean
  /**
   * The pathname that will be appended to the base URL to create the full URL to be copied.
   */
  linkPathname: string
  name: ProjectFieldsFragment['name'] | FileFieldsFragment['name']
  onClose: () => void
  onCollaboratorPermissionChange: (collaborator: MenuOptionProps, permission: CollaboratorPermission) => Promise<void>
  onDefaultPermissionChange?: (permission: AllPermissionEnum) => Promise<void>
  onInviteCollaborators: (
    invitedCollaborators: TextAreaTagItemProps[],
    permission: CollaboratorPermission
  ) => Promise<void>
  onRemoveCollaborator: (collaborator: MenuOptionProps) => Promise<void>
  onLeave?: (collaborator: MenuOptionProps) => Promise<void>
  onResendInvitation: (collaborator: MenuOptionProps) => Promise<void>
  onCancelInvitation: (collaborator: MenuOptionProps) => Promise<void>
  open: boolean
  teamMemberList?: MenuOptionProps[]
  type: ShareDialogType
  fileId?: string
  projectId: string
  inviteComponent: React.ReactNode
}

const BaseShareDialog = ({
  collaboratorList,
  defaultPermission,
  isEditable = false,
  isPreviewLinkShareable = false,
  linkPathname,
  name,
  onClose,
  onCollaboratorPermissionChange,
  onDefaultPermissionChange,
  onInviteCollaborators,
  onRemoveCollaborator,
  onLeave,
  onResendInvitation,
  onCancelInvitation,
  open,
  teamMemberList,
  type,
  fileId,
  projectId,
  inviteComponent: InviteComponent
}: BaseShareDialogProps) => {
  const { t } = useTranslation(['workspace', 'common'])
  const currentUser = useProfile()
  const [invitedCollaborators, setInvitedCollaborators] = useState<TextAreaTagItemProps[]>([])
  const [invitedPermission, setInvitedPermission] = useState(CollaboratorPermission.CAN_REVIEW)
  const [invitedCollaboratorsError, setInvitedCollaboratorsError] = useState('')
  const [isInviteLinkMode, setIsInviteLinkMode] = useState(false)
  const { space, teamName } = useHeapAnalytics()
  const { workspaceData } = useWorkspaceContext()

  const TRANSLATED_DEFAULT_PERMISSION_OPTIONS_BY_TYPE = useMemo(() => {
    if (type === 'personalFile') {
      return []
    } else {
      return translateMenuOptions(t, DEFAULT_PERMISSION_OPTIONS_BY_TYPE[type], { ns: 'workspace' })
    }
  }, [t, type])
  const isProjectType = type === 'project'
  const isPrototypeLinkShareable = type === 'personalFile'

  const checkIsCurrentUser = (email: MenuOptionProps['value']) => email === currentUser.email

  const existingCollaboratorList = useMemo(
    () => new Set(collaboratorList.map((item) => String(item.value).toLowerCase())),
    [collaboratorList]
  )

  const validateInvitedCollaborator = ({ value: email }: TextAreaTagItemProps) =>
    isValidEmail(email) && !checkIsCurrentUser(email) && !existingCollaboratorList.has(email)

  const checkInvitedCollaboratorsAllValid = () => {
    if (invitedCollaborators.length === 0) {
      setInvitedCollaboratorsError(t('please_enter_at_least_one_email'))
      return false
    }
    if (!invitedCollaborators.every(({ value }) => isValidEmail(value))) {
      setInvitedCollaboratorsError(t('some_emails_are_invalid'))
      return false
    }
    if (!invitedCollaborators.every(validateInvitedCollaborator)) {
      setInvitedCollaboratorsError(t('users_already_a_team_member_or_invited'))
      return false
    }
    setInvitedCollaboratorsError('')
    return true
  }

  const handleInviteCollaborators = async () => {
    if (!checkInvitedCollaboratorsAllValid()) return

    await onInviteCollaborators?.(invitedCollaborators, invitedPermission)

    setInvitedCollaborators([])

    const accessType = invitedPermission === CollaboratorPermission.CAN_EDIT ? 'edit' : 'view'

    const location = projectId === workspaceData.draftProjectId ? 'drafts' : 'project'
    if (fileId) {
      // track invitation sent to file by every user
      invitedCollaborators.forEach(() => {
        track('Invitation To File Sent', {
          space,
          location,
          teamName,
          projectId,
          fileId,
          accessType
        })
      })
    }

    if (isProjectType) {
      invitedCollaborators.forEach(() => {
        track('Invite To Project Sent', {
          space,
          location,
          teamName,
          projectId,
          accessType
        })
      })
    }
  }

  const filterTeamMember = (searchValue: string, teamMember: MenuOptionProps) => {
    if (checkIsCurrentUser(teamMember.value) || existingCollaboratorList.has(String(teamMember.value).toLowerCase())) {
      return false
    }
    return (
      teamMember.name?.toLowerCase().startsWith(searchValue.trim().toLowerCase()) ||
      String(teamMember.value).toLowerCase().startsWith(searchValue.trim().toLowerCase())
    )
  }

  const sortedCollaboratorList = useMemo(() => {
    const copyCollaboratorList = [...collaboratorList]
    copyCollaboratorList.sort((a, b) => b.isPending - a.isPending || b.isCreator - a.isCreator)
    return copyCollaboratorList
  }, [collaboratorList])

  const translatedOptions = useMemo(
    () => translateMenuOptions(t, COLLABORATOR_PERMISSION_OPTIONS, { ns: 'workspace' }),
    [t]
  )

  const onCloseInviteLinkMode = () => {
    onClose()
    setTimeout(() => {
      setIsInviteLinkMode(false)
    }, DEFAULT_DIALOG_TRANSITION_TIME)
  }

  const { openModal } = useModalContext()
  const handleOpenPrototypeLinkModal = isPrototypeLinkShareable
    ? (data: { prototypeLink: string }) => {
        openModal(ModalKey.PROTOTYPE_LINK_MODAL, {
          prototypeLink: data.prototypeLink
        })
        onClose()
      }
    : undefined

  const shouldShowCollaboratorList = type === 'project' || type === 'draftFile' || type === 'projectFile'

  return (
    <>
      {isInviteLinkMode ? (
        <Dialog
          title={t('invite_via_link')}
          onClose={onCloseInviteLinkMode}
          footer={<></>}
          onBack={() => setIsInviteLinkMode(false)}
          bodyClassName="!px-0"
          open={open}
          backable
          showProgressIndicator
        >
          {InviteComponent}
        </Dialog>
      ) : (
        <Dialog
          title={t('invite_collaborators')}
          onClose={onClose}
          footer={
            <DialogFooter
              linkPathname={linkPathname}
              isPreviewLinkShareable={isPreviewLinkShareable}
              isPrototypeLinkShareable={isPrototypeLinkShareable}
              fileId={fileId}
              projectId={projectId}
              onOpenPrototypeLinkModal={handleOpenPrototypeLinkModal}
            />
          }
          bodyClassName="!px-0"
          open={open}
          showProgressIndicator
        >
          {/* @ts-ignore TODO: fix after refactor of ScrollView */}
          <ScrollView className="h-[475px]">
            <div className="pb-12 px-16 flex items-start gap-8">
              <TextArea
                disabled={!isEditable}
                placeholder={teamMemberList ? t('enter_user_name_or_email') : t('enter_user_email')}
                allowTags
                defaultTags={invitedCollaborators}
                error={invitedCollaboratorsError}
                onChangeTags={setInvitedCollaborators}
                customTagValidator={validateInvitedCollaborator}
                showSearch
                filterOption={teamMemberList ? filterTeamMember : undefined}
                searchMenuOptions={teamMemberList}
                errorTextClassName="basis-full"
                rightComponent={
                  <Select
                    // @ts-ignore TODO: fix after refactor of Select
                    focusOnSelect={false}
                    disabled={!isEditable}
                    value={invitedPermission}
                    onChange={setInvitedPermission}
                    options={translatedOptions}
                    size="l"
                    dataTestId="file-share-permission-select"
                  />
                }
              />
              <Button
                disabled={!isEditable}
                size="l"
                onClick={handleInviteCollaborators}
                data-test-id="invite-btn"
                className="flex-shrink-0"
              >
                {t('common:invite')}
              </Button>
            </div>
            {isEditable && (
              <IfFeatureEnabled feature={FEATURE_KEYS.INVITE_LINK}>
                <div className="px-16">
                  <LinkSwitcher
                    onClick={() => setIsInviteLinkMode(true)}
                    className="flex items-center gap-4 text-primary-40"
                  >
                    <Icon name="AddUser" interactive={false} useCurrentColor />
                    {t('invite_via_link')}
                  </LinkSwitcher>
                </div>
              </IfFeatureEnabled>
            )}

            {shouldShowCollaboratorList && (
              <div className="h-56 w-full px-16 flex items-center gap-8 text-white">
                <div className="w-full flex justify-between items-center">
                  <div className="flex items-center gap-8">
                    <Icon
                      name="Global"
                      size="xxxl"
                      className="flex justify-center items-center text-light-overlay-60"
                      interactive={false}
                      useCurrentColor
                    />
                    <div className="whitespace-nowrap">
                      {t('all_collaborators', { type: t(getShareTypeDisplayName(type)).toLowerCase() })}
                    </div>
                  </div>
                  <div>
                    <Select
                      // @ts-ignore TODO: fix after refactor of Select
                      value={defaultPermission}
                      options={TRANSLATED_DEFAULT_PERMISSION_OPTIONS_BY_TYPE}
                      onChange={onDefaultPermissionChange}
                      className="max-w-min"
                      disabled={!isEditable}
                    />
                  </div>
                </div>
              </div>
            )}

            {sortedCollaboratorList.map((collaborator) => (
              <ListItemWithContent
                key={collaborator.id}
                size="l"
                title={collaborator.name}
                description={collaborator.isPending ? t('pending') : collaborator.value}
                avatarAlt={collaborator.name || ''}
                avatarSrc={collaborator.avatarImage || ''}
                tag={checkIsCurrentUser(collaborator.value) ? t('common:you') : ''}
                after={
                  <BaseShareCollaboratorAction
                    type={type}
                    name={name}
                    collaborator={collaborator}
                    onPermissionChange={onCollaboratorPermissionChange}
                    onResendInvitation={onResendInvitation}
                    onCancelInvitation={onCancelInvitation}
                    onRemove={onRemoveCollaborator}
                    onLeave={onLeave}
                    isCurrentUser={checkIsCurrentUser(collaborator.value)}
                    isEditable={isEditable}
                    fileId={fileId}
                    projectId={projectId}
                    closeBaseShareDialog={onClose}
                  />
                }
              />
            ))}
          </ScrollView>
        </Dialog>
      )}
      {isPrototypeLinkShareable && <PrototypeLinkQRCode />}
    </>
  )
}

export default BaseShareDialog

const DialogFooter = ({
  linkPathname,
  isPreviewLinkShareable,
  isPrototypeLinkShareable,
  fileId,
  projectId,
  onOpenPrototypeLinkModal
}: {
  linkPathname: BaseShareDialogProps['linkPathname']
  isPreviewLinkShareable: boolean
  isPrototypeLinkShareable: boolean
  fileId?: string
  projectId?: string
  onOpenPrototypeLinkModal?: (data: { prototypeLink: string }) => void
}) => {
  const { t } = useTranslation(['workspace', 'common'])
  const { addNotification } = useSetNotification()
  const { createFilePreviewLink } = useFileActions()

  const copyURL = useHref(linkPathname)

  const copyLinkToClipboard = () => {
    const fullUrl = new URL(copyURL, window.location.origin)
    navigator.clipboard.writeText(fullUrl.toString())

    addNotification({
      type: 'success',
      content: t('common:collaborator_link_copied')
    })
  }

  const copyPrototypeLinkToClipboard = isPrototypeLinkShareable
    ? () => {
        const fullUrl = new URL(copyURL, window.location.origin)
        fullUrl.searchParams.set(PROTOTYPE_MODE_PARAMETER_KEY, PROTOTYPE_MODE_PARAMETER_VALUE)
        navigator.clipboard.writeText(fullUrl.toString())

        addNotification({
          type: 'success',
          content: t('common:prototype_link_copied'),
          action: t('common:view_in_mobile'),
          callback: () => {
            onOpenPrototypeLinkModal?.({
              prototypeLink: fullUrl.toString()
            })
          }
        })
      }
    : () => null

  const copyPreviewLinkToClipboard = async () => {
    try {
      const res = await createFilePreviewLink({ fileId, projectId })

      if (!res) {
        throw new Error('Failed to create preview link')
      }

      navigator.clipboard.writeText(window.location.origin + '/preview/' + res.id)

      addNotification({
        type: 'success',
        content: t('common:public_link_copied')
      })
    } catch (error) {
      console.error('Error copying preview link:', error)
    }
  }

  return (
    <div className="flex gap-12 flex-col p-16 border-t border-solid border-neutral-60 text-white ">
      <div className="flex gap-4">
        <LinkSwitcher onClick={copyLinkToClipboard} className="flex items-center gap-4">
          <Icon name="Link" interactive={false} useCurrentColor />
          {t('workspace:copy_collaborator_link')}
        </LinkSwitcher>
        <div>
          <Tooltip content={t('workspace:copy_collaborator_link_tooltip')}>
            <Icon name="Help" interactive={false} useCurrentColor />
          </Tooltip>
        </div>
      </div>
      {isPrototypeLinkShareable && (
        <div className="flex gap-4">
          <LinkSwitcher onClick={copyPrototypeLinkToClipboard} className="flex items-center gap-4">
            <Icon name="PlayOutline" interactive={false} useCurrentColor />
            {t('workspace:copy_prototype_link')}
          </LinkSwitcher>
          <div>
            <Tooltip content={t('workspace:copy_prototype_link_tooltip')}>
              <Icon name="Help" interactive={false} useCurrentColor />
            </Tooltip>
          </div>
        </div>
      )}
      {isPreviewLinkShareable && (
        <div className="flex gap-4">
          <LinkSwitcher onClick={copyPreviewLinkToClipboard} className="flex items-center gap-4">
            <Icon name="Remix" interactive={false} useCurrentColor />
            {t('workspace:copy_public_link')}
          </LinkSwitcher>
          <div>
            <Tooltip content={t('workspace:copy_public_link_tooltip')}>
              <Icon name="Help" interactive={false} useCurrentColor />
            </Tooltip>
          </div>
        </div>
      )}
    </div>
  )
}
