import { useTranslation } from 'react-i18next'

import { Modal<PERSON>ey } from '../../constants'
import { useModalContext } from '../../providers/ModalContextProvider'
import { useSetNotification } from '../../providers/NotificationProvider'
import { Button, Dialog, Icon } from '../shared'
import PhaseLogo from '../shared/Icon/svgs/Phase.svg'
import { QRCode } from './QRCode'

export const PrototypeLinkQRCode = () => {
  const { getModal, closeModal } = useModalContext()

  const {
    open,
    data: { prototypeLink }
  } = getModal(ModalKey.PROTOTYPE_LINK_MODAL)

  const handleClose = () => {
    closeModal(ModalKey.PROTOTYPE_LINK_MODAL)
  }

  const { t } = useTranslation(['common', 'workspace'])
  const { addNotification } = useSetNotification()
  const handleCopyLink = () => {
    navigator.clipboard.writeText(prototypeLink)

    addNotification({
      type: 'success',
      content: t('common:prototype_link_copied')
    })
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      size="s"
      title={t('workspace:share_to_mobile_devices')}
      className="max-w-[320px]"
      footer={
        <div className="p-16">
          <Button fluid size="l" color="secondary" onClick={handleCopyLink} data-test-id="copy-prototype-link-btn">
            <Icon name="Copy" className="mr-4" size={16} useCurrentColor />
            {t('workspace:copy_prototype_link')}
          </Button>
        </div>
      }
    >
      <div className="text-light-overlay-60 text-[12px] leading-4 font-medium mb-12 text-left font-inter tracking-[0.005em]">
        {t('workspace:scan_the_qr_code')}
      </div>
      <div className="w-full aspect-h-1 aspect-w-1 bg-white rounded-xl flex items-center justify-center overflow-hidden">
        <QRCode
          value={prototypeLink}
          size={288}
          logo={PhaseLogo}
          imageSize={0.9} // With the current URL length, this seems to match the corner square sizes better
          className="ml-auto mr-auto mt-auto mb-auto"
        />
      </div>
    </Dialog>
  )
}
