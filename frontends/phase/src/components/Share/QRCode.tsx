import { useEffect, useRef, useState } from 'react'

import QRCodeStyling, { Options } from 'qr-code-styling'

interface QRCodeProps {
  value: string
  logo?: string
  size: number
  className?: string
  imageSize?: number
}

export const QRCode = ({ value, logo, size, className, imageSize = 1 }: QRCodeProps) => {
  const [options] = useState<Options>({
    width: size,
    height: size,
    data: value,
    image: logo,
    imageOptions: logo
      ? {
          hideBackgroundDots: true,
          imageSize,
          crossOrigin: 'anonymous',
          margin: 4
        }
      : undefined,
    backgroundOptions: {
      color: '#fff'
    },
    dotsOptions: {
      color: '#000',
      type: 'square'
    },
    qrOptions: {
      errorCorrectionLevel: 'L' // This makes the corner squares larger; if we ever think we sometime want higher recovery, we can expose this as a prop
    }
  })

  const [qrCode] = useState(() => new QRCodeStyling(options))
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (ref.current) {
      qrCode.append(ref.current)
    }
  }, [qrCode])

  return <div className={className} ref={ref} data-id="qr-code" style={{ width: size, height: size }} />
}
