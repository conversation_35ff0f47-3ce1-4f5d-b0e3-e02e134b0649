import { KeyframeIndicatorType } from '@phase-software/types'

import DiamondIcon from './icons/explicit.svg?react'
import Highlighted from './icons/highlighted.svg?react'
import HalfDiamondIcon from './icons/initial.svg?react'
import EmptyDiamond from './icons/tween.svg?react'

type KeyframeProps = {
  isIndicator?: boolean
  selected?: boolean
  highlighted?: boolean
  shouldScaleOnHover?: boolean
  type?: KeyframeIndicatorType
  className?: string
  disabled?: boolean
}

const InitialIcon = ({
  colorClassName,
  isIndicator,
  highlighted,
  className = '',
  ...props
}: {
  highlighted?: boolean
  colorClassName?: string
  isIndicator?: boolean
  className?: string
}) => {
  return (
    <div {...props} className={`${className}`}>
      <div className={`absolute inset-0 ${isIndicator ? 'text-white' : 'text-light-overlay-60'}`}>
        <HalfDiamondIcon />
      </div>
      <div className={`${colorClassName}`}>
        <DiamondIcon />
        <div className={`absolute inset-0 js-kf-highlight ${highlighted ? '' : 'hidden'}`}>
          <Highlighted />
        </div>
      </div>
    </div>
  )
}

const DiamondIconWrapper = ({
  highlighted,
  className = '',
  colorClassName = '',
  ...props
}: {
  highlighted?: boolean
  className?: string
  colorClassName?: string
}) => {
  return (
    <div {...props} className={`${className} ${colorClassName}`}>
      <DiamondIcon />
      <div className={`absolute inset-0 js-kf-highlight ${highlighted ? '' : 'hidden'}`}>
        <Highlighted />
      </div>
    </div>
  )
}

const EmptyDiamondIcon = ({
  className = '',
  colorClassName = '',
  // eslint-disable-next-line unused-imports/no-unused-vars
  highlighted,
  ...props
}: {
  className?: string
  colorClassName?: string
  highlighted?: boolean
}) => {
  return (
    <div {...props} className={`${className} ${colorClassName}`}>
      <EmptyDiamond />
    </div>
  )
}

const KEYFRAME_TYPE_MAP = {
  [KeyframeIndicatorType.EXPLICIT]: DiamondIconWrapper,
  [KeyframeIndicatorType.INITIAL]: InitialIcon,
  [KeyframeIndicatorType.TWEEN]: EmptyDiamondIcon,
  [KeyframeIndicatorType.NON_EDITABLE]: null
}

const Keyframe = ({
  type = KeyframeIndicatorType.NON_EDITABLE,
  selected = false,
  isIndicator = false,
  highlighted = false,
  shouldScaleOnHover = true,
  disabled = false,
  className = '',
  ...props
}: KeyframeProps) => {
  const IconComponent = KEYFRAME_TYPE_MAP[type]

  if (!IconComponent) return null

  const colorClassName = isIndicator
    ? 'text-light-overlay-60 active:text-light-overlay-40'
    : selected
      ? 'text-primary-40'
      : 'text-neutral-20'

  const scaleClassName = shouldScaleOnHover ? 'hover:scale-125' : 'scale-125'

  if (type === KeyframeIndicatorType.INITIAL) {
    return (
      <IconComponent
        className={`w-full transform cursor-pointer ${!disabled && scaleClassName} ${className}`}
        colorClassName={colorClassName}
        isIndicator={isIndicator}
      />
    )
  } else {
    return (
      <IconComponent
        {...props}
        className={`w-full transform cursor-pointer ${!disabled && scaleClassName} ${className}`}
        highlighted={highlighted}
        colorClassName={colorClassName}
      />
    )
  }
}

export default Keyframe
