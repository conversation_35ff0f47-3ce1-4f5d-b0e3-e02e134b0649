import { vi } from 'vitest'
import { getFontData } from '../Font'

test('getFontData', () => {
  const mockFont = {
    id: 'dummyId',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontSizeUnit: 'px',
    fontStyle: 'Regular',
    gets: vi.fn((...args) => {
      return args.reduce((acc, cur) => {
        acc[cur] = mockFont[cur]
        return acc
      }, {})
    })
  }
  const result = getFontData(mockFont)
  expect(result).toEqual({
    id: 'dummyId',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontSizeUnit: 'px',
    fontStyle: 'Regular'
  })
})
