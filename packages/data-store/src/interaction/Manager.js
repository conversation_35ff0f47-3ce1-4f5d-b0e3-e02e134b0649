import {
    IDType,
    Unit,
    PropComponentType,
    EntityType,
    EasingType,
    FrameType,
    TriggerType,
    LogicType,
    OperationType,
    InteractionEntityType,
    ChangeType,
    PaintType,
    EventFlag,
    EffectType,
    AnimationPresetType,
    SelectorType,
    ChangeEventName
} from '@phase-software/types'
import {
    arrEquals,
    isObj,
    parseSceneTreeChanges,
    Undoable,
    Change,
    NO_FIRE,
    id,
    isId,
    loadId,
    toRad,
} from '@phase-software/data-utils'
import { easePoints } from '@phase-software/transition-manager'
import { clamp } from '@phase-software/data-utils/src/commons'
import { LayerListKeyList, LayerTypeMapLayerListKey, LayerListKeySet, AnimationPresetTypeKeyMap } from '../../dist'
import {
    DEFAULT_DURATION_TIME,
    DEFAULT_MOTION_PATH_VALUE,
    MIN_DURATION_TIME,
    MAX_DURATION_TIME,
    GRADIENT_PAINT_SET,
    AVAILABLE_UNIT_CHANGE,
    ALWAYS_UPDATE_WITH_UNIT_CHANGE,
    UNIT_CHANGE_PROPS_MAP,
    SHOULD_UPDATE_ALL_KFS_WITH_UNIT_CHANGES,
    EFFECT_TYPE_NAME_MAP,
    EFFECT_TYPE_VALUE_MAP
} from '../constant'
import { canElementApplyEffect, generateLayerPropertyTrackKey, generatePropertyTrackKey } from '../utils'
import { getNextName, pick, getTriggerOptions, findIndexToInsert, isPaintPropKey, getTimeAtStep } from './utils'
import {
    childListMap,
    nonAnimatableKeySet,
    GENERAL_PROPERTY_GROUP_MAP,
    generalKeySet,
    generalChildKeySet,
    effectChildKeySet,
} from './constants'

/** @typedef {import('@phase-software/data-store/src/DataStore').DataStore} DataStore */

class Manager extends Undoable {
    constructor(dataStore, data = {}) {
        super(dataStore, ChangeType.ENTITY, ChangeEventName.INTERACTION_CHANGES)
        /** @type {DataStore} */
        this.dataStore = dataStore
        this._cacheKeyframeGroupByTime = new WeakMap()

        this.load(data)

        this.dataStore.workspace.on(ChangeEventName.SCENE_TREE_CHANGES, this._onSceneTreeChanges.bind(this))

        /**
         * Tracks timeline maximum times during drag operations to optimize performance.
         * - initialMaxTime: Captures the maximum time before a drag operation starts
         * - currentMaxTime: Tracks the current maximum time during an ongoing drag
         *
         * This prevents redundant recalculations of maximum times across all keyframes
         * and animation presets during drag operations.
         */
        this.timeTracker = {
            initialMaxTime: null,
            currentMaxTime: null,
        }
    }

    _flushCacheBeforeFire(changes) {
        changes.UPDATE.forEach((change, entityId) => {
            const entity = this.data.entityMap.get(entityId)
            if (!entity) {
                return
            }
            switch (entity.type) {
                case InteractionEntityType.PROPERTY_TRACK: {
                    if (change.has('keyFrameList')) {
                        this.flushKfByTime(entityId)
                    }
                    break
                }
                case InteractionEntityType.KEY_FRAME: {
                    if (change.has('time')) {
                        this.flushKfByTime(entityId)
                    }
                    break
                }
            }
        })
    }

    fire(undoable = true, options = {}) {
        super.fire(undoable, options)
        this.dataStore.library.fire(undoable)
    }

    emit(changes, options) {
        this._flushCacheBeforeFire(changes)
        super.emit(changes, options)
    }

    _debug() {
        if (process.env.NODE_ENV !== 'production') {
            const { entityMap } = this.save()
            const responseId = this._getCurrentResponseId()
            return Object.entries(entityMap[responseId].elementTrackMap).reduce((acc, [elementId, elementTrackId]) => {
                const elementTrack = entityMap[elementTrackId]
                elementTrack.id = elementTrackId
                elementTrack.propertyTrackMap = Object.entries(elementTrack.propertyTrackMap).reduce((acc, [trackKey, trackId]) => {
                    const propertyTrack = entityMap[trackId]
                    propertyTrack.id = trackId
                    propertyTrack.keyFrameList = propertyTrack.keyFrameList.map(keyFrameId => ({ id: keyFrameId, ...entityMap[keyFrameId] }))
                    acc[trackKey] = propertyTrack
                    return acc
                }, {})
                acc[elementId] = elementTrack
                return acc
            }, {})
        }
    }

    // TODO: add tests for this
    _onSceneTreeChanges(changes, options) {
        // don't do this if we're in undo or redo
        if (this.dataStore.inUndo || this.dataStore.inRedo) {
            return
        }
        const { removed, moved } = parseSceneTreeChanges(changes)


        for (const elTrackMap of this._getElementTrackMapsForAllActions()) {
            removed.forEach(elementId => {
                const root = this.dataStore.getById(elementId)
                for (const child of this.dataStore.traverseSubtree(root)) {
                    const childId = child.get('id')
                    const elementTrackId = elTrackMap.get(childId)
                    if (!elementTrackId || moved.has(childId)) {
                        continue
                    }

                    this._deleteElementTrack(elementTrackId)
                }
            })
        }

        this.fire(options.undoable)
    }

    /**
     * @yields {Map<string, ElementTrack>}
     */
    *_getElementTrackMapsForAllActions() {
        let actionRelation, responseId
        for (actionRelation of this.dataStore.elementInteractionManager.getAllActionList()) {
            for (responseId of this.getResponseList(actionRelation.actionId)) {
                yield this.getElementTrackMap(responseId)
            }
        }
    }

    _getTransitionTime() {
        return getTimeAtStep(this.dataStore.transition.currentTime)
    }

    _getCurrentActionId() {
        // console.warn('DEPRECATED: This method is deprecated, directly pass actionId instead')
        return this.dataStore.selection?.get('action')
    }

    _getCurrentResponseId() {
        // console.warn('DEPRECATED: This method is deprecated, directly pass responseId instead')
        const currentActionId = this._getCurrentActionId()
        const responseId = this.getFirstResponseIdByActionId(currentActionId)
        return responseId || null
    }

    _handleDelete(entity) {
        switch (entity.type) {
            // when undo the keyframe is deleted from undo(changes)
            case InteractionEntityType.KEY_FRAME: {
                const motionPoints = this.dataStore.selection.get('motionPoints')
                const deselectMotionPoint = motionPoints.find(point => point.key === entity.id)
                if (deselectMotionPoint) {
                    this.dataStore.selection.removeMotionPoints([deselectMotionPoint], null, { commit: false })
                }
                break
            }
        }
    }

    _delete(entityId) {
        const entity = this.data.entityMap.get(entityId)

        // in case of recursive delete may delete same entity twice
        if (entity) {
            this.deletedMap.set(entity.id, entity)
            this.data.entityMap.delete(entity.id)
            this._handleDelete(entity)
        }
    }

    _restore(entityId) {
        const entity = this.deletedMap.get(entityId)
        this.data.entityMap.set(entity.id, entity)
        this.deletedMap.delete(entity.id)
    }

    /**
     * @param {object} entity
     * @returns {Action | Trigger | Response | Condition | ElementTrack | PropertyTrack | KeyFrame | AnimationPreset}
     */
    _load(entity) {
        if (entity.id) {
            loadId(entity.id, IDType.INTERACTION_MANAGER_COMPONENT)
        }

        switch (entity.type) {
            case InteractionEntityType.ACTION:
                return this._loadAction(entity)
            case InteractionEntityType.TRIGGER:
                return this._loadTrigger(entity)
            case InteractionEntityType.RESPONSE:
                return this._loadResponse(entity)
            case InteractionEntityType.CONDITION:
                return this._loadCondition(entity)
            case InteractionEntityType.ELEMENT_TRACK:
                return this._loadElementTrack(entity)
            case InteractionEntityType.PROPERTY_TRACK:
                return this._loadPropertyTrack(entity)
            case InteractionEntityType.KEY_FRAME:
                return this._loadKeyFrame(entity)
            case InteractionEntityType.ANIMATION_PRESET:
                return this._loadAnimationPreset(entity)
        }
    }

    /**
     * @param {object} data
     * @returns {Action}
     */
    _loadAction(data) {
        return pick(data, [
            'id',
            'type',
            'name',
            'responseList',
            'triggerList',
        ])
    }

    /**
     * @param {object} data
     * @returns {Trigger}
     */
    _loadTrigger(data) {
        return pick(data, [
            'id',
            'type',
            'actionId',
            'elementId',
            'triggerType',
            'options',
            'selector'
        ])
    }

    /**
     * @param {object} data
     * @returns {Response}
     */
    _loadResponse(data) {
        /** @type {Response} */
        const response = pick(data, [
            'id',
            'type',
            'actionId',
            'name',
            'duration',
            'looping',
            'speed',
            'conditionList',
            'elementTrackMap'
        ])
        response.elementTrackMap = new Map(Object.entries(response.elementTrackMap))
        return response
    }

    /**
     * @param {object} data
     * @returns {Condition}
     */
    _loadCondition(data) {
        return pick(data, [
            'id',
            'type',
            'responseId',
            'logic',
            'operation',
            'elementId'
        ])
    }

    /**
     * @param {object} data
     * @returns {ElementTrack}
     */
    _loadElementTrack(data) {
        /** @type {ElementTrack} */
        const elementTrack = pick(data, [
            'id',
            'type',
            'responseId',
            'elementId',
            'propertyTrackMap',
            ...LayerListKeyList,
            'presets',
        ])
        elementTrack.propertyTrackMap = new Map(Object.entries(elementTrack.propertyTrackMap))

        // FIXME: (animation-preset) not fallback here, should write a migration script
        if (!elementTrack.presets) {
            elementTrack.presets = []
        }
        return elementTrack
    }

    /**
     * @param {object} data
     * @returns {PropertyTrack}
     */
    _loadPropertyTrack(data) {
        /** @type {PropertyTrack} */
        const propertyTrack = pick(data, [
            'id',
            'type',
            'key',
            'elementTrackId',
            'parentId',
            'children',
            'keyFrameList'
        ])
        propertyTrack.children = new Set(propertyTrack.children)
        return propertyTrack
    }

    /**
     * @param {object} data
     * @returns {KeyFrame}
     */
    _loadKeyFrame(data) {
        // TODO: make these arrays to be a constant
        return pick(data, [
            'id',
            'type',
            'frameType',
            'trackId',
            'easingType',
            'animatable',
            'bezier',
            'steps',
            'time',
            'delta',
            'value',
            'ref',
            'unit'
        ])
    }

    /**
     * @param {object} data
     * @returns {AnimationPreset}
     */
    _loadAnimationPreset(data) {
        /** @type {AnimationPreset} */
        const preset = pick(data, [
            'id',
            'type',
            'elementTrackId',
            'key',
            'easingType',
            'bezier',
            'presetType',
            'startTime',
            'duration',
            'delay',
        ])

        switch (preset.presetType) {
            case AnimationPresetType.FADE_IN:
            case AnimationPresetType.FADE_OUT:
                preset.opacity = data.opacity
                preset.opacityTo = data.opacityTo
                break
            case AnimationPresetType.MOVE_IN:
            case AnimationPresetType.MOVE_OUT:
                preset.opacity = data.opacity
                preset.direction = data.direction
                preset.distance = data.distance
                preset.opacityTo = data.opacityTo
                preset.motionPath = data.motionPath
                preset.motionPathTo = data.motionPathTo
                break
            case AnimationPresetType.SCALE_IN:
            case AnimationPresetType.SCALE_OUT:
                preset.opacity = data.opacity
                preset.scaleX = data.scaleX
                preset.scaleY = data.scaleY
                preset.opacityTo = data.opacityTo
                preset.scaleXTo = data.scaleXTo
                preset.scaleYTo = data.scaleYTo
                break
            case AnimationPresetType.SPIN_IN:
            case AnimationPresetType.SPIN_OUT:
                preset.opacity = data.opacity
                preset.direction = data.direction
                preset.scaleX = data.scaleX
                preset.scaleY = data.scaleY
                preset.rotation = data.rotation
                preset.rotationTo = data.rotationTo
                preset.opacityTo = data.opacityTo
                preset.scaleXTo = data.scaleXTo
                preset.scaleYTo = data.scaleYTo
                break
            case AnimationPresetType.TRIM_IN:
            case AnimationPresetType.TRIM_OUT:
                preset.mode = data.mode
                preset.direction = data.direction
                preset.start = data.start
                preset.end = data.end
                break
        }
        return preset
    }

    _watchElement(element) {
        if (this.watchSet.has(element.get('id'))) {
            return
        }
        this.watchSet.add(element.get('id'))
    }

    _unwatchElement(element) {
        if (!this.watchSet.has(element.get('id'))) {
            return
        }
        this.watchSet.delete(element.get('id'))
    }

    load(data = {}) {
        this.deletedMap = new Map()
        this.watchSet = new Set()

        this.data = {
            id: data.id || id(),
            entityMap: this.parseEntityData(data.entityMap)
        }
    }

    parseEntityData(entityMapData = {}) {
        /** @type {Map<string, Action | Trigger | Response | Condition | ElementTrack | PropertyTrack | KeyFrame | AnimationPreset>} */
        const entityMap = new Map()
        Object.entries(entityMapData).forEach(([id, entityData]) => {
            // restore id inside the data; id not stored in entity object to save space
            const entity = this._load({ id, ...entityData })
            if (entity) {
                entityMap.set(id, entity)
            }
        })
        return entityMap
    }

    saveAction(actionId, clipboardContent) {
        const action = this.getAction(actionId)

        if (!action) {
            return clipboardContent
        }

        const data = this._saveEntity(action)
        clipboardContent.interaction.set(action.id, data)

        action.responseList.forEach((responseId) => {
            this.saveResponse(responseId, clipboardContent)
        })

        action.triggerList.forEach((triggerId) => {
            this.saveTrigger(triggerId, clipboardContent)
        })

        return clipboardContent
    }

    saveTrigger(triggerId, clipboardContent) {
        const trigger = this.getTrigger(triggerId)

        if (!trigger) {
            return clipboardContent
        }

        const data = this._saveEntity(trigger)
        clipboardContent.interaction.set(trigger.id, data)

        return clipboardContent
    }

    saveResponse(responseId, clipboardContent) {
        const response = this.getResponse(responseId)

        if (!response) {
            return clipboardContent
        }

        const data = this._saveEntity(response)
        clipboardContent.interaction.set(response.id, data)

        response.elementTrackMap.forEach((elementTrackId) => {
            this.saveElementTrack(elementTrackId, clipboardContent)
        })

        return clipboardContent
    }

    /**
     * Saves element track data to clipboard
     * @param      {string}  elementTrackId   The element track identifier
     * @param      {object}  clipboardContent  The clipboard content
     * @returns    {object}  updated clipboard content
     */
    saveElementTrack(elementTrackId, clipboardContent) {
        const elementTrack = this.getElementTrack(elementTrackId)

        if (!elementTrack) {
            return clipboardContent
        }

        const data = this._saveEntity(elementTrack)
        clipboardContent.interaction.set(elementTrack.id, data)

        elementTrack.propertyTrackMap.forEach((propertyTrackId) => {
            this.savePropertyTrack(propertyTrackId, clipboardContent)
        })

        return clipboardContent
    }

    /**
     * Saves property track data to clipboard
     * @param      {string}  propertyTrackId   The property track identifier
     * @param      {object}  clipboardContent  The clipboard content
     * @returns    {object}  updated clipboard content
     */
    savePropertyTrack(propertyTrackId, clipboardContent) {
        const propertyTrack = this.getPropertyTrack(propertyTrackId)

        if (!propertyTrack) {
            return clipboardContent
        }

        const data = this._saveEntity(propertyTrack)
        clipboardContent.interaction.set(propertyTrack.id, data)

        propertyTrack.keyFrameList.forEach((keyFrameId) => {
            this.saveKeyFrame(keyFrameId, clipboardContent)
        })

        return clipboardContent
    }

    /**
     * Saves keyframe data to clipboard
     * @param      {string}  keyFrameId        The key frame identifier
     * @param      {object}  clipboardContent  The clipboard content
     * @returns    {object}  updated clipboard content
     */
    saveKeyFrame(keyFrameId, clipboardContent) {
        const keyFrame = this.getKeyFrame(keyFrameId)

        if (!keyFrame) {
            return clipboardContent
        }

        const data = this._saveEntity(keyFrame)
        clipboardContent.interaction.set(keyFrame.id, data)

        if (keyFrame.ref) {
            const data = this.dataStore.library.getComponent(keyFrame.ref).save()
            clipboardContent.library.set(keyFrame.id, data)
            clipboardContent.mapKeyFrameToLibrary[keyFrame.id] = data.id
        }

        return clipboardContent
    }

    updateKeyFrameData(kfId, newValue, fire = true) {
        this._updateKeyFrameData(kfId, { value: newValue })
        if (fire) {
            this.fire()
        }
    }

    _saveEntityMap() {
        const map = {}
        this.data.entityMap.forEach((entity, key) => {
            const data = this._saveEntity(entity)
            map[key] = data
        })
        return map
    }

    _saveEntity(entity) {
        const obj = Object.entries(entity).reduce((acc, [prop, value]) => {
            if (value instanceof Map) {
                acc[prop] = Object.fromEntries(value)
            } else if (value instanceof Set) {
                acc[prop] = Array.from(value)
            } else if (Array.isArray(value)) {
                acc[prop] = value.slice()
            } else {
                acc[prop] = value
            }
            return acc
        }, {})

        return obj
    }

    save() {
        return {
            // keep this for backward capability
            actionList: [],
            entityMap: this._saveEntityMap()
        }
    }

    clear() {
        this.data.entityMap.clear()
    }

    undo(changes) {
        changes.DELETE.forEach(entityId => {
            this._restore(entityId)
        })
        changes.UPDATE.forEach((change, entityId) => {
            const entity = this.data.entityMap.get(entityId)
            // entity may be deleted by other tabs can't find it
            if (entity) {
                change.forEach(({ before }, key) => {
                    entity[key] = isObj(before) ? { ...before } : before
                })
            }
        })
        changes.CREATE.forEach(entityId => {
            this._delete(entityId)
        })

        super.undo(changes)
    }

    redo(changes, options) {
        changes.CREATE.forEach(entityId => {
            this._restore(entityId)
        })

        changes.UPDATE.forEach((change, entityId) => {
            const entity = this.data.entityMap.get(entityId)
            if (entity) {
                change.forEach(({ after }, key) => {
                    entity[key] = isObj(after) ? { ...after } : after
                })
            }
        })

        changes.DELETE.forEach(entityId => {
            this._delete(entityId)
        })

        super.redo(changes, options)
    }

    _reorderList(list, fromIndex, toIndex) {
        if (
            fromIndex === toIndex ||
            fromIndex < 0 ||
            fromIndex >= list.length ||
            toIndex < 0 ||
            toIndex >= list.length
        ) {
            return false
        }
        list.splice(toIndex, 0, list.splice(fromIndex, 1)[0])
        return true
    }

    _createAction() {
        /** @type {Action} */
        const action = {
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.ACTION,
            name: '',
            responseList: [],
            triggerList: []
        }
        this.data.entityMap.set(action.id, action)
        this.changes.create([action.id])
        return action
    }

    _createResponse(actionId) {
        const nameList = this.getResponseList(actionId).map(
            actionId => this.getResponse(actionId).name
        )
        const name = getNextName('Do', nameList)
        /** @type {Response} */
        const response = {
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.RESPONSE,
            actionId,
            name,
            looping: false,
            speed: 1,
            duration: DEFAULT_DURATION_TIME,
            conditionList: [],
            elementTrackMap: new Map()
        }
        this.data.entityMap.set(response.id, response)
        this.changes.create([response.id])
        return response
    }

    _createTrigger(actionId) {
        const targetElementId = this.getElementIdByActionId(actionId)

        /** @type {Trigger} */
        const trigger = {
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.TRIGGER,
            actionId,
            triggerType: TriggerType.CLICK,
            options: getTriggerOptions(TriggerType.CLICK),
            selector: {
                selectorType: SelectorType.ELEMENT,
                value: targetElementId
            }
        }
        this.data.entityMap.set(trigger.id, trigger)
        this.changes.create([trigger.id])
        return trigger
    }

    _createElementTrack(responseId, elementId) {
        /** @type {ElementTrack} */
        const elementTrack = {
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.ELEMENT_TRACK,
            responseId,
            elementId,
            propertyTrackMap: new Map(),
            fills: [],
            strokes: [],
            shadows: [],
            innerShadows: [],
            presets: [],
        }
        this.data.entityMap.set(elementTrack.id, elementTrack)
        this.changes.create([elementTrack.id])
        return elementTrack
    }

    _createPropertyTrack(elementTrackId, key, prefix) {
        const trackId = id(IDType.INTERACTION_MANAGER_COMPONENT)
        /** @type {PropertyTrack} */
        const propertyTrack = {
            id: trackId,
            key: `${prefix ? `${prefix}.` : ''}${key ? key : trackId}`,
            type: InteractionEntityType.PROPERTY_TRACK,
            elementTrackId,
            parentId: null,
            children: new Set(),
            keyFrameList: []
        }
        this.data.entityMap.set(propertyTrack.id, propertyTrack)
        this.changes.create([propertyTrack.id])
        return propertyTrack
    }

    _createKeyFrame(propertyTrackId, keyframeData = {}) {
        const defaultData = {
            steps: 1,
            animatable: true,
            unit: Unit.PIXEL,
            time: 0,
            value: undefined,
            ref: null,
            delta: false,
            frameType: FrameType.EXPLICIT
        }
        const data = { ...defaultData, ...keyframeData }

        const easingType = data.easingType || (data.animatable ? EasingType.LINEAR : EasingType.STEP_END)
        const bezier = data.bezier || easePoints[easingType]

        /** @type {KeyFrame} */
        const keyFrame = {
            ...data,
            easingType,
            bezier,
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.KEY_FRAME,
            trackId: propertyTrackId,
        }

        this.data.entityMap.set(keyFrame.id, keyFrame)
        this.changes.create([keyFrame.id])
        return keyFrame
    }

    _createAnimationPreset(elementTrack, presetName, presetData = {}) {
        const data = { delay: true, ...presetData }

        const easingType = presetName.toLowerCase().endsWith('in') ? EasingType.EASE_IN : EasingType.EASE_OUT
        const bezier = easePoints[easingType]

        /** @type {AnimationPreset} */
        const animationPreset = {
            ...data,
            easingType,
            bezier,
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            elementTrackId: elementTrack.id,
            key: presetName,
            type: InteractionEntityType.ANIMATION_PRESET,
        }

        this.data.entityMap.set(animationPreset.id, animationPreset)
        this.changes.create([animationPreset.id])
        return animationPreset
    }

    _createCondition(responseId) {
        /** @type {Condition} */
        const condition = {
            id: id(IDType.INTERACTION_MANAGER_COMPONENT),
            type: InteractionEntityType.CONDITION,
            responseId,
            logic: LogicType.AND,
            operation: OperationType.NONE,
            elementId: null
        }
        this.data.entityMap.set(condition.id, condition)
        this.changes.create([condition.id])
        return condition
    }

    /**
     * @param {Action} action
     * @param {Map<ID, ID>} elementIdMap
     * @param {Map<ID, Entity>} entityMap
     * @param {Map<ID, ID>} libraryMap
     * @returns {Action}
     */
     _cloneAction(action, elementIdMap, entityMap, libraryMap) {
        /** @type {Action} */
        const newAction = this._createAction()

        newAction.name = action.name

        const responseList = action.responseList.map(responseId => {
            const newResponse = this._cloneResponse(
                this.getResponse(responseId, entityMap),
                newAction.id,
                elementIdMap,
                entityMap,
                libraryMap
            )
            return newResponse.id
        })
        newAction.responseList = responseList

        const triggerList = action.triggerList.map(triggerId => {
            const newTrigger = this._cloneTrigger(
                this.getTrigger(triggerId, entityMap),
                newAction.id,
                elementIdMap

            )
            return newTrigger.id
        })
        newAction.triggerList = triggerList

        return newAction
    }

    /**
     * @param {import('@phase-software/types').ResponseData} response
     * @param {ID} actionId
     * @param {Map<ID, ID>} elementIdMap
     * @param {Map<ID, Entity>} entityMap
     * @param {Map<ID, ID>} libraryMap
     * @returns {Response}
     */
    _cloneResponse(response, actionId, elementIdMap, entityMap, libraryMap) {
        /** @type {ResponseData} */
        const newResponse = this._createResponse(actionId)

        const conditionList = response.conditionList.map(conditionId => {
            const condition = this.getCondition(conditionId, entityMap)
            const newCondition = this._cloneCondition(
                condition,
                newResponse.id,
                elementIdMap ? elementIdMap.get(condition.elementId) : null
            )

            return newCondition.id
        })
        newResponse.conditionList = conditionList

        const newElementTrackMap = new Map()
        response.elementTrackMap.forEach((elementTrackId, elementId) => {
            const newElementId = elementIdMap ? elementIdMap.get(elementId) : elementId
            const newElementTrack = this._cloneElementTrack(
                this.getElementTrack(elementTrackId, entityMap),
                newResponse.id,
                newElementId,
                entityMap,
                libraryMap
            )
            if (newElementTrack) {
                newElementTrackMap.set(newElementId, newElementTrack.id)
            }
        })
        newResponse.elementTrackMap = newElementTrackMap

        newResponse.looping = response.looping
        newResponse.speed = response.speed
        newResponse.duration = response.duration

        return newResponse
    }

    /**
     * @param {Trigger} trigger
     * @param {string} actionId
     * @param {Map<string, string>} elementIdMap
     * @returns {Trigger}
     */
    _cloneTrigger(trigger, actionId, elementIdMap) {
        /** @type {Trigger} */
        const newTrigger = this._createTrigger(actionId)

        newTrigger.triggerType = trigger.triggerType
        newTrigger.option = { ...trigger.option }
        newTrigger.elementId = elementIdMap ? elementIdMap.get(trigger.elementId) : trigger.elementId
        newTrigger.selector = { selectorType: trigger.selector.selectorType, value: elementIdMap ? elementIdMap.get(trigger.selector.value) : trigger.selector.value }

        return newTrigger
    }

    /**
     * @param {Condition} condition
     * @param {string} responseId
     * @param {string} newElementId
     * @returns {Condition}
     */
    _cloneCondition(condition, responseId, newElementId) {
        const newCondition = this._createCondition(responseId)

        newCondition.logic = condition.logic
        newCondition.operation = condition.operation
        newCondition.elementId = newElementId || condition.elementId

        return newCondition
    }

    /**
     * @param {ElementTrack} elementTrack
     * @param {string} responseId
     * @param {string} newElementId
     * @param {Map<string, ElementTrack>} entityMap
     * @param {Map<ID, ID>} libraryMap
     * @returns {ElementTrack}
     */
    _cloneElementTrack(elementTrack, responseId, newElementId, entityMap, libraryMap) {
        const elementId = newElementId || elementTrack.elementId
        const newElementTrack = this._createElementTrack(responseId, elementId)

        if (entityMap) {
            entityMap.set(newElementTrack.id, newElementTrack)
        }

        childListMap.get('ROOT').forEach(key => {
            const trackId = elementTrack.propertyTrackMap.get(key)
            if (!trackId) {
                return
            }

            const propTrack = this._clonePropertyTrack(
                this.getPropertyTrack(trackId, entityMap),
                newElementTrack.id,
                null,
                entityMap,
                libraryMap
            )

            newElementTrack.propertyTrackMap.set(propTrack.key, propTrack.id)
        })

        if (!newElementTrack.propertyTrackMap.size && !elementTrack.presets.length) {
            if (entityMap) {
                entityMap.delete(newElementTrack.id)
            }
            this.data.entityMap.delete(newElementTrack.id)
            this.changes.removeFromCreate([newElementTrack.id])
            return null
        }

        return newElementTrack
    }

    updateResponseDuration() {
        // Currently, adding/deleting keyframes or animation presets is handled within interaction change,
        // as it's more convenient to handle scenarios where both keyframes and animation presets are deleted simultaneously.
        // However, updating keyframe/animation preset timing within interaction change leads to performance issues,
        // so it's handled directly here in this Manager. This can be revisited in the future if performance concerns arise.

        const actionId = this._getCurrentActionId()
        const firstResponseId = this.getFirstResponseIdByActionId(actionId)
        const response = this.getResponse(firstResponseId)
        if (!response) return
        const responseId = response.id
        const maxKeyframeTime = this.getResponseMaxKeyframeTime(responseId)
        const maxAnimationPresetTime = this.getMaxAnimationPresetKeyframeTime(responseId)
        const currentDuration = this.getResponseDuration(responseId)
        const maxTime = Math.max(maxKeyframeTime, maxAnimationPresetTime)

        if(maxTime !== currentDuration && maxTime > 100) {
            this.setResponseDuration(responseId, maxTime, false, false)
        }
    }

    /**
     * Updates the response duration when dragging keyframes or animation presets
     * @param {number} time - The new time value to consider for duration update
     */
    updateResponseDurationWithCache(time) {
        const actionId = this._getCurrentActionId()
        const firstResponseId = this.getFirstResponseIdByActionId(actionId)
        const currentDuration = this.getResponseDuration(firstResponseId)


        const adjustedTime = this._getDurationForUpdate(time)
        if (this._shouldUpdateResponseDuration(adjustedTime, currentDuration)) {
            this.setResponseDuration(firstResponseId, adjustedTime, false, false)
        }
    }

    /**
     * @param {PropertyTrack} propertyTrack
     * @param {string} elementTrackId
     * @param {string} parentId
     * @param {Map<ID, PropertyTrack>} entityMap
     * @param {Map<ID, ID>} libraryMap
     * @returns {PropertyTrack}
     */
    _clonePropertyTrack(propertyTrack, elementTrackId, parentId = null, entityMap, libraryMap) {
        // prop track key rules:
        // generic key: 'x', 'y'
        // group key: 'position', 'dimensions'
        // layer prop key: `${cuid}.${prop}`
        // base layer key: cuid equal to layerId
        // non-base layer key: cuid equal to id
        const propKeys = propertyTrack.key.split('.')
        const isLayerPropKey = propKeys.length === 2
        const newElementTrack = this.getElementTrack(elementTrackId, entityMap)
        let prefix
        let key = isLayerPropKey ? propKeys[1] : propKeys[0]
        const isLayerKey = !generalKeySet.has(key) && (isId(key, IDType.LAYER) || isId(key, IDType.INTERACTION_MANAGER_COMPONENT))

        if (isLayerKey) {
            const isBaseLayer = propertyTrack.id !== propertyTrack.key
            if (isBaseLayer) {

                if(libraryMap) {
                    key = libraryMap.get(propertyTrack.key)
                } else{
                    const elementTrack = this.getElementTrack(propertyTrack.elementTrackId, entityMap)
                    const parentTrack = this.getPropertyTrack(parentId, entityMap)
                    const listName = parentTrack.key

                    const element = this.dataStore.getById(elementTrack.elementId)
                    const listComponentId = element.base[listName][0]
                    const listComponent = this.dataStore.library.getComponent(listComponentId)
                    const index = listComponent.layers.indexOf(propertyTrack.key)

                    const newElement = this.dataStore.getById(newElementTrack.elementId)
                    const newListComponentId = newElement.base[listName][0]
                    const newListComponent = this.dataStore.library.getComponent(newListComponentId)

                    key = newListComponent.layers[index]
                }
            } else {
                // make key as undefined to generate the new ID
                key = undefined
            }
        } else if (isLayerPropKey) {
            prefix = this.getPropertyTrack(parentId, entityMap).key
        } else {
            key = propKeys[0]
        }

        const newPropertyTrack = this._createPropertyTrack(elementTrackId, key, prefix)
        newPropertyTrack.parentId = parentId

        newElementTrack.propertyTrackMap.set(newPropertyTrack.key, newPropertyTrack.id)

        if (entityMap) {
            entityMap.set(newPropertyTrack.id, newPropertyTrack)
        }

        // non-base layer
        if (newPropertyTrack.id === newPropertyTrack.key) {
            const listName = this.getPropertyTrack(parentId).key
            newElementTrack[listName].push(newPropertyTrack.id)
        }

        const oriElementTrack = this.getElementTrack(propertyTrack.elementTrackId, entityMap)
        propertyTrack.children.forEach(trackKey => {
            const subId = oriElementTrack.propertyTrackMap.get(trackKey)
            const subTrack = this._clonePropertyTrack(
                this.getPropertyTrack(subId, entityMap),
                elementTrackId,
                newPropertyTrack.id,
                entityMap,
                libraryMap
            )
            newPropertyTrack.children.add(subTrack.key)
            newElementTrack.propertyTrackMap.set(subTrack.key, subTrack.id)
        })

        const oriElement = this.dataStore.getById(oriElementTrack.elementId)
        const newElement = this.dataStore.getById(newElementTrack.elementId)
        const vMap = new Map()
        const remapVertexId = oriElement?.basePath && newElement?.basePath
        if (remapVertexId) {
            const oriVertexIdList = Array.from(oriElement.basePath.keys())
            const newVertexIdList = Array.from(newElement.basePath.keys())
            oriVertexIdList.forEach((oriVertexId, index) => {
                vMap.set(oriVertexId, newVertexIdList[index])
            })
        }

        const keyFrameList = propertyTrack.keyFrameList.map(keyFrameId => {
            const newKeyFrame = this._cloneKeyFrame(
                this.getKeyFrame(keyFrameId, entityMap),
                newPropertyTrack.id,
                entityMap
            )
            if (remapVertexId && key === 'pathMorphing') {
                newKeyFrame.value = newKeyFrame.value.map(vertex => {
                    const id = vMap.get(vertex.id)
                    return { ...vertex, id }
                })
            }
            return newKeyFrame.id
        })
        newPropertyTrack.keyFrameList = keyFrameList

        return newPropertyTrack
    }

    /**
     * @param {KeyFrame} keyFrame
     * @param {string} propertyTrackId
     * @param {Map<string, KeyFrame>} entityMap
     * @returns {KeyFrame}
     */
    _cloneKeyFrame(keyFrame, propertyTrackId, entityMap) {
        const newKeyFrame = this._createKeyFrame(propertyTrackId, keyFrame)

        if (entityMap) {
            entityMap.set(newKeyFrame.id, newKeyFrame)
        }

        // don't clone the library component if clone by paste
        if (keyFrame.ref) {
            if (entityMap) {
                newKeyFrame.ref = keyFrame.ref
            } else {
                newKeyFrame.ref = this.dataStore.library.cloneComponent(keyFrame.ref, NO_FIRE)
            }
        }

        return newKeyFrame
    }

    _deleteAction(actionId) {
        const action = this.getAction(actionId)
        action.responseList.forEach(responseId => {
            this._deleteResponse(responseId)
        })
        action.triggerList.forEach(triggerId => {
            this._deleteTrigger(triggerId)
        })
        this.changes.delete([actionId])
        this._delete(actionId)
    }

    _deleteResponse(responseId) {
        const response = this.getResponse(responseId)
        response.conditionList.forEach(conditionId => {
            this._deleteCondition(conditionId)
        })
        response.elementTrackMap.forEach(elementTrackId => {
            this._deleteElementTrack(elementTrackId)
        })
        this.changes.delete([responseId])
        this._delete(responseId)
    }

    _deleteTrigger(triggerId) {
        this.changes.delete([triggerId])
        this._delete(triggerId)
    }

    _deleteCondition(conditionId) {
        this.changes.delete([conditionId])
        this._delete(conditionId)
    }

    _deleteElementTrack(elementTrackId) {
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return
        }
        elementTrack.propertyTrackMap.forEach((propertyTrackId, propKey) => {
            if (Object.values(AnimationPresetTypeKeyMap).includes(propKey)) {
                this.deleteAnimationPreset(propertyTrackId, false) // In the existng coding style, functon whose name starting with _ will not fire directly.
            } else {
                this._deletePropertyTrack(propertyTrackId)
            }
        })

        const response = this.getResponse(elementTrack.responseId)
        const before = new Map(response.elementTrackMap)
        response.elementTrackMap.delete(elementTrack.elementId)
        this.changes.update(response.id, 'elementTrackMap', new Change({ before, after: new Map(response.elementTrackMap) }))

        this.changes.delete([elementTrackId])
        this._delete(elementTrackId)
    }

    _deletePropertyTrack(propertyTrackId, recursiveDelete = true) {
        const propertyTrack = this.getPropertyTrack(propertyTrackId)
        const elementTrack = this.getElementTrack(propertyTrack.elementTrackId)
        propertyTrack.keyFrameList.forEach(keyFrameId => {
            this._deleteKeyFrame(keyFrameId, false)
        })
        propertyTrack.children.forEach(childKey => {
            const childId = elementTrack.propertyTrackMap.get(childKey)
            this._deletePropertyTrack(childId, false)
        })
        const before = new Map(elementTrack.propertyTrackMap)
        elementTrack.propertyTrackMap.delete(propertyTrack.key)
        this.changes.update(
            elementTrack.id,
            'propertyTrackMap',
            new Change({
                before,
                after: new Map(elementTrack.propertyTrackMap)
            })
        )
        this.changes.delete([propertyTrackId])
        this._delete(propertyTrackId)

        // non-base layer track
        if (propertyTrack.key === propertyTrack.id) {
            const layerListTrack = this.getPropertyTrack(propertyTrack.parentId)
            if (layerListTrack) {
                const listName = layerListTrack.key
                const before = elementTrack[listName].slice()
                const index = elementTrack[listName].indexOf(propertyTrack.id)
                elementTrack[listName].splice(index, 1)
                this.changes.update(
                    elementTrack.id,
                    listName,
                    new Change({
                        before,
                        after: elementTrack[listName].slice(),
                        index
                    })
                )
            }
        }

        if (recursiveDelete) {
            const parentTrack = this.getPropertyTrack(propertyTrack.parentId)
            if (parentTrack) {
                const before = new Set(parentTrack.children)
                parentTrack.children.delete(propertyTrack.key)
                this.changes.update(parentTrack.id, 'children', new Change({ before, after: new Set(parentTrack.children) }))
                if (!parentTrack.children.size) {
                    this._deletePropertyTrack(parentTrack.id)
                }
            } else if (!elementTrack.propertyTrackMap.size) {
                this._deleteElementTrack(propertyTrack.elementTrackId)
            }
        }
    }

    _deleteKeyFrame(keyFrameId, recursiveDelete = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return
        }

        const propertyTrack = this.getPropertyTrack(keyFrame.trackId)
        const before = propertyTrack.keyFrameList.slice()
        const index = before.indexOf(keyFrameId)
        if (keyFrame.ref) {
            this.dataStore.library.deleteProperty(keyFrame.ref, false)
        }

        propertyTrack.keyFrameList.splice(index, 1)
        this.changes.update(keyFrame.trackId, 'keyFrameList', new Change({
            before,
            after: propertyTrack.keyFrameList.slice(),
            index
        }))

        this.changes.delete([keyFrameId])
        this._delete(keyFrameId)

        if (recursiveDelete) {
            if (!propertyTrack.keyFrameList.length) {
                this._deletePropertyTrack(propertyTrack.id)
            }
        }
    }

    _shouldUpdateResponseDuration(newTime, currentDuration) {
        return newTime !== currentDuration &&
            (newTime >= this.timeTracker.initialMaxTime || this.timeTracker.initialMaxTime !== currentDuration)
      }

    _getDurationForUpdate(newTime) {
      const adjustedTime = Math.max(newTime, 100);
      return newTime >= this.timeTracker.initialMaxTime ?
        adjustedTime : this.timeTracker.initialMaxTime
    }


    _initializeTimeTracker(excludeKeyframeIds = [], excludePresetIds = []) {
        if (this.timeTracker.initialMaxTime === null) {
            const actionId = this.dataStore.selection?.get('action')
            const responseId = this.getFirstResponseIdByActionId(actionId)
            const keyframeMaxTime = this.getResponseMaxKeyframeTime(responseId, excludeKeyframeIds)
            const presetMaxTime = this.getMaxAnimationPresetKeyframeTime(responseId, excludePresetIds)
            this.timeTracker.initialMaxTime = Math.max(keyframeMaxTime, presetMaxTime)
        }
    }

    _resetTimeTracker() {
        this.timeTracker.initialMaxTime = null
    }

    getEntity(entityId) {
        return this.data.entityMap.get(entityId)
    }

    addAction(elementId, fire = true) {
        const action = this._createAction()

        // Add response and trigger
        this.addResponse(action.id, undefined, false)
        const triggerId = this.addTrigger(action.id, undefined, false)

        // TODO: Set trigger element if provided
        // Still need adjustment
        if (elementId) {
            this.setTriggerElement(triggerId, elementId, false)
        }

        if (fire) {
            this.fire()
        }

        return action.id
    }

    getFirstResponseIdByActionId(actionId) {
        const action = this.getAction(actionId)
        if (!action) {
            return
        }
        return action.responseList[0]
    }

    getResponseDuration(responseId) {
        const response = this.getResponse(responseId)
        if (!response) {
            return 0
        }
        return response.duration
    }


    _getKeyframeListByActionId(actionId) {
        const action = this.getAction(actionId)
        if (!action) {
            return []
        }
        const responseList = this.getResponseList(actionId)
        return responseList.map(responseId => this._getKeyframeListByResponseId(responseId)).flat()
    }

    _getKeyframeListByResponseId(responseId) {
        const response = this.getResponse(responseId)
        if (!response) {
            return []
        }
        return Array.from(response.elementTrackMap.values()).map(elementTrackId => this._getKeyframeListByElementTrackId(elementTrackId)).flat()
    }

    _getAnimationPresetByResponseId(responseId) {
        const response = this.getResponse(responseId)
        if (!response) {
            return []
        }
        return Array.from(response.elementTrackMap.values()).map(elementTrackId => this._getAnimationPresetByElementTrackId(elementTrackId)).flat()
    }

    _getKeyframeListByElementTrackId(elementTrackId) {
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return []
        }
        return Array.from(elementTrack.propertyTrackMap.values()).map(propertyTrackId => this.getKeyFrameList(propertyTrackId)).flat()
    }

    _getAnimationPresetByElementTrackId(elementTrackId) {
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return []
        }
        return Array.from(elementTrack.propertyTrackMap.values())
            .map(propertyTrackId => this.getAnimationPreset(propertyTrackId))
            .filter(Boolean)
            .flat()
    }


    getResponseMaxKeyframeTime(responseId, excludeKeyframeIds = []) {
        const response = this.getResponse(responseId)
        if (!response) {
          return 0
        }
        const allKeyframeIds = this._getKeyframeListByResponseId(responseId)
        if (!allKeyframeIds.length) {
          return 0
        }

        const excludeSet = new Set(excludeKeyframeIds)

        const filteredTimes = allKeyframeIds
          .filter(id => !excludeSet.has(id))
          .map(id => this.getKeyFrame(id).time)

        if (!filteredTimes.length) {
          return 0
        }

        return Math.max(...filteredTimes)
    }

    getMaxAnimationPresetKeyframeTime(responseId, excludeIds = []) {
        const response = this.getResponse(responseId)
        if (!response) {
          return 0
        }

        const allPresets = this._getAnimationPresetByResponseId(responseId)
        if (!allPresets.length) {
          return 0
        }

        const excludeSet = new Set(excludeIds)
        const times = allPresets
          .filter(preset => !excludeSet.has(preset.id))
          .map(preset => preset.startTime + preset.duration)

        if (!times.length) {
          return 0
        }

        return Math.max(...times)
    }

    setResponseDuration(responseId, duration, autoScale = true, fire = true) {
        if (isNaN(duration)) {
            return false
        }

        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }

        // Normalize and clamp duration between min/max limits
        const clampedDuration = clamp(Number(duration), MIN_DURATION_TIME, MAX_DURATION_TIME)

        if (response.duration === clampedDuration) {
            return false
        }

        const beforeDuration = response.duration
        const scale = clampedDuration / beforeDuration

        if (autoScale) {
            // Scale keyframes
            this._scaleKeyframesByResponse(responseId, scale)

            // Scale animation presets
            this._scaleAnimationPresetsByResponse(responseId, scale)
        }

        response.duration = clampedDuration

        this.changes.update(
            response.id,
            'duration',
            new Change({
                before: beforeDuration,
                after: clampedDuration
            })
        )
        if (fire) {
            this.fire()
        }

        return true
    }

    _scaleKeyframes(keyframeIds, scale) {
        keyframeIds.forEach(keyFrameId => {
            const keyframe = this.getKeyFrame(keyFrameId)
            if (keyframe) {
                const newTime = getTimeAtStep(keyframe.time * scale)
                this.setKeyFrameTime(keyFrameId, newTime, { replace: false, fire: false })
            }
        })
    }

    _scaleKeyframesByResponse(responseId, scale) {
        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }

        const keyframeIds = this._getKeyframeListByResponseId(responseId)
        if (!keyframeIds.length) {
            return false
        }

        this._scaleKeyframes(keyframeIds, scale)
    }

    _scaleAnimationPresets(presets, scale) {
        presets.forEach(preset => {
            if (preset) {
                const newStartTime = getTimeAtStep(preset.startTime * scale)
                const newDuration = getTimeAtStep(preset.duration * scale)

                const beforeStartTime = preset.startTime
                preset.startTime = newStartTime
                this.changes.update(
                    preset.id,
                    'startTime',
                    new Change({
                        before: beforeStartTime,
                        after: newStartTime
                    })
                )

                const beforeDuration = preset.duration
                preset.duration = newDuration
                this.changes.update(
                    preset.id,
                    'duration',
                    new Change({
                        before: beforeDuration,
                        after: newDuration
                    })
                )
            }
        })
    }

    _scaleAnimationPresetsByResponse(responseId, scale) {
        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }

        const presets = this._getAnimationPresetByResponseId(responseId)
        if (!presets.length) {
            return false
        }

        this._scaleAnimationPresets(presets, scale)
    }


    /**
     * @param {ID} actionId
     * @param {Map} [entityMap]
     * @returns {Action}
     */
    getAction(actionId, entityMap) {
        const map = entityMap || this.data.entityMap
        const action = map.get(actionId)
        if (!action || action.type !== InteractionEntityType.ACTION) {
            return
        }
        return action
    }

    /**
     * @param {ID} actionId
     * @returns {ID} elementId
     */
    getElementIdByActionId(actionId) {
        const interactionElementsMap = this.dataStore.elementInteractionManager.elementInteractions
        const elementId = interactionElementsMap.get(actionId)?.elementId

        return elementId
    }

    /**
     * @param {ID} actionId
     * @returns {boolean} success
     */
    deleteAction(actionId) {
        const action = this.getAction(actionId)
        if (!action) {
            return false
        }

        this._deleteAction(actionId)

        this.fire()

        return true
    }

    /**
     * @param {ID} actionId
     * @param {boolean} fire
     * @param {Map} elementIdMap
     * @param {Map} entityMap
     * @param {Map} libraryMap
     * @returns {ID} newActionId
     */
    cloneAction(actionId, fire = true, elementIdMap, entityMap, libraryMap) {
        const action = this.getAction(actionId, entityMap)
        if (!action) {
            return false
        }

        const newAction = this._cloneAction(action, elementIdMap, entityMap, libraryMap)

        if (fire) {
            this.fire()
        }

        return newAction.id
    }

    setActionName(actionId, name) {
        const action = this.getAction(actionId)
        if (!action) {
            return false
        }
        if (action.name === name) {
            return false
        }

        const before = action.name
        action.name = name

        this.changes.update(
            actionId,
            'name',
            new Change({ before, after: name })
        )
        this.fire()

        return true
    }

    addResponse(actionId, index = undefined, fire = true) {
        const action = this.getAction(actionId)
        if (!action) {
            return false
        }
        if (index < 0 || index > action.responseList.length) {
            return false
        }
        const response = this._createResponse(actionId)
        const idx = index === undefined ? action.responseList.length : index
        const before = action.responseList.slice()
        action.responseList.splice(idx, 0, response.id)

        this.changes.update(
            actionId,
            'responseList',
            new Change({
                before,
                after: action.responseList.slice(),
                index: idx
            })
        )

        if (fire) {
            this.fire()
        }

        return response.id
    }

    getResponseList(actionId, entityMap) {
        const action = this.getAction(actionId, entityMap)
        if (!action) {
            return []
        }
        return action.responseList
    }

    /**
     * @param {string} responseId
     * @param {Map} [entityMap]
     * @returns {import('@phase-software/types').ResponseData}
     */
    getResponse(responseId, entityMap) {
        const map = entityMap || this.data.entityMap

        const response = map.get(responseId)
        if (!response || response.type !== InteractionEntityType.RESPONSE) {
            return
        }
        return response
    }

    cloneResponse(responseId, entityMap) {
        const response = this.getResponse(responseId, entityMap)
        if (!response) {
            return false
        }
        const responseList = this.getResponseList(response.actionId, entityMap)
        const before = responseList.slice()
        const index = responseList.length

        const newResponse = this._cloneResponse(response, response.actionId)

        responseList.push(newResponse.id)

        this.changes.update(
            response.actionId,
            'responseList',
            new Change({
                before,
                after: responseList.slice(),
                index
            })
        )
        this.fire()

        return newResponse.id
    }

    getActionElementTrackIdByElementId(actionId, elementId) {
        const responseId = this.getFirstResponseIdByActionId(actionId)
        if (!responseId) return null
        const elementTrackMap = this.getElementTrackMap(responseId)
        return elementTrackMap.get(elementId)
    }

    getElementTrackIdByElementId(elementId) {
        const responseId = this._getCurrentResponseId()
        if (!responseId) return null
        const elementTrackMap = this.getElementTrackMap(responseId)
        return elementTrackMap.get(elementId)
    }

    cloneElementTrack(elementTrackId, newElementId, fire = true, entityMap) {
        const elementTrack = this.getElementTrack(elementTrackId, entityMap)
        if (!elementTrack) {
            return false
        }

        const responseId = this._getCurrentResponseId()
        if (!responseId) return null
        const newElementTrack = this._cloneElementTrack(elementTrack, responseId, newElementId, entityMap)
        if (!newElementTrack) {
            return null
        }

        const response = this.getResponse(responseId)
        const before = new Map(response.elementTrackMap)
        response.elementTrackMap.set(newElementId, newElementTrack.id)

        this.changes.update(
            response.id,
            'elementTrackMap',
            new Change({
                before,
                after: new Map(response.elementTrackMap)
            })
        )

        if (fire) {
            this.fire()
        }

        return newElementTrack.id
    }

    // clone animation preset postprocess after cloneElement
    postCloneAnimationPreset(originalElementId, newElementId) {
        // when clone a container/group of elements may only the parent has the animation preset
        if (!this.hasAnimationPreset(originalElementId)) return

        const originalElementTrackId = this.getElementTrackIdByElementId(originalElementId)
        const originalElementTrack = this.getElementTrack(originalElementTrackId)
        const newElementTrackId = this.getElementTrackIdByElementId(newElementId)
        const newElementTrack = this.getElementTrack(newElementTrackId)

        if (!originalElementTrack || !newElementTrack) return

        Object.values(AnimationPresetTypeKeyMap).forEach(key => {
            const presetTrackId = originalElementTrack.propertyTrackMap.get(key)
            const presetData = this.getAnimationPreset(presetTrackId)

            if (presetTrackId && presetData) {
                this.dataStore.transition.postCloneAnimationPreset(originalElementId, newElementId, presetData)

                const presetTrack = this._createAnimationPreset(newElementTrack, presetData.key, presetData)

                let before = new Map(newElementTrack.propertyTrackMap)
                newElementTrack.propertyTrackMap.set(presetTrack.key, presetTrack.id)
                this.changes.update(
                    newElementTrack.id,
                    'propertyTrackMap',
                    new Change({
                        before: before,
                        after: newElementTrack.propertyTrackMap
                    })
                )

                before = newElementTrack.presets.slice()
                newElementTrack.presets.push(presetTrack.id)
                this.changes.update(
                    newElementTrack.id,
                    'presets',
                    new Change({
                        before: before,
                        after: newElementTrack.presets
                    })
                )
            }
        })
    }

    deleteResponse(responseId) {
        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }

        const responseList = this.getResponseList(response.actionId)
        const before = responseList.slice()
        this._deleteResponse(responseId)

        const index = responseList.indexOf(responseId)
        responseList.splice(index, 1)

        this.changes.update(
            response.actionId,
            'responseList',
            new Change({
                before,
                after: responseList.slice(),
                index
            })
        )
        this.fire()
        return true
    }

    setResponseName(responseId, name) {
        const response = this.getResponse(responseId)

        if (!response) {
            return false
        }
        if (response.name === name) {
            return false
        }

        const before = response.name

        response.name = name

        this.changes.update(
            response.id,
            'name',
            new Change({
                before,
                after: response.name
            })
        )
        this.fire()

        return true
    }

    /**
     * @param {string} responseId
     * @param {boolean} stopPropagation
     * @returns {boolean} success
     */
    setResponsePropagation(responseId, stopPropagation) {
        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }
        const before = response.stopPropagation
        response.stopPropagation = stopPropagation
        this.changes.update(
            response.id,
            'stopPropagation',
            new Change({ before, after: stopPropagation })
        )
        this.fire()
        return true
    }

    // TODO: when trigger is created default trigger should be set to default of tigger of onClick and the target is the currently selected element
    addTrigger(actionId, index = undefined, fire = true) {
        const action = this.getAction(actionId)
        if (!action) {
            return false
        }

        if (index < 0 || index > action.triggerList.length) {
            return false
        }
        const trigger = this._createTrigger(actionId)
        const before = action.triggerList.slice()
        const idx = index === undefined ? action.triggerList.length : index
        action.triggerList.splice(idx, 0, trigger.id)

        this.changes.update(
            actionId,
            'triggerList',
            new Change({
                before,
                after: action.triggerList.slice(),
                index: idx
            })
        )
        if (fire) {
            this.fire()
        }
        return trigger.id
    }

    getTriggerList(actionId) {
        const action = this.getAction(actionId)
        if (!action) {
            return []
        }
        return action.triggerList
    }

    getTrigger(triggerId, entityMap) {
        const map = entityMap || this.data.entityMap
        const trigger = map.get(triggerId)
        if (!trigger || trigger.type !== InteractionEntityType.TRIGGER) {
            return
        }
        return trigger
    }

    cloneTrigger(triggerId) {
        const trigger = this.getTrigger(triggerId)
        if (!trigger) {
            return false
        }
        const newTrigger = this._cloneTrigger(trigger, trigger.actionId)

        const triggerList = this.getTriggerList(trigger.actionId)
        const before = triggerList.slice()
        const index = triggerList.length

        triggerList.push(newTrigger.id)

        this.changes.update(
            trigger.actionId,
            'triggerList',
            new Change({
                before,
                after: triggerList.slice(),
                index
            })
        )
        this.fire()

        return newTrigger.id
    }

    deleteTrigger(triggerId) {
        const trigger = this.getTrigger(triggerId)
        if (!trigger) {
            return false
        }

        this._deleteTrigger(triggerId)

        const triggerList = this.getTriggerList(trigger.actionId)
        const before = triggerList.slice()
        const index = triggerList.indexOf(triggerId)
        triggerList.splice(index, 1)

        this.changes.update(
            trigger.actionId,
            'triggerList',
            new Change({
                before,
                after: triggerList.slice(),
                index
            })
        )
        this.fire()
        return true
    }

    setTriggerElement(triggerId, elementId, fire = true) {
        const trigger = this.getTrigger(triggerId)

        if (!trigger) {
            return false
        }

        const element = this.dataStore.getById(elementId)
        if (!element || element.get('type') !== EntityType.ELEMENT) {
            return false
        }

        if (trigger.selector.selectorType === SelectorType.ELEMENT && trigger.selector.value === element.get('id')) {
            return false
        }

        this._setTriggerSelector(triggerId, {
            selectorType: SelectorType.ELEMENT,
            value: elementId
        })
        if (fire) {
            this.fire()
        }
        return true
    }

    setTriggerType(triggerId, triggerType) {
        const trigger = this.getTrigger(triggerId)

        if (!trigger) {
            return false
        }

        if (!Object.values(TriggerType).includes(triggerType)) {
            return false
        }
        const before = trigger.triggerType
        const optionsBefore = { ...trigger.options }
        trigger.triggerType = triggerType
        trigger.options = getTriggerOptions(triggerType)
        this.changes.update(
            triggerId,
            'options',
            new Change({
                before: optionsBefore,
                after: trigger.options
            })
        )
        this.changes.update(
            triggerId,
            'triggerType',
            new Change({
                before,
                after: triggerType
            })
        )
        this.fire()
        return true
    }

    setTriggerOptions(triggerId, changes) {
        const trigger = this.getTrigger(triggerId)

        if (!trigger) {
            return false
        }

        if (typeof changes !== 'object' || changes === null) {
            return false
        }

        const before = { ...trigger.options }

        const dirtyKeys = new Set()
        Object.entries(changes).forEach(([key, value]) => {
            if (Object.prototype.hasOwnProperty.call(trigger.options, key)) {
                if (trigger.options[key] !== value) {
                    trigger.options[key] = value
                    dirtyKeys.add(key)
                }
            }
        })

        if (dirtyKeys.size === 0) {
            return false
        }

        this.changes.update(
            triggerId,
            'options',
            new Change({
                before,
                after: trigger.options
            })
        )
        this.fire()

        return true
    }

    addCondition(responseId, index = undefined) {
        const response = this.getResponse(responseId)

        if (!response) {
            return false
        }

        const conditionList = response.conditionList
        const before = conditionList.slice()
        if (index < 0 || index > conditionList.length) {
            return false
        }
        const condition = this._createCondition(responseId)
        const idx = index === undefined ? conditionList.length : index
        conditionList.splice(idx, 0, condition.id)

        this.changes.update(
            responseId,
            'conditionList',
            new Change({
                before,
                after: conditionList.slice(),
                index: idx
            })
        )
        this.fire()

        return condition.id
    }

    getConditionList(responseId) {
        const response = this.getResponse(responseId)

        if (!response) {
            return
        }

        return response.conditionList
    }

    getCondition(conditionId) {
        const condition = this.data.entityMap.get(conditionId)
        if (!condition || condition.type !== InteractionEntityType.CONDITION) {
            return
        }
        return condition
    }

    cloneCondition(conditionId) {
        const condition = this.getCondition(conditionId)

        if (!condition) {
            return false
        }

        const newCondition = this._cloneCondition(condition, condition.responseId)

        const conditionList = this.getConditionList(condition.responseId)
        const before = conditionList.slice()
        const index = conditionList.length
        conditionList.push(newCondition.id)

        this.changes.update(
            newCondition.responseId,
            'conditionList',
            new Change({
                before,
                after: conditionList,
                index
            })
        )
        this.fire()
        return newCondition.id
    }

    deleteCondition(conditionId) {
        const condition = this.getCondition(conditionId)
        if (!condition) {
            return false
        }

        this._deleteCondition(conditionId)

        const conditionList = this.getConditionList(condition.responseId)
        const before = conditionList.slice()
        const index = conditionList.indexOf(conditionId)
        conditionList.splice(index, 1)

        this.changes.update(
            condition.responseId,
            'conditionList',
            new Change({
                before,
                after: conditionList,
                index
            })
        )
        this.fire()
        return true
    }

    setConditionLogic(conditionId, logic) {
        const condition = this.getCondition(conditionId)
        if (!condition) {
            return false
        }

        if (!Object.values(LogicType).includes(logic)) {
            return false
        }

        if (condition.logic === logic) {
            return false
        }
        const before = condition.logic
        condition.logic = logic

        this.changes.update(
            conditionId,
            'logic',
            new Change({
                before,
                after: condition.logic
            })
        )
        this.fire()

        return true
    }

    setConditionElement(conditionId, elementId) {
        const condition = this.getCondition(conditionId)

        if (!condition) {
            return false
        }

        if (condition.elementId === elementId) {
            return false
        }

        const element = this.dataStore.getById(elementId)
        if (!element || element.get('type') !== EntityType.ELEMENT) {
            return false
        }

        // unbind old event listeners
        if (condition.elementId) {
            const element = this.dataStore.getById(condition.elementId)
            this._unwatchElement(element)
        }
        this._watchElement(element)

        const before = condition.elementId
        condition.elementId = elementId

        this.changes.update(
            conditionId,
            'elementId',
            new Change({
                before,
                after: condition.elementId
            })
        )
        this.fire()

        return true
    }

    setConditionOperation(conditionId, operation) {
        const condition = this.getCondition(conditionId)
        if (!condition) {
            return false
        }

        if (!Object.values(OperationType).includes(operation)) {
            return false
        }

        if (condition.operation === operation) {
            return false
        }
        const before = condition.operation
        condition.operation = operation

        this.changes.update(
            conditionId,
            'operation',
            new Change({
                before,
                after: condition.operation
            })
        )
        this.fire()
        return true
    }

    getElementTrackMap(responseId) {
        const response = this.getResponse(responseId)

        if (!response) {
            return
        }

        return response.elementTrackMap
    }

    getAnimatedElementMap() {
        const actionId = this._getCurrentActionId()
        if (!actionId) {
            return
        }
        const responseId = this.getFirstResponseIdByActionId(actionId)
        const response = this.getResponse(responseId)
        if (!response) {
            return
        }

        return response.elementTrackMap
    }

    addElementTrack(responseId, elementId) {
        const response = this.getResponse(responseId)
        if (!response) {
            return false
        }

        const element = this.dataStore.getById(elementId)
        if (!element || element.get('type') !== EntityType.ELEMENT) {
            return false
        }

        if (response.elementTrackMap.has(elementId)) {
            return false
        }

        const elementTrack = this._createElementTrack(responseId, elementId)

        const before = new Map(response.elementTrackMap)
        response.elementTrackMap.set(elementId, elementTrack.id)

        this.changes.update(
            responseId,
            'elementTrackMap',
            new Change({
                before,
                after: new Map(response.elementTrackMap)
            })
        )
        this.fire()

        return elementTrack.id
    }

    /**
     * @param {string} elementTrackId
     * @param {Map} entityMap
     * @returns {ElementTrack}
     */
    getElementTrack(elementTrackId, entityMap) {
        const map = entityMap || this.data.entityMap
        const elementTrack = map.get(elementTrackId)

        if (!elementTrack || elementTrack.type !== InteractionEntityType.ELEMENT_TRACK) {
            return
        }

        return elementTrack
    }

    getElementTrackKeyFrameGroupByTime(elementTrackId, filteredKeySet) {
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return
        }

        const map = {}
        elementTrack.propertyTrackMap.forEach((trackId, trackKey) => {
            if (filteredKeySet && !filteredKeySet.has(trackKey)) {
                return
            }
            if (!Object.values(AnimationPresetTypeKeyMap).includes(trackKey)) {
                const keyFrameList = this.getKeyFrameList(trackId)
                this.groupKeyframeByTime(keyFrameList, map)
            }
        })

        return map
    }

    deleteElementTrack(elementTrackId, fire = true) {
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return false
        }

        this._deleteElementTrack(elementTrackId)

        if (fire) {
            this.fire()
        }
        return true
    }

    deleteElementPropertyTrack(elementId, trackKey, fire = true) {
        const elementTrackId = this.getElementTrackIdByElementId(elementId)
        const propertyTrackMap = this.getPropertyTrackMap(elementTrackId)
        if (!propertyTrackMap) {
            return false
        }

        const trackId = propertyTrackMap.get(trackKey)
        if (!trackId) {
            return false
        }

        this._deletePropertyTrack(trackId)

        if (fire) {
            this.fire()
        }

        return true
    }

    /**
     * @param {string} elementId
     * @returns {Map}
     */
    getPropertyTrackMapByElementId(elementId) {
        const responseId = this._getCurrentResponseId()
        if (!responseId) return null
        const elementTrackMap = this.getElementTrackMap(responseId)
        const elementTrackId = elementTrackMap.get(elementId)
        return this.getPropertyTrackMap(elementTrackId)
    }

    /**
     * @param {string} elementTrackId
     * @param {Map} entityMap
     * @returns
     */
    getPropertyTrackMap(elementTrackId, entityMap) {
        const elementTrack = this.getElementTrack(elementTrackId, entityMap)

        if (!elementTrack) {
            return
        }

        return elementTrack.propertyTrackMap
    }

    /**
     * @param {string} trackId
     * @param {Map} entityMap
     * @returns {ElementTrack | PropertyTrack | AnimationPreset}
     */
    getTrack(trackId, entityMap) {
        const map = entityMap || this.data.entityMap
        const track = map.get(trackId)
        switch (track?.type) {
            case InteractionEntityType.ELEMENT_TRACK:
            case InteractionEntityType.PROPERTY_TRACK:
            case InteractionEntityType.ANIMATION_PRESET:
                return track
        }
    }

    /**
     *
     * @param {string} propertyTrackId
     * @param {Map} entityMap
     * @returns {PropertyTrack}
     */
    getPropertyTrack(propertyTrackId, entityMap) {
        const map = entityMap || this.data.entityMap
        const propertyTrack = map.get(propertyTrackId)
        if (
            !propertyTrack ||
            propertyTrack.type !== InteractionEntityType.PROPERTY_TRACK
        ) {
            return
        }
        return propertyTrack
    }

    /**
     * @param {string} presetTrackId
     * @param {Map} entityMap
     * @returns {AnimationPreset}
     */
    getAnimationPreset(presetTrackId, entityMap) {
        const map = entityMap || this.data.entityMap
        const animationPreset = map.get(presetTrackId)
        if (
            !animationPreset ||
            animationPreset.type !== InteractionEntityType.ANIMATION_PRESET
        ) {
            return
        }
        return animationPreset
    }

    setTrackSegmentStartTimeAndDuration(trackId, startTime, duration, finished, fire = true) {
        const presetTrack = this.getAnimationPreset(trackId)
        if (!presetTrack) {
            return
        }
        // Initialize base max time if not already set
        this._initializeTimeTracker([], [presetTrack.id])

        const beforeStartTime = presetTrack.startTime
        const beforeDuration = presetTrack.duration

        // stretch start time
        if (beforeStartTime !== startTime && beforeDuration !== duration) {
            presetTrack.startTime = startTime
            presetTrack.duration = duration

            this.changes.update(trackId, 'startTime', new Change({
                before: beforeStartTime,
                after: presetTrack.startTime
            }))
            this.changes.update(trackId, 'duration', new Change({
                before: beforeDuration,
                after: presetTrack.duration
            }))
            this.updateResponseDurationWithCache(presetTrack.startTime + presetTrack.duration)
        }
        // move segment
        else if (beforeStartTime !== startTime) {
            presetTrack.startTime = startTime

            this.changes.update(trackId, 'startTime', new Change({
                before: beforeStartTime,
                after: presetTrack.startTime
            }))

            if (!this.timeTracker.currentMaxTime || this.timeTracker.currentMaxTime < presetTrack.startTime + presetTrack.duration) {
                this.timeTracker.currentMaxTime = presetTrack.startTime + presetTrack.duration
                this.updateResponseDurationWithCache(presetTrack.startTime + presetTrack.duration)
            }
        }
        // stretch end time
        else if (beforeDuration !== duration) {
            presetTrack.duration = duration
            this.changes.update(trackId, 'duration', new Change({
                before: beforeDuration,
                after: presetTrack.duration
            }))
            this.updateResponseDurationWithCache(presetTrack.startTime + presetTrack.duration)
        }

        if (finished) {
            this._resetTimeTracker()
        }

        if (fire) {
            this.fire(true, {flag: EventFlag.FROM_INTERACTION_CONTINUOUSLY_CHANGE})
        }
    }

    getActionPropertyTrackByElementIdAndPropKey(actionId, elementId, propKey) {
        const elementTrackId = this.getActionElementTrackIdByElementId(actionId, elementId)
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return
        }
        const propertyTrackId = elementTrack.propertyTrackMap.get(propKey)
        return this.getPropertyTrack(propertyTrackId)
    }

    getPropertyTrackByElementIdAndPropKey(elementId, propKey) {
        const elementTrackId = this.getElementTrackIdByElementId(elementId)
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return
        }
        const propertyTrackId = elementTrack.propertyTrackMap.get(propKey)
        return this.getPropertyTrack(propertyTrackId)
    }

    getPropertyTrackKeyFrameGroupByTime(propertyTrackId) {
        const propertyTrack = this.getPropertyTrack(propertyTrackId)
        if (!propertyTrack) {
            return
        }
        const queue = [propertyTrack.key]
        const map = {}
        const propTrackMap = this.getPropertyTrackMap(propertyTrack.elementTrackId)

        while (queue.length) {
            const trackKey = queue.shift()
            const trackId = propTrackMap.get(trackKey)
            const track = this.getPropertyTrack(trackId)
            if (!track) {
                continue
            }
            queue.push(...track.children)
            const keyFrameList = this.getKeyFrameList(trackId)
            this.groupKeyframeByTime(keyFrameList, map)
        }
        return map
    }

    getKeyframeListByTime(time) {
        const responseId = this._getCurrentResponseId()
        const elementTrackMap = this.getElementTrackMap(responseId)
        const keyframeList = []
        elementTrackMap.forEach(elementTrackId => {
            const elementTrack = this.getElementTrack(elementTrackId)
            elementTrack.propertyTrackMap.forEach(propertyTrackId => {
                const keyFrameList = this.getKeyFrameList(propertyTrackId)
                if (keyFrameList) {
                    keyFrameList.forEach(keyFrameId => {
                        const keyFrame = this.getKeyFrame(keyFrameId)
                        if (!keyFrame) {
                            return
                        }
                        if (keyFrame.time === time) {
                            keyframeList.push(keyFrameId)
                        }
                    })
                }
            })
        })
        return keyframeList
    }

    groupKeyframeEntityByTime(keyframeList = [], initialMap = {}) {
        return keyframeList.reduce((acc, keyframe) => {
            acc[keyframe.time] = acc[keyframe.time] || []
            acc[keyframe.time].push(keyframe.id)
            return acc
        }, initialMap)
    }

    groupKeyframeByTime(keyframeIdList = [], initialMap = {}) {
        const keyframeList = keyframeIdList.map(id => this.getKeyFrame(id)).filter(Boolean)
        return this.groupKeyframeEntityByTime(keyframeList, initialMap)
    }

    groupEntityByPropName(entityList = [], initialMap = {}, propName) {
        return entityList.reduce((acc, entity) => {
            acc[entity[propName]] = acc[entity[propName]] || []
            acc[entity[propName]].push(entity)
            return acc
        }, initialMap)
    }

    groupPresetByStartTime(presetIdList = [], initialMap = {}) {
        const presetList = presetIdList.map(id => this.getAnimationPreset(id)).filter(Boolean)
        return this.groupEntityByPropName(presetList, initialMap, 'startTime')
    }

    /**
     * Calculates if the selected keyframes are overlapped.
     *
     * @returns {boolean} true if any of the selected keyframes overlap, false otherwise.
     */
    getIsSelectedKeyframesOverlapped() {
        const selectedKeyframeIds = this.dataStore.selection.get('kfs')

        if (!selectedKeyframeIds.length) {
            return
        }
        const selectedKeyframes = selectedKeyframeIds.map(id => this.getKeyFrame(id)).filter(Boolean)
        return selectedKeyframes.some(selectedKeyframe => this.getKeyFrameByTime(selectedKeyframe.trackId, selectedKeyframe.time, selectedKeyframe.id))
    }

    getAllKeyFramesByTime(propertyTrackId, time, refId) {
        const keyFrameList = this.getKeyFrameList(propertyTrackId)
        if (!keyFrameList) {
            return []
        }
        const keyFrames = keyFrameList.map(id => this.getKeyFrame(id))
        return keyFrames.filter(keyFrame => keyFrame.id !== refId && keyFrame.time === time)
    }

    getKeyFrameByTime(propertyTrackId, time, refId) {
        const keyFrameList = this.getKeyFrameList(propertyTrackId)
        if (!keyFrameList) {
            return
        }
        const keyFrames = keyFrameList.map(id => this.getKeyFrame(id)).filter(Boolean)
        return keyFrames.find(keyFrame => keyFrame.id !== refId && keyFrame.time === time)
    }

    getKeyFrameList(propertyTrackId) {
        const propertyTrack = this.getPropertyTrack(propertyTrackId)
        if (!propertyTrack) {
            return []
        }
        return propertyTrack.keyFrameList
    }

    createKeyframeFromTMKeyframe(elementId, propertyKey, keyframe) {
        const { key, trackId } = keyframe

        const animationPreset = this.dataStore.interaction.getAnimationPreset(trackId)

        const tmKeyframes = this.dataStore.transition.getPropertyKeyframesByElementId(elementId, propertyKey)
        const selectedKf = tmKeyframes.find(kf => kf.id === key && kf.trackId === trackId)
        const insertKeyframeData = pick(selectedKf, [
            'animatable',
            'bezier',
            'delta',
            'easingType',
            'frameType',
            'step',
            'time',
            'type',
            'unit',
            'value'
        ])

        const elementTrack = this.dataStore.interaction.getElementTrack(animationPreset.elementTrackId)
        const propertyTrackId =  elementTrack.propertyTrackMap.get(propertyKey)
        let propertyTrack = this.dataStore.interaction.getPropertyTrack(propertyTrackId)
        if (!propertyTrack) {
            const trackKeyList = [propertyKey]
            propertyTrack = this.dataStore.interaction._createPropertyTrackRecursive(elementTrack, trackKeyList)[0]
        }

        const newKeyframe = this.dataStore.interaction.upsertKeyframeToPropertyTrack(propertyTrack, insertKeyframeData)
        return newKeyframe
    }

    // @deprecate
    addKeyFrame(propertyTrackId, time, value, fire = true) {
        const propertyTrack = this.getPropertyTrack(propertyTrackId)
        if (!propertyTrack) {
            return false
        }

        const existKeyFrame = this.getKeyFrameByTime(propertyTrackId, time)
        if (existKeyFrame) {
            return false
        }

        const keyFrame = this._createKeyFrame(propertyTrackId, { time, value })

        const before = propertyTrack.keyFrameList.slice()
        propertyTrack.keyFrameList.push(keyFrame.id)

        this.changes.update(
            propertyTrack.id,
            'keyFrameList',
            new Change({
                before,
                after: propertyTrack.keyFrameList.slice(),
                index: before.length
            })
        )
        if (fire) {
            this.fire()
        }

        return keyFrame.id
    }

    flushKfByTime(entityId) {
        const entity = this.data.entityMap.get(entityId)
        if (!entity) {
            return
        }

        // recursive flush first
        switch (entity.type) {
            case InteractionEntityType.PROPERTY_TRACK: {
                if (entity.parentId) {
                    this.flushKfByTime(entity.parentId)
                } else {
                    this.flushKfByTime(entity.elementTrackId)
                }
                break
            }

            case InteractionEntityType.KEY_FRAME: {
                this.flushKfByTime(entity.trackId)
            }
        }

        this._cacheKeyframeGroupByTime.delete(entity)
    }

    getKeyframeIdList(trackId, time) {
        const track = this.data.entityMap.get(trackId)
        if (!track) {
            return []
        }

        if (this._cacheKeyframeGroupByTime.has(track)) {
            const map = this._cacheKeyframeGroupByTime.get(track)
            return map[time] || []
        }


        switch (track.type) {
            case InteractionEntityType.ELEMENT_TRACK: {
                const map = this.getElementTrackKeyFrameGroupByTime(trackId)
                this._cacheKeyframeGroupByTime.set(track, map)
                return map[time] || []
            }

            case InteractionEntityType.PROPERTY_TRACK: {
                const map = this.getPropertyTrackKeyFrameGroupByTime(trackId)
                this._cacheKeyframeGroupByTime.set(track, map)
                return map[time] || []
            }
            default:
                return []
        }
    }

    getKeyFrame(keyFrameId, entityMap) {
        const map = entityMap || this.data.entityMap
        const keyFrame = map.get(keyFrameId)
        if (
            !keyFrame ||
            keyFrame.type !== InteractionEntityType.KEY_FRAME
        ) {
            return
        }
        return keyFrame
    }

    getElementIdByKeyFrame(keyFrameId) {
        const keyFrame = this.data.entityMap.get(keyFrameId)
        if (!keyFrame || keyFrame.type !== InteractionEntityType.KEY_FRAME) {
            return null
        }

        const propertyTrack = this.data.entityMap.get(keyFrame.trackId)
        const elementTrack = this.data.entityMap.get(propertyTrack.elementTrackId)
        return elementTrack.elementId
    }

    setSelectedKeyFrameEasingType(easingType) {
        const kfSelection = this.dataStore.selection.get('kfs')
        kfSelection.forEach(kfId => {
            this.setKeyFrameEasingType(kfId, easingType, false)
        })

        const segmentSelection = this.dataStore.selection.get('segments')
        segmentSelection.forEach(presetId => {
            this.setPresetEasingType(presetId, easingType, false)
        })
        this.fire()
    }

    getMaxKeyframeOffset(keyframes) {
        const currentPlayheadTime = this._getTransitionTime()
        const offsets = keyframes.map((keyframe) => currentPlayheadTime - keyframe.time)
        return Math.max(...offsets)
    }

    setSelectedKeyFrameBezier(bezier) {
        const kfSelection = this.dataStore.selection.get('kfs')
        kfSelection.forEach(kfId => {
            this.setKeyFrameBezier(kfId, bezier, false)
        })

        const segmentSelection = this.dataStore.selection.get('segments')
        segmentSelection.forEach(presetId => {
            this.setPresetBezier(presetId, bezier, false)
        })
        this.fire()
    }

    getKeyframesTimeRange(keyframeList) {
        const keyframes = keyframeList.map(kfId => this.getKeyFrame(kfId)).filter(Boolean)
        const times = keyframes.map(kf => kf.time)
        return {
            min: Math.min(...times),
            max: Math.max(...times)
        }
    }

    getSelectedKeyframesTimeRange() {
        const kfSelection = this.dataStore.selection.get('kfs')
        return this.getKeyframesTimeRange(kfSelection)
    }

    getAllKeyframes(excludedSet = new Set()) {
        const list = []
        this.data.entityMap.forEach((entity) => {
            if (entity.type === InteractionEntityType.KEY_FRAME && !excludedSet.has(entity.id)) {
                list.push(entity)
            }
        })
        return list
    }

    getKeyframeTimeList(keyframeList) {
        return Array.from(new Set(keyframeList.map(keyframe => keyframe.time))).sort((a, b) => a - b)
    }

    getAllAnimationPresetKeyframes(excludedSet = new Set()) {
        const list = []
        this.data.entityMap.forEach((entity) => {
            if (entity.type === InteractionEntityType.ANIMATION_PRESET && !excludedSet.has(entity.id)) {
                list.push(entity)
            }
        })
        const keyframes = []
        list.forEach((entity) => {
            const kfs = this.dataStore.transition.getAnimationPresetKeyframes(entity.id)
            keyframes.push(...kfs)
        })
        return keyframes
    }

    _cacheListByKey(key, cachedData) {
        this[`_${key}Cache`] = this[`_${key}Cache`] || cachedData
        return this[`_${key}Cache`]
    }

    _clearCachedListByKey(key) {
        this[`_${key}Cache`] = undefined
    }

    _reorderKeyframeList(keyframeIdList) {
        // TODO: DRY sort keyframes
        const trackIds = new Set(keyframeIdList.map(keyframeId => this.getKeyFrame(keyframeId)?.trackId).filter(Boolean))

        // sort keyFrames by time
        for (const tid of trackIds) {
            const track = this.getPropertyTrack(tid)

            const before = [...track.keyFrameList]
            track.keyFrameList.sort((kfA, kfB) => this.getKeyFrame(kfA).time - this.getKeyFrame(kfB).time)

            const after = [...track.keyFrameList]
            if (!arrEquals(before, after)) {
                this.changes.update(tid, 'keyFrameList', new Change({
                    before,
                    after
                }))
            }
        }
    }

    stretchKeyframeList(keyframeIds, startTime, endTime, finished = false) {
        const keyframesGroupedByTime = this._cacheListByKey('keyframe', this.groupKeyframeByTime(keyframeIds))
        const times = Object.keys(keyframesGroupedByTime).map(Number).sort((a, b) => a - b)

        // Normalize.
        const minTime = times[0]
        const maxTime = times[times.length - 1]
        const timeRange = maxTime - minTime

        const clampedStartTime = clamp(startTime, 0, MAX_DURATION_TIME)
        const clampedEndTime = clamp(endTime, 0, MAX_DURATION_TIME)
        const scale = (clampedEndTime - clampedStartTime) / timeRange

        // Replace order:
        // 1. dragged stretch keyframe (t)
        // 2. stretch keyframe (t)
        // 2. time (selected)
        // 4. un-selected
        const isDragEnd = startTime === times[0]
        const rest = times.slice(1, -1).reverse()
        const replaceOrder = isDragEnd ? [maxTime, minTime, ...rest] : [minTime, maxTime, ...rest]
        let maxScaledTime = 0

        this._initializeTimeTracker(keyframeIds, [])

        replaceOrder.forEach(time => {
            const keyframeIds = keyframesGroupedByTime[time]
            const offsetTime = time - minTime
            const scaledTime = getTimeAtStep(offsetTime * scale + clampedStartTime)
            maxScaledTime = Math.max(maxScaledTime, scaledTime)
            this.timeTracker.currentMaxTime = scaledTime

            keyframeIds.forEach(keyframeId => {
                this.setKeyFrameTime(keyframeId, scaledTime, {replace: finished, fire: false})
            })
        })

        this.updateResponseDurationWithCache(maxScaledTime)

        if (finished) {
            this._reorderKeyframeList(keyframeIds)
            this._clearCachedListByKey('keyframe')
            this._resetTimeTracker()
        }

        this.timeTracker.currentMaxTime = null

        this.fire(true, {flag: EventFlag.FROM_INTERACTION_CONTINUOUSLY_CHANGE})
    }



    setSelectedKeyframeAndPresetByTime(keyframeIdList, presetIdList, diffMs, finished = false) {
        const keyframeGroupByTime = this._cacheListByKey('keyframe', this.groupKeyframeByTime(keyframeIdList))
        const presetGroupByStartTime = this._cacheListByKey('presetStart', this.groupPresetByStartTime(presetIdList))

        const keyframeTimes = Object.keys(keyframeGroupByTime).map(Number)
        const presetStartTimes = Object.keys(presetGroupByStartTime).map(Number)
        const timeList = [...keyframeTimes, ...presetStartTimes]

        // Initialize base max time if not already set
        this._initializeTimeTracker(keyframeIdList, presetIdList)

        if (timeList.length === 0) {
            return
        }

        const presetEndTimeList = this._cacheListByKey('presetEnd', Object.values(presetGroupByStartTime).map(presetList => {
            const preset = presetList[0]
            const endTime = preset.startTime + preset.duration
            return endTime
        }))

        const minTime = Math.min(...timeList)
        const maxTime = Math.max(
            ...timeList,
            ...presetEndTimeList
        )

        let restrictedDiff = diffMs
        if (minTime + restrictedDiff < 0) {
            restrictedDiff = -minTime // Limit at start (0)
        } else if (maxTime + restrictedDiff > MAX_DURATION_TIME) {
            restrictedDiff = MAX_DURATION_TIME - maxTime // Limit at end
        }

        // Update keyframes
        keyframeTimes.forEach((time) => {
            const kfIdList = keyframeGroupByTime[time]
            kfIdList.forEach(kfId => {
                this.setKeyFrameTime(kfId, parseInt(time) + restrictedDiff, { replace: finished, fire: false })
            })
        })

        // Update presets
        presetStartTimes.forEach(time => {
            const presetList = presetGroupByStartTime[time]
            presetList.forEach(preset => {
                this.setTrackSegmentStartTimeAndDuration(preset.id, parseInt(time) + restrictedDiff, preset.duration, finished, false)
            })
        })


        if (finished) {
            this._reorderKeyframeList(keyframeIdList)

            this._clearCachedListByKey('keyframe')
            this._clearCachedListByKey('presetStart')
            this._clearCachedListByKey('presetEnd')
            this._resetTimeTracker()
        }

        // Reset temporary state
        this.timeTracker.currentMaxTime = null

        this.fire(true, {flag: EventFlag.FROM_INTERACTION_CONTINUOUSLY_CHANGE})
    }

    deleteSelectedKeyframesAndPresets() {
        this.deleteSelectedKeyFrame(false)
        this.deleteSelectedAnimationPresets(false)
        this.fire()
    }

    // TODO: test
    deleteSelectedKeyFrame(fire = true) {
        const kfSelection = new Set(this.dataStore.selection.get('kfs'))

        // Delete kfs
        kfSelection.forEach(keyFrameId => {
            this._deleteKeyFrame(keyFrameId)
        })
        if (fire) {
            this.fire()
        }
        this.dataStore.selection.clearKFs()
    }

    // TODO: test
    duplicateSelectedKeyFrame() {
        const kfSelection = this.dataStore.selection.get('kfs')
        const keyFrames = kfSelection.map(kfId => this.getKeyFrame(kfId))
        const maxTimeOffset = this.getMaxKeyframeOffset(keyFrames)

        // clone kf by offset time
        const newKfIds = keyFrames.map(kf => this.cloneKeyFrame(kf.id, maxTimeOffset + kf.time, false)).filter(Boolean)

        const trackIds = new Set(keyFrames.map(kf => kf.trackId))

        // sort keyFrames by time
        for (const tid of trackIds) {
            const track = this.getPropertyTrack(tid)

            const before = [...track.keyFrameList]
            track.keyFrameList.sort((kfA, kfB) => this.getKeyFrame(kfA).time - this.getKeyFrame(kfB).time)

            const after = [...track.keyFrameList]
            if (!arrEquals(before, after)) {
                this.changes.update(tid, 'keyFrameList', new Change({
                    before,
                    after
                }))
            }
        }

        if (!this.changes.isEmpty()) {
            this.fire()
        }
        return newKfIds
    }

    setKeyFrameTime(keyFrameId, time, { replace = false, fire = true } = { replace: false, fire: true }) {
        const keyFrame = this.getKeyFrame(keyFrameId)

        if (!keyFrame) {
            return false
        }

        if (keyFrame.time === time && !replace) {
            return false
        }

        // replace the old KeyFrame(s) which at the same time
        if (replace) {
            const existKeyFrameList = this.getAllKeyFramesByTime(keyFrame.trackId, time, keyFrame.id)
            if (existKeyFrameList.length) {
                existKeyFrameList.forEach(existKeyFrame => {
                    this._deleteKeyFrame(existKeyFrame.id)
                })
            }
        }

        if (keyFrame.time !== time) {
            const before = keyFrame.time
            keyFrame.time = time

            this.changes.update(keyFrameId, 'time', new Change({
                before,
                after: keyFrame.time
            }))
            if (!this.timeTracker.currentMaxTime || this.timeTracker.currentMaxTime < keyFrame.time) {
                this.timeTracker.currentMaxTime = keyFrame.time
                this.updateResponseDurationWithCache(keyFrame.time)
            }
        }

        if (fire) {
            this.fire()
        }

        return true
    }

    setKeyFrameFrameType(keyFrameId, frameType) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return false
        }

        if (!Object.values(FrameType).includes(frameType)) {
            return false
        }

        if (keyFrame.frameType === frameType) {
            return false
        }

        const before = keyFrame.frameType
        keyFrame.frameType = frameType

        this.changes.update(keyFrameId, 'frameType', new Change({
            before,
            after: keyFrame.frameType
        }))
        this.fire()

        return true
    }

    setKeyFrameEasingType(keyFrameId, easingType, fire = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return false
        }

        if (!keyFrame.animatable) {
            return false
        }

        if (!Object.values(EasingType).includes(easingType)) {
            return false
        }

        if (keyFrame.easingType === easingType) {
            return false
        }

        const before = keyFrame.easingType
        keyFrame.easingType = easingType

        this.changes.update(keyFrameId, 'easingType', new Change({
            before,
            after: keyFrame.easingType
        }))

        this.setKeyFrameBezier(keyFrameId, easePoints[easingType], false)

        if (fire) {
            this.fire()
        }
        return true
    }

    setKeyFrameBezier(keyFrameId, bezier, fire = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return false
        }

        if (!keyFrame.animatable) {
            return false
        }

        if (!Array.isArray(bezier) || bezier.length !== 4) {
            return false
        }

        if (bezier.every((v, i) => v === keyFrame.bezier[i])) {
            return false
        }

        const before = keyFrame.bezier
        keyFrame.bezier = bezier

        this.changes.update(keyFrameId, 'bezier', new Change({
            before,
            after: keyFrame.bezier
        }))

        const easingType = Object.entries(easePoints).find(([, points]) => arrEquals(bezier, points))
        if (easingType === undefined) {
            this.setKeyFrameEasingType(keyFrameId, EasingType.CUSTOM, false)
        } else {
            this.setKeyFrameEasingType(keyFrameId, Number(easingType[0]), false)
        }

        if (fire) {
            this.fire()
        }

        return true
    }

    setPresetEasingType(presetId, easingType, fire = true) {
        const animationPreset = this.getAnimationPreset(presetId)
        if (!animationPreset) {
            return false
        }

        if (!Object.values(EasingType).includes(easingType)) {
            return false
        }

        if (animationPreset.easingType === easingType) {
            return false
        }

        this.updateAnimationPreset(presetId, { easingType }, fire)

        this.setPresetBezier(presetId, easePoints[easingType], false)

        if (fire) {
            this.fire()
        }
        return true
    }

    setPresetBezier(presetId, bezier, fire = true) {
        const animationPreset = this.getAnimationPreset(presetId)
        if (!animationPreset) {
            return false
        }

        if (!Array.isArray(bezier) || bezier.length !== 4) {
            return false
        }

        if (bezier.every((v, i) => v === animationPreset.bezier[i])) {
            return false
        }

        this.updateAnimationPreset(presetId, { bezier }, fire)

        const easingType = Object.entries(easePoints).find(([, points]) => arrEquals(bezier, points))
        if (easingType === undefined) {
            this.setPresetEasingType(presetId, EasingType.CUSTOM, false)
        } else {
            this.setPresetEasingType(presetId, Number(easingType[0]), false)
        }

        if (fire) {
            this.fire()
        }
        return true
    }

    setKeyFrameValue(keyFrameId, value, fire = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return false
        }

        if (keyFrame.value === value) {
            return false
        }

        const before = keyFrame.value
        keyFrame.value = value

        this.changes.update(keyFrameId, 'value', new Change({
            before,
            after: keyFrame.value
        }))

        if (keyFrame.frameType !== FrameType.EXPLICIT) {
            const before = keyFrame.frameType
            keyFrame.frameType = FrameType.EXPLICIT
            this.changes.update(keyFrameId, 'frameType', new Change({
                before,
                after: keyFrame.frameType
            }))
        }

        if (fire) {
            this.fire()
        }

        return true
    }

    _setTriggerSelector(triggerId, newSelector) {
        const trigger = this.getTrigger(triggerId)
        if (!trigger) return false

        const before = { ...trigger.selector }
        trigger.selector = newSelector

        this.changes.update(
            triggerId,
            'selector',
            new Change({ before, after: { ...newSelector } })
        )
    }

    cloneKeyFrame(keyFrameId, time, fire = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)

        if (!keyFrame) {
            return false
        }

        if (keyFrame.time === time || time > MAX_DURATION_TIME) {
            return false
        }

        const keyFrameList = this.getKeyFrameList(keyFrame.trackId)
        const before = keyFrameList.slice()

        // delete the origin KeyFrame which at the specified time
        const existKeyFrame = this.getKeyFrameByTime(keyFrame.trackId, time, keyFrame.id)
        if (existKeyFrame) {
            this._deleteKeyFrame(existKeyFrame.id)
        }

        const newKeyFrame = this._cloneKeyFrame({ ...keyFrame, time }, keyFrame.trackId)
        const index = keyFrameList.length
        keyFrameList.push(newKeyFrame.id)

        this.changes.update(
            keyFrame.trackId,
            'keyFrameList',
            new Change({
                before,
                after: keyFrameList,
                index
            })
        )

        if (fire) {
            this.fire()
        }

        return newKeyFrame.id
    }

    deleteKeyFrame(keyFrameId, fire = true) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        if (!keyFrame) {
            return false
        }

        this._deleteKeyFrame(keyFrameId)
        this.dataStore.selection.removeKFs([keyFrameId], { commit: false })
        if (fire) {
            this.fire()
        }

        return true
    }

    deleteKeyFrameByElementProp(elementId, propKey, typeKey, time = this._getTransitionTime()) {
        const responseId = this._getCurrentResponseId()
        const elementTrackMap = this.getElementTrackMap(responseId)
        const elementTrackId = elementTrackMap.get(elementId)
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) { return }

        const propertyKey = generatePropertyTrackKey(
            propKey,
            typeKey
        )
        const trackId = elementTrack.propertyTrackMap.get(propertyKey)
        const track = this.getPropertyTrack(trackId)
        if (!track) { return }

        const kf = this.getKeyFrameByTime(track.id, time)
        if (kf) {
            this.deleteKeyFrame(kf.id)
        }
    }

    deleteTriggerSelector(triggerId) {
        const trigger = this.getTrigger(triggerId)
        if (!trigger) return false

        const before = { ...trigger.selector }
        trigger.selector = null

        this.changes.update(
            triggerId,
            'selector',
            new Change({ before, after: null })
        )
        this.fire()
        return true
    }

    setResponseLooping(responseId, looping) {
        const response = this.getResponse(responseId)
        if (!response) return false

        if (response.looping === looping) {
            return false
        }

        const before = response.looping

        response.looping = looping

        this.changes.update(
            responseId,
            'looping',
            new Change({ before, after: looping })
        )
        this.fire()
        return true
    }

    /**
     * Set Action looping
     * @param {string} actionId
     * @param {boolean} looping
     * @returns {boolean}
     */
    setActionLooping(actionId, looping) {
        console.warn('setActionLooping is deprecated, use setResponseLooping instead')
        const responseId = this.getFirstResponseIdByActionId(actionId)
        return this.setResponseLooping(responseId, looping)
    }

    setResponseSpeed(responseId, speed) {
        const response = this.getResponse(responseId)
        if (!response) return false

        if (response.speed === speed) {
            return false
        }

        const before = response.speed
        response.speed = speed

        this.changes.update(
            responseId,
            'speed',
            new Change({ before, after: speed })
        )
        this.fire()
        return true
    }

    /**
     * Set Action speed
     * @param {string} actionId
     * @param {number} speed
     * @returns {boolean}
     */
    setActionSpeed(actionId, speed) {
        console.warn('setActionSpeed is deprecated, use setResponseSpeed instead')
        const responseId = this.getFirstResponseIdByActionId(actionId)
        return this.setResponseSpeed(responseId, speed)
    }


    _getOrCreateElementTrack(elementId) {
        const responseId = this._getCurrentResponseId()
        const elementTrackMap = this.getElementTrackMap(responseId)
        const elementTrackId = elementTrackMap.get(elementId)
        let elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            elementTrack = this._createElementTrack(responseId, elementId)
            const response = this.getResponse(responseId)
            const before = new Map(response.elementTrackMap)
            response.elementTrackMap.set(elementId, elementTrack.id)
            this.changes.update(response.id, 'elementTrackMap', new Change({ before, after: new Map(response.elementTrackMap) }))
        }
        return elementTrack
    }

    /**
     * @param {ElementTrack} elementTrack
     * @param {string[]} trackKeyList
     * @returns
     */
    _createPropertyTrackRecursive(elementTrack, trackKeyList) {
        const trackList = []
        trackKeyList.reduce((child, key) => {
            const trackId = elementTrack.propertyTrackMap.get(key)
            let track = this.getPropertyTrack(trackId)
            if (!track) {
                track = this._createPropertyTrack(elementTrack.id, key)
                const before = new Map(elementTrack.propertyTrackMap)
                elementTrack.propertyTrackMap.set(track.key, track.id)
                this.changes.update(elementTrack.id, 'propertyTrackMap', new Change({ before, after: new Map(elementTrack.propertyTrackMap) }))
            }
            if (child) {
                if (!track.children.has(child.key)) {
                    child.parentId = track.id
                    const before = new Set(track.children)
                    track.children.add(child.key)
                    this.changes.update(track.id, 'children', new Change({ before, after: new Set(track.children) }))
                }
            }
            trackList.push(track)
            return track
        }, null)
        return trackList
    }

    _upsertRefKeyFrame(track, time, value, paintType, propKey, frameType = FrameType.EXPLICIT) {
        let kf = this.getKeyFrameByTime(track.id, time)
        if (kf) {
            if (kf.frameType !== frameType) {
                this.setKeyFrameFrameType(kf.id, frameType)
            }
            this._updateKeyFrameRefData(kf.id, { [propKey]: value }, false)
        } else {
            let paintId = ''
            let createGradient = false
            let refId = ''
            let computedLayer = null
            const layerId = track.key.split('.')[0]
            const layer = this.dataStore.library.getComponent(layerId)
            if (propKey === 'gradientStops' || propKey === 'gradientTransform') {
                createGradient = true
            }
            if (createGradient || propKey === 'paintType') {
                const elementTrack = this.getElementTrack(track.elementTrackId)
                const element = this.dataStore.getById(elementTrack.elementId)
                computedLayer = element.computedStyle.getComputedLayerById(layerId)
                const firstKF = this.getKeyFrame(track.keyFrameList[0])
                if (firstKF) {
                    refId = firstKF ? firstKF.ref : layer.paintId
                    if (this.dataStore.library.getComponent(refId).paintType !== PaintType.SOLID) {
                        createGradient = true
                    }
                }
            }

            if (createGradient) {
                const gradientData = {
                    gradientStops: propKey === 'gradientStops' ? value : computedLayer.get('gradientStops'),
                    gradientTransform: propKey === 'gradientTransform' ? value : computedLayer.get('gradientTransform')
                }
                if (refId && computedLayer) {
                    paintId = this.dataStore.library.cloneComponent(refId, NO_FIRE, gradientData)
                } else {
                    paintId = this.dataStore.library.addProperty(
                        PropComponentType.PAINT,
                        {
                            paintType,
                            ...gradientData
                        },
                        false
                    )
                }
            } else {
                paintId = this.dataStore.library.addProperty(
                    PropComponentType.PAINT,
                    {
                        paintType,
                        [propKey]: value
                    },
                    false
                )
            }
            const animatable = paintType !== PaintType.IMAGE && !nonAnimatableKeySet.has(propKey)
            kf = this._createKeyFrame(track.id, { time, ref: paintId, animatable, frameType })
            const before = track.keyFrameList.slice()

            // find index by time
            const index = track.keyFrameList.length === 0
                ? 0
                : findIndexToInsert(0, track.keyFrameList.length - 1, time, idx =>
                    this.getKeyFrame(track.keyFrameList[idx]).time
                )

            track.keyFrameList.splice(index, 0, kf.id)
            this.changes.update(
                track.id,
                'keyFrameList',
                new Change({
                    before,
                    after: track.keyFrameList.slice(),
                    index
                })
            )
        }
        return kf
    }

    _getFullUpdateValue(key, particalValue, kfValue = undefined) {
        let mergedValue = particalValue
        switch (key) {
            case 'motionPath': {
                mergedValue = { ...DEFAULT_MOTION_PATH_VALUE, ...kfValue, ...particalValue }
                break
            }
        }
        return mergedValue
    }

    upsertKeyframeToPropertyTrack(track, { time, value, delta = false, frameType = FrameType.EXPLICIT, easingType, bezier }) {
        let kf = this.getKeyFrameByTime(track.id, time)
        const unit = this._getPropUnit(track)

        if (kf) {
            const newValue = this._getFullUpdateValue(track.key, value, kf.value)
            if (kf.frameType !== frameType) {
                this.setKeyFrameFrameType(kf.id, frameType)
            }

            this.setKeyFrameEasingType(kf.id, easingType, false)
            this._updateKeyFrameData(kf.id, { value: newValue, delta, unit })

        } else {
            const newValue = this._getFullUpdateValue(track.key, value)
            const animatable = !nonAnimatableKeySet.has(track.key)
            kf = this._createKeyFrame(track.id, { time, value: newValue, unit, delta, animatable, frameType, easingType, bezier })
            const before = track.keyFrameList.slice()

            // find index by time
            const index = track.keyFrameList.length === 0
                ? 0
                : findIndexToInsert(0, track.keyFrameList.length - 1, time, idx =>
                    this.getKeyFrame(track.keyFrameList[idx]).time
                )

            track.keyFrameList.splice(index, 0, kf.id)
            this.changes.update(
                track.id,
                'keyFrameList',
                new Change({
                    before,
                    after: track.keyFrameList.slice(),
                    index
                })
            )
        }
        return kf
    }

    _getPropUnit(track) {
        let unit = Unit.PIXEL
        const unitChange = AVAILABLE_UNIT_CHANGE.get(track.key)
        if (unitChange) {
            const elementTrack = this.getElementTrack(track.elementTrackId)
            const element = this.dataStore.getElement(elementTrack.elementId)
            const component = this.dataStore.library.getComponent(element.base[track.key])
            unit = component[unitChange]
        }

        return unit
    }

    /**
     * Only for non-base layer
     * @param {string} elementId
     * @param {*} layerType
     * @returns
     */
    addLayer(elementId, layerType) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        const layerListKey = LayerTypeMapLayerListKey[layerType]

        // use ID as key for layer track
        const trackKeyList = [undefined, layerListKey]
        const [layerTrack] = this._createPropertyTrackRecursive(elementTrack, trackKeyList)

        const opacityTrack = this._createPropertyTrack(elementTrack.id, 'opacity', layerTrack.key)
        opacityTrack.parentId = layerTrack.id

        let before = new Set(layerTrack.children)
        layerTrack.children.add(opacityTrack.key)
        this.changes.update(
            layerTrack.id,
            'children',
            new Change({
                before,
                after: new Set(layerTrack.children)
            })
        )

        before = new Map(elementTrack.propertyTrackMap)
        elementTrack.propertyTrackMap.set(opacityTrack.key, opacityTrack.id)
        this.changes.update(
            elementTrack.id,
            'propertyTrackMap',
            new Change({
                before,
                after: new Map(elementTrack.propertyTrackMap)
            })
        )

        before = elementTrack[layerListKey].slice()
        elementTrack[layerListKey].push(layerTrack.id)
        this.changes.update(
            elementTrack.id,
            layerListKey,
            new Change({
                before,
                after: elementTrack[layerListKey].slice()
            })
        )

        const time = this._getTransitionTime()
        this.upsertKeyframeToPropertyTrack(opacityTrack, { time, value: 1 })

        this.fire()

        return layerTrack.id
    }

    addAnimationPreset(elementId, { presetType, presetDuration, presetStartTime }) {
        const presetData = this.generateAnimationPresetData(
            elementId,
            presetType,
            presetStartTime,
            presetDuration
        )

        const presetEndTime = presetData.startTime + presetData.duration

        if (presetEndTime > MAX_DURATION_TIME ) {
            return
        }

        const elementTrack = this._getOrCreateElementTrack(elementId)
        const presetName = AnimationPresetTypeKeyMap[presetType]
        const presetTrack = this._createAnimationPreset(elementTrack, presetName, presetData)

        let before = new Map(elementTrack.propertyTrackMap)
        elementTrack.propertyTrackMap.set(presetName, presetTrack.id)
        this.changes.update(
            elementTrack.id,
            'propertyTrackMap',
            new Change({
                before: before,
                after: new Map(elementTrack.propertyTrackMap)
            })
        )

        before = elementTrack.presets.slice()
        elementTrack.presets.push(presetTrack.id)
        this.changes.update(
            elementTrack.id,
            'presets',
            new Change({
                before: before,
                after: elementTrack.presets.slice()
            })
        )

        this.fire()
        return presetTrack
    }

    updatePresetEffect(elementId, data) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        for (const [key, trackId] of elementTrack.propertyTrackMap.entries()) {
            if (key === 'trimIn' || key === 'trimOut') {
                if ('mode' in data) {
                    this.updateAnimationPreset(trackId, {'mode': data.mode}, false)
                }
            }
        }
        this.fire()
    }

    hasAnimationPreset(elementId) {
        const elementTrackId = this.getElementTrackIdByElementId(elementId)
        const elementTrack = this.getElementTrack(elementTrackId)
        if (elementTrack) {
            return elementTrack.presets.length > 0
        }
        return false
    }

    updateAnimationPreset(presetTrackId, updateObj, fire = true) {
        const presetTrack = this.getAnimationPreset(presetTrackId)
        if (!presetTrack) return

        const elementTrack = this.getElementTrack(presetTrack.elementTrackId)
        const keyEndsWithIn = presetTrack.key.toLowerCase().endsWith('in')

        for (const [key, value] of Object.entries(updateObj)) {
            if (presetTrack[key] === value) continue

            const before = presetTrack[key]

            // the value of these keys is boolean from the UI toggle
            if (key === 'opacity' || key === 'scaleX' || key === 'scaleY') {
                const element = this.dataStore.getById(elementTrack.elementId)
                const computedVal = this.dataStore.transition.getPropertyValueByTime(elementTrack.elementId, key, presetTrack.startTime)?.key ?? element.get(key)
                if (keyEndsWithIn) {
                    presetTrack[key] = value ? 0 : null
                    presetTrack[`${key}To`] = value ? computedVal : null
                } else {
                    presetTrack[key] = value ? computedVal : null
                    presetTrack[`${key}To`] = value ? 0 : null
                }

                this.changes.update(
                    presetTrack.id,
                    key,
                    new Change({
                        before,
                        after: presetTrack[key]
                    })
                )
                this.changes.update(
                    presetTrack.id,
                    `${key}To`,
                    new Change({
                        before,
                        after: presetTrack[`${key}To`]
                    })
                )
            } else if (key === 'direction') {
                switch (presetTrack.presetType) {
                    case AnimationPresetType.MOVE_IN:
                    case AnimationPresetType.MOVE_OUT: {
                        const { motionPath, motionPathTo } = presetTrack

                        let fixedAxisVal = 0
                        let deltaAxisVal = 0
                        // vertical direction
                        if (motionPath.pos[0] === motionPathTo.pos[0]) {
                            fixedAxisVal = motionPath.pos[0]
                            deltaAxisVal = keyEndsWithIn ? Math.abs(motionPath.pos[1]) : Math.abs(motionPathTo.pos[1])
                        } else { // horizontal direction
                            fixedAxisVal = motionPath.pos[1]
                            deltaAxisVal = keyEndsWithIn ? Math.abs(motionPath.pos[0]) : Math.abs(motionPathTo.pos[0])
                        }

                        const beforeMotion = keyEndsWithIn ? { ...motionPath } : { ...motionPathTo }
                        if (value === 'bottom' || value === 'top') {
                            if (keyEndsWithIn) {
                                presetTrack.motionPath.pos[0] = fixedAxisVal
                                presetTrack.motionPath.pos[1] = value === 'bottom' ? -deltaAxisVal : deltaAxisVal
                            } else {
                                presetTrack.motionPathTo.pos[0] = fixedAxisVal
                                presetTrack.motionPathTo.pos[1] = value === 'top' ? -deltaAxisVal : deltaAxisVal
                            }
                        } else if (value === 'left' || value === 'right') {
                            if (keyEndsWithIn) {
                                presetTrack.motionPath.pos[0] = value === 'right' ? -deltaAxisVal : deltaAxisVal
                                presetTrack.motionPath.pos[1] = fixedAxisVal
                            } else {
                                presetTrack.motionPathTo.pos[0] = value === 'left' ? -deltaAxisVal : deltaAxisVal
                                presetTrack.motionPathTo.pos[1] = fixedAxisVal
                            }
                        }
                        this.changes.update(
                            presetTrack.id,
                            keyEndsWithIn ? 'motionPath' : 'motionPathTo',
                            new Change({
                                before: beforeMotion,
                                after: keyEndsWithIn ? presetTrack.motionPath : presetTrack.motionPathTo
                            })
                        )
                        break
                    }
                    case AnimationPresetType.SPIN_IN:
                    case AnimationPresetType.SPIN_OUT: {
                        const { rotation, rotationTo } = presetTrack
                        const beforeRotation = keyEndsWithIn ? { ...rotation } : { ...rotationTo }

                        const startRotation = keyEndsWithIn ? rotationTo : rotation
                        const deltaRotation = keyEndsWithIn ? Math.abs(rotation) : Math.abs(rotationTo)

                        if (value === 'counterclockwise') {
                            if (keyEndsWithIn) {
                                presetTrack.rotation = startRotation + deltaRotation
                            } else {
                                presetTrack.rotationTo = startRotation + deltaRotation
                            }
                        } else {
                            if (keyEndsWithIn) {
                                presetTrack.rotation = startRotation - deltaRotation
                            } else {
                                presetTrack.rotationTo = startRotation - deltaRotation
                            }
                        }
                        this.changes.update(
                            presetTrack.id,
                            keyEndsWithIn ? 'rotation' : 'rotationTo',
                            new Change({
                                before: beforeRotation,
                                after: keyEndsWithIn ? presetTrack.rotation : presetTrack.rotationTo
                            })
                        )
                        break
                    }
                }

                presetTrack[key] = value
            } else if (key === 'distance') {
                switch (presetTrack.presetType) {
                    case AnimationPresetType.MOVE_IN:
                    case AnimationPresetType.MOVE_OUT: {
                        const { direction, motionPath, motionPathTo } = presetTrack

                        const beforeMotion = keyEndsWithIn ? { ...motionPath } : { ...motionPathTo }
                        const diffDistance = value - before

                        if (direction === 'left' || direction === 'right') {
                            if (keyEndsWithIn) {
                                presetTrack.motionPath.pos[0] = direction === 'right' ? motionPath.pos[0] - diffDistance : motionPath.pos[0] + diffDistance
                            } else {
                                presetTrack.motionPathTo.pos[0] = direction === 'right' ? motionPathTo.pos[0] + diffDistance : motionPathTo.pos[0] - diffDistance
                            }
                        } else if (direction === 'top' || direction === 'bottom') {
                            if (keyEndsWithIn) {
                                presetTrack.motionPath.pos[1] = direction === 'bottom' ? motionPath.pos[1] - diffDistance : motionPath.pos[1] + diffDistance
                            } else {
                                presetTrack.motionPathTo.pos[1] = direction === 'bottom' ? motionPathTo.pos[1] + diffDistance : motionPathTo.pos[1] - diffDistance
                            }
                        }

                        this.changes.update(
                            presetTrack.id,
                            keyEndsWithIn ? 'motionPath' : 'motionPathTo',
                            new Change({
                                before: beforeMotion,
                                after: keyEndsWithIn ? presetTrack.motionPath : presetTrack.motionPathTo
                            })
                        )
                        break
                    }
                }

                presetTrack[key] = value
                this.changes.update(
                    presetTrack.id,
                    key,
                    new Change({
                        before,
                        after: presetTrack[key]
                    })
                )
            } else if (key === 'rotation') {
                const { direction, rotation, rotationTo } = presetTrack

                const beforeRotation = keyEndsWithIn ? rotation : rotationTo
                const baseRotation = keyEndsWithIn ? rotationTo : rotation

                if (keyEndsWithIn) {
                    presetTrack.rotation = direction === 'clockwise' ? baseRotation - value : baseRotation + value
                } else {
                    presetTrack.rotationTo = direction === 'clockwise' ? baseRotation + value : baseRotation - value
                }

                this.changes.update(
                    presetTrack.id,
                    keyEndsWithIn ? 'rotation' : 'rotationTo',
                    new Change({
                        before: beforeRotation,
                        after: keyEndsWithIn ? presetTrack.rotation : presetTrack.rotationTo
                    })
                )
            } else {
                presetTrack[key] = value
                this.changes.update(
                    presetTrack.id,
                    key,
                    new Change({
                        before,
                        after: presetTrack[key]
                    })
                )
            }
        }

        if (fire) {
            this.fire()
        }
    }

    deleteSelectedAnimationPresets(fire = true) {
        const presetSelection = new Set(this.dataStore.selection.get('segments'))
        presetSelection.forEach(presetId => {
            this.deleteAnimationPreset(presetId, fire)
        })
        this.dataStore.selection.clearSegments()
    }

    deleteAnimationPreset(presetTrackId, fire = true) {
        const animationPreset = this.getAnimationPreset(presetTrackId)
        const elementTrack = this.getElementTrack(animationPreset?.elementTrackId)
        if (!elementTrack) return

        let before = new Map(elementTrack.propertyTrackMap)
        elementTrack.propertyTrackMap.delete(animationPreset.key)
        this.changes.update(
            elementTrack.id,
            'propertyTrackMap',
            new Change({
                before,
                after: new Map(elementTrack.propertyTrackMap)
            })
        )

        before = elementTrack.presets.slice()
        elementTrack.presets = elementTrack.presets.filter(id => id !== presetTrackId)
        this.changes.update(
            elementTrack.id,
            'presets',
            new Change({
                before,
                after: elementTrack.presets.slice()
            })
        )

        if (elementTrack.propertyTrackMap.size === 0) {
            this._deleteElementTrack(animationPreset.elementTrackId)
        }

        this.dataStore.selection.removeSegments([presetTrackId], {commit: false})

        this.changes.delete([presetTrackId])
        this._delete(presetTrackId)
        if (fire){
            this.fire()
        }
    }

    generateAnimationPresetData(elementId, presetType, startTime, duration) {
        let data = {
           startTime,
           duration,
           presetType,
        }
        const element = this.dataStore.getById(elementId)
        switch (presetType) {
            case AnimationPresetType.FADE_IN:
            case AnimationPresetType.FADE_OUT: {
                const opacityComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'opacity', startTime)?.opacity ?? element.get('opacity')
                data = {
                    ...data,
                    opacity: presetType === AnimationPresetType.FADE_IN ? 0 : opacityComputedVal,
                    opacityTo: presetType === AnimationPresetType.FADE_IN ? opacityComputedVal : 0,
                }
                break
            }
            case AnimationPresetType.MOVE_IN:
            case AnimationPresetType.MOVE_OUT: {
                const distance = Math.max(50, Math.round(element.get('width') * 1.5 * 100) / 100)
                const opacityComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'opacity', startTime)?.opacity ?? element.get('opacity')

                const baseTranslate = element.getBaseValue('translate')
                const motionPathComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'motionPath', startTime)

                let startPos = [0, 0]
                if (motionPathComputedVal.translateX && motionPathComputedVal.translateY) {
                    startPos = [
                        motionPathComputedVal.translateX - baseTranslate.translateX,
                        motionPathComputedVal.translateY - baseTranslate.translateY
                    ]
                }

                const deltaPos = startPos.slice()
                // default is move left to right
                if (presetType === AnimationPresetType.MOVE_IN) {
                    deltaPos[0] = startPos[0] - distance
                } else {
                    deltaPos[0] = startPos[0] + distance
                }

                const startMotionPoint = {
                    in: [0, 0],
                    out: [0, 0],
                    mirror: 0,
                    pos: startPos
                }

                const deltaMotionPoint = {
                    in: [0, 0],
                    out: [0, 0],
                    mirror: 0,
                    pos: deltaPos
                }

                data = {
                    ...data,
                    direction: 'right',
                    opacity: presetType === AnimationPresetType.MOVE_IN ? 0 : opacityComputedVal,
                    opacityTo: presetType === AnimationPresetType.MOVE_IN ? opacityComputedVal : 0,
                    motionPath: presetType === AnimationPresetType.MOVE_IN ? deltaMotionPoint : startMotionPoint,
                    motionPathTo: presetType === AnimationPresetType.MOVE_IN ? startMotionPoint : deltaMotionPoint,
                    distance,
                }
                break
            }
            case AnimationPresetType.SCALE_IN:
            case AnimationPresetType.SCALE_OUT: {
                const opacityComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'opacity', startTime)?.opacity ?? element.get('opacity')
                const scaleXComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'scaleX', startTime)?.scaleX ?? element.get('scaleX')
                const scaleYComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'scaleY', startTime)?.scaleY ?? element.get('scaleY')
                data = {
                    ...data,
                    opacity: presetType === AnimationPresetType.SCALE_IN ? 0 : opacityComputedVal,
                    scaleX: presetType === AnimationPresetType.SCALE_IN ? 0 : scaleXComputedVal,
                    scaleY: presetType === AnimationPresetType.SCALE_IN ? 0 : scaleYComputedVal,
                    opacityTo: presetType === AnimationPresetType.SCALE_IN ? opacityComputedVal : 0,
                    scaleXTo: presetType === AnimationPresetType.SCALE_IN ? scaleXComputedVal : 0,
                    scaleYTo: presetType === AnimationPresetType.SCALE_IN ? scaleYComputedVal : 0
                }
                break
            }
            case AnimationPresetType.SPIN_IN:
            case AnimationPresetType.SPIN_OUT: {
                const opacityComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'opacity', startTime)?.opacity ?? element.get('opacity')
                const scaleXComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'scaleX', startTime)?.scaleX ?? element.get('scaleX')
                const scaleYComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'scaleY', startTime)?.scaleY ?? element.get('scaleY')
                const rotationComputedVal = this.dataStore.transition.getPropertyValueByTime(elementId, 'rotation', startTime)?.rotation ?? element.get('rotation')
                data = {
                    ...data,
                    direction: 'clockwise',
                    opacity: presetType === AnimationPresetType.SPIN_IN ? 0 : opacityComputedVal,
                    scaleX: presetType === AnimationPresetType.SPIN_IN ? 0 : scaleXComputedVal,
                    scaleY: presetType === AnimationPresetType.SPIN_IN ? 0 : scaleYComputedVal,
                    opacityTo: presetType === AnimationPresetType.SPIN_IN ? opacityComputedVal : 0,
                    scaleXTo: presetType === AnimationPresetType.SPIN_IN ? scaleXComputedVal : 0,
                    scaleYTo: presetType === AnimationPresetType.SPIN_IN ? scaleYComputedVal : 0,
                    rotation: presetType === AnimationPresetType.SPIN_IN ? rotationComputedVal - toRad(125) : rotationComputedVal,
                    rotationTo: presetType === AnimationPresetType.SPIN_IN ? rotationComputedVal : rotationComputedVal + toRad(125),
                }
                break
            }
            case AnimationPresetType.TRIM_IN:
            case AnimationPresetType.TRIM_OUT: {
                const trimPath = Array.from(element.computedStyle.effects).find(ce => ce.type === EffectType.TRIM_PATH)
                data = {
                    ...data,
                    direction: 'forward',
                    mode: trimPath ? trimPath.get('mode') : 0,
                    start: 0,
                    end: presetType === AnimationPresetType.TRIM_IN ? 0 : 100,
                }
                break
            }
        }

        return data
    }

    reorderLayer(elementId, layerKey, toIndex) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        const trackId = elementTrack.propertyTrackMap.get(layerKey)
        const track = this.getPropertyTrack(trackId)
        const layerListTrack = this.getPropertyTrack(track.parentId)
        const layerListKey = layerListTrack.key

        const fromIndex = elementTrack[layerListKey].indexOf(track.id)
        const before = elementTrack[layerListKey].slice()
        this._reorderList(elementTrack[layerListKey], fromIndex, toIndex)
        this.changes.update(
            elementTrack.id,
            layerListKey,
            new Change({
                before,
                after: elementTrack[layerListKey].slice(),
                fromIndex,
                toIndex
            })
        )
    }

    _updateKeyFrameRefData(keyFrameId, data, fire) {
        const keyFrame = this.getKeyFrame(keyFrameId)
        const paintId = keyFrame.ref
        this.changes.update(keyFrame.id, 'ref', new Change({
            before: keyFrame.ref, after: keyFrame.ref
        }))
        this.dataStore.library.setProperty(paintId, data, true, fire)
    }

    _updateKeyFrameData(keyFrameId, { value, delta = false, unit = Unit.PIXEL }) {
        const kf = this.getKeyFrame(keyFrameId)
        if (kf.value !== value) {
            const before = kf.value
            kf.value = value
            this.changes.update(kf.id, 'value', new Change({ before, after: value }))
        }
        if (kf.delta !== delta) {
            const before = kf.delta
            kf.delta = delta
            this.changes.update(kf.id, 'delta', new Change({ before, after: delta }))
        }
        if (kf.unit !== unit) {
            const before = kf.unit
            kf.unit = unit
            this.changes.update(kf.id, 'unit', new Change({ before, after: unit }))
        }
    }

    setEffect(elementId, effectId, propKey, value, frameType) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        const effect = this.dataStore.library.getComponent(effectId)
        const effectKey = EFFECT_TYPE_NAME_MAP[effect.effectType]
        const propertyTrackKey = generatePropertyTrackKey(propKey, effectKey)
        const time = this._getTransitionTime()
        const trackId = elementTrack.propertyTrackMap.get(propertyTrackKey)
        let propertyTrack = this.getPropertyTrack(trackId)
        if (!propertyTrack) {
            const trackKeyList = [propertyTrackKey, effectKey]
            propertyTrack = this._createPropertyTrackRecursive(elementTrack, trackKeyList)[0]
        }
        const keyframe = this.upsertKeyframeToPropertyTrack(propertyTrack, { time, value, delta: false, frameType })

        this.fire()
        return keyframe.id
    }

    /**
     * @param {string} elementId
     * @param {*} layerKey
     * @param {*} propKey
     * @param {*} value
     * @param {*} meta
     * @param {*} frameType
     * @returns
     */
    setLayer(elementId, layerKey, propKey, value, meta, frameType) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        const propertyTrackKey = generatePropertyTrackKey(propKey, layerKey)
        const time = this._getTransitionTime()
        const trackId = elementTrack.propertyTrackMap.get(propertyTrackKey)

        let propertyTrack = this.getPropertyTrack(trackId)
        const newValue = value
        if (!propertyTrack) {
            const layerPropKey = generateLayerPropertyTrackKey(propKey, layerKey)
            /**
             * [layerPropKey, layerKey, LayerTypeMapLayerListKey[meta.layerType]]
             */
            const trackKeyList = [layerPropKey, layerKey, LayerTypeMapLayerListKey[meta.layerType]]
            propertyTrack = this._createPropertyTrackRecursive(elementTrack, trackKeyList)[0]
        }

        this._updateRelatedKeyFrameList(propertyTrack, propKey, newValue)

        let kf = this.getKeyFrameByTime(propertyTrack.id, time)
        if (!kf || propKey !== 'paintType') {
            if (isPaintPropKey(propKey)) {
                kf = this._upsertRefKeyFrame(propertyTrack, time, newValue, meta.paintType, propKey, frameType)
            } else {
                kf = this.upsertKeyframeToPropertyTrack(propertyTrack, { time, value: newValue, delta: false, frameType })
            }
        }

        this.fire()
        return kf.id
    }

    upsertKeyframeToElement(element, propertyKey, keyframeData) {
        if (generalChildKeySet.has(propertyKey)) {
            return this._upsertGeneralKeyframeProperty(element.get('id'), propertyKey, keyframeData)
        } else if (effectChildKeySet.has(propertyKey)) {
            return this._upsertEffectKeyframeProperty(element, propertyKey, keyframeData)
        } else {
            console.warn(`Keyframe property ${propertyKey} is not allowed to be pasted.`)
            return null
        }
    }

    _upsertGeneralKeyframeProperty(elementId, propertyKey, keyframeData) {
        const generalPropertyGroupKey = GENERAL_PROPERTY_GROUP_MAP[propertyKey]

        return this._upsertElementKeyframe(elementId, keyframeData, [propertyKey, generalPropertyGroupKey])
    }

    _upsertEffectKeyframeProperty(element, propertyKey, keyframeData) {
        const [effectTypeKey] = propertyKey.split('.')
        const effectTypeValue = EFFECT_TYPE_VALUE_MAP[effectTypeKey]

        if (!canElementApplyEffect(element, effectTypeValue)) {
            return null
        }

        this.dataStore.library.addEffect(element.base.effects[0], this.dataStore.editor.effectList.length, { effectType: effectTypeValue })
        return this._upsertElementKeyframe(element.get('id'), keyframeData, [propertyKey, effectTypeKey])
    }

    _upsertElementKeyframe(elementId, keyframeData, propertyTrackKeyList) {
        const elementTrack = this._getOrCreateElementTrack(elementId)
        const [newPropertyTrack] = this._createPropertyTrackRecursive(elementTrack, propertyTrackKeyList)
        return this.upsertKeyframeToPropertyTrack(newPropertyTrack, keyframeData)
    }

    /**
     * Update related KeyFrame list
     * @param {PropertyTrack} track
     * @param {string} propKey
     * @param {number|object} newValue
     */
    _updateRelatedKeyFrameList(track, propKey, newValue) {
        switch (propKey) {
            case 'paintType': {
                const existsKeyFrameList = this.getKeyFrameList(track.id)
                existsKeyFrameList.forEach(keyFrameId => {
                    const keyFrame = this.getKeyFrame(keyFrameId)
                    const paintComponent = this.dataStore.library.getComponent(keyFrame.ref)
                    const oldPaintType = paintComponent.paintType
                    const paintData = { [propKey]: newValue }
                    // Should also update ref component gradient and color if change paintType
                    if (newValue === PaintType.SOLID && GRADIENT_PAINT_SET.has(oldPaintType)) {
                        const firstGradientStopColor = paintComponent.gradientStops[0].color
                        paintData.color = [...firstGradientStopColor]
                    } else if (oldPaintType === PaintType.SOLID && GRADIENT_PAINT_SET.has(newValue)) {
                        const [r, g, b] = paintComponent.color
                        paintData.gradientStops = [
                            { color: [r, g, b, 1], position: 0 },
                            { color: [r, g, b, 0], position: 1 }
                        ]
                    }
                    this._updateKeyFrameRefData(keyFrameId, paintData, false)
                })
                break
            }
            case 'origin': {
                const existsKeyFrameList = this.getKeyFrameList(track.id)
                const elementId = this.getElementTrack(track.elementTrackId).elementId
                existsKeyFrameList.forEach(keyFrameId => {
                    const keyFrame = this.getKeyFrame(keyFrameId)
                    // If kf unit is the same as new unit, then no need to change.
                    if (keyFrame.unit === newValue.originXUnit) {
                        return
                    }

                    const widthData = this.dataStore.transition.getPropertyValueByTime(elementId, 'width', keyFrame.time)
                    const heightData = this.dataStore.transition.getPropertyValueByTime(elementId, 'height', keyFrame.time)
                    const newData = {
                        originXUnit: newValue.originXUnit,
                        originYUnit: newValue.originYUnit
                    }

                    switch (newValue.originXUnit) {
                        case Unit.PIXEL:
                            newData.originX = keyFrame.value.originX * widthData.width / 100
                            newData.originY = keyFrame.value.originY * heightData.height / 100
                            break
                        case Unit.PERCENT:
                            newData.originX = (keyFrame.value.originX / widthData.width) * 100
                            newData.originY = (keyFrame.value.originY / heightData.height) * 100
                            break
                    }

                    this._updateKeyFrameData(keyFrameId, { value: newData, delta: false, unit: newValue.originXUnit })
                })
                break
            }
        }
    }

    /**
     * Update KeyFrame list with unit has changed
     * @param {string} elementId
     * @param {Map} changes
     */
    updateKeyFrameListWithUnitChange(elementId, changes) {
        const changePropsMap = new Map()
        ALWAYS_UPDATE_WITH_UNIT_CHANGE.forEach((dataKey) => {
            const data = changes.get(dataKey)
            const propKey = UNIT_CHANGE_PROPS_MAP.get(dataKey)
            if (data && !changePropsMap.has(propKey)) {
                changePropsMap.set(propKey, { [dataKey]: data.value })
            }
        })

        changePropsMap.forEach((data, propKey) => {
            const elementTrackId = this.getElementTrackIdByElementId(elementId)
            const elementTrack = this.getElementTrack(elementTrackId)
            if (!elementTrack) {
                return
            }
            const propertyTrackId = elementTrack.propertyTrackMap.get(propKey)
            const propertyTrack = this.getPropertyTrack(propertyTrackId)
            if (!propertyTrack) {
                return
            }

            this._updateRelatedKeyFrameList(propertyTrack, propKey, data)
        })
    }

    deleteLayer(elementId, layerKey, fire = true) {
        const responseId = this._getCurrentResponseId()
        if (!responseId) return false

        const elementTrackMap = this.getElementTrackMap(responseId)
        const elementTrackId = elementTrackMap.get(elementId)
        const elementTrack = this.getElementTrack(elementTrackId)

        if (!elementTrack || elementTrack.elementId !== elementId) return false

        const layerTrackId = elementTrack.propertyTrackMap.get(layerKey)
        const layerTrack = this.getPropertyTrack(layerTrackId)

        if (!layerTrack) return false

        this._deletePropertyTrack(layerTrackId)

        if (fire) {
            this.fire()
        }

        return true
    }

    setProperty(elementId, propKey, value, frameType, delta = false, options = { fire: true }) {
        const trackIds = this.setProperties([{
            elementId,
            propKey,
            delta,
            frameType,
            value
        }], options)
        return trackIds[0]
    }

    setProperties(changes, options = { fire: true }) {
        const trackIds = []
        const time = this._getTransitionTime()

        changes.forEach((change) => {
            const { elementId, propKey, value, delta = false, frameType } = change
            const elementTrack = this._getOrCreateElementTrack(elementId)
            const propertyTrackKey = generatePropertyTrackKey(propKey)
            const trackKeyList = [propertyTrackKey]
            const trackGroupKey = GENERAL_PROPERTY_GROUP_MAP[propertyTrackKey]

            if (trackGroupKey) {
                trackKeyList.push(trackGroupKey)
            }

            const [propertyTrack] = this._createPropertyTrackRecursive(elementTrack, trackKeyList)
            this.upsertKeyframeToPropertyTrack(propertyTrack, { time, value, delta, frameType })
            trackIds.push(propertyTrack.id)

            const unitChangeKey = SHOULD_UPDATE_ALL_KFS_WITH_UNIT_CHANGES.get(propertyTrackKey)
            const shouldUpdateAllKfs = unitChangeKey && unitChangeKey.some((key) => value[key] !== undefined)
            if (shouldUpdateAllKfs) {
                this._updateRelatedKeyFrameList(propertyTrack, propertyTrackKey, value)
            }
        })

        if (options.fire) {
            this.fire()
        }

        return trackIds
    }

    getChildList(entityId) {
        const entity = this.data.entityMap.get(entityId)
        if (!entity) {
            return []
        }

        if (entity.type === InteractionEntityType.ELEMENT_TRACK) {
            return childListMap.get('ROOT')
                .filter(key => entity.propertyTrackMap.has(key))
                .map(key => entity.propertyTrackMap.get(key))
        }
        if (entity.type === InteractionEntityType.PROPERTY_TRACK) {
            const elementTrack = this.getElementTrack(entity.elementTrackId)
            if (!entity.children.size) { // prop, LAYER.prop
                return []
            }
            if (childListMap.has(entity.key)) { // prop group & layer list
                if (LayerListKeySet.has(entity.key)) { // layer list
                    // keep non-exists track in the list for the index mapping
                    const element = this.dataStore.getById(elementTrack.elementId)
                    return Array.from(element.computedStyle[entity.key])
                        .map((cl) => elementTrack.propertyTrackMap.get(cl.get('layerId') || cl.get('trackId')))
                } else { // prop group
                    return childListMap.get(entity.key)
                        .filter(key => entity.children.has(key))
                        .map(key => elementTrack.propertyTrackMap.get(key))
                }
            } else { // LAYER
                return childListMap.get('LAYER')
                    .filter(key => entity.children.has(`${entity.key}.${key}`))
                    .map(key => elementTrack.propertyTrackMap.get(`${entity.key}.${key}`))
            }
        }
        if (entity.type === InteractionEntityType.ANIMATION_PRESET) {
            return []
        }
    }

    deleteEffect(elementId, effectId) {
        const effect = this.dataStore.library.getComponent(effectId)
        const effectKey = EFFECT_TYPE_NAME_MAP[effect.effectType]
        const elementTrackId = this.getElementTrackIdByElementId(elementId)
        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return
        }
        const trackId = elementTrack.propertyTrackMap.get(effectKey)

        if (trackId) {
            this._deletePropertyTrack(trackId)
        }

        if (effect.effectType === EffectType.TRIM_PATH) {
            const presetTrackId =
                elementTrack.propertyTrackMap.get(AnimationPresetTypeKeyMap[AnimationPresetType.TRIM_IN]) ||
                elementTrack.propertyTrackMap.get(AnimationPresetTypeKeyMap[AnimationPresetType.TRIM_OUT])
            if (presetTrackId) {
                this.deleteAnimationPreset(presetTrackId)
            }
        }

        this.fire()
    }

    /**
     * This function is only used when the file is corrupted and the parent property track is missing
     * @param {string} elementTrackId
     * @param {string} childKey
     * @returns {PropertyTrack}
     */
    createParentPropertyTrack(elementTrackId, childKey) {


        const parentKey = GENERAL_PROPERTY_GROUP_MAP[childKey]
        if (!parentKey) {
            return null
        }

        const elementTrack = this.getElementTrack(elementTrackId)
        if (!elementTrack) {
            return null
        }

        // Create parent track
        const parentTrack = this._createPropertyTrack(elementTrackId, parentKey)
        const before = new Map(elementTrack.propertyTrackMap)
        elementTrack.propertyTrackMap.set(parentTrack.key, parentTrack.id)
        this.changes.update(elementTrackId, 'propertyTrackMap', new Change({
            before,
            after: new Map(elementTrack.propertyTrackMap)
        }))
        // Get all possible child keys for this parent
        const childKeys = childListMap.get(parentKey) || []
        childKeys.forEach(key => {
            const childTrackId = elementTrack.propertyTrackMap.get(key)
            const childTrack = this.getPropertyTrack(childTrackId)
            if (childTrack) {
                childTrack.parentId = parentTrack.id
                this.changes.update(childTrack.id, 'parentId', new Change({
                    before: childTrack.parentId,
                    after: parentTrack.id
                }))
            }
        })
        this.fire(false)
        return parentTrack
    }

    getCurrentElementKeyframeAtTime(elementId, trackKey, time) {
        const propertyTrackMap = this.getPropertyTrackMapByElementId(elementId)
        if (!propertyTrackMap) return null

        const trackId = propertyTrackMap.get(trackKey)
        if (!trackId) return null

        const keyframeList = this.getKeyFrameList(trackId).map(keyframeId => this.getKeyFrame(keyframeId))
        return keyframeList.find(keyframe => keyframe.time === time)
    }

    getActionUsedElementIdList(actionId) {
        const elementIdSet = new Set()
        const triggerList = this.dataStore.interaction.getTriggerList(actionId)

        triggerList.forEach(triggerId => {
            const trigger = this.dataStore.interaction.getTrigger(triggerId)
            if (trigger.selector.selectorType === SelectorType.ELEMENT) {
                if (trigger.selector.value) {
                    elementIdSet.add(trigger.selector.value)
                }
            }
        })

        const responseList = this.dataStore.interaction.getResponseList(actionId)
        responseList.forEach(responseId => {
            const elementTrackMap = this.dataStore.interaction.getElementTrackMap(responseId)
            elementTrackMap.keys().forEach(elementId => {
                elementIdSet.add(elementId)
            })
        })

        return Array.from(elementIdSet)
    }


}

export default Manager

/** @typedef {string} ID */

/**
 * @typedef {object} Action
 * @property {ID} id
 * @property {InteractionEntityType.ACTION} type
 * @property {string} name
 * @property {ID} elementId
 * @property {ID[]} responseList
 * @property {ID[]} triggerList
 * @property {boolean} looping
 * @property {number} speed
 * @property {number} duration
 * @property {ID[]} transferList
 */

/** @typedef {'CLICK' | 'DOUBLE_CLICK' | 'DRAG' | 'EDGE_SWIPE' | 'FORCE_TAP' | 'HOVER' | 'KEY_PRESS' | 'LOAD' | 'LONG_PRESS' | 'MANY_CLICK' | 'MOUSE_MOVE' | 'PINCH' | 'PRESS' | 'ROTATE' | 'SCROLL' | 'SWIPE'} TriggerType */

/**
 * @typedef {object} Trigger
 * @property {ID} id
 * @property {InteractionEntityType.TRIGGER} type
 * @property {ID} actionId
 * @property {ID} elementId
 * @property {TriggerType} triggerType
 * @property {object} options
 * @property {Selector} selector
 */


/**
 * @typedef {object} Selector
 * @property {string} value
 * @property {SelectorType} selectorType
 */


/**
 * @typedef {object} Response
 * @property {ID} id
 * @property {InteractionEntityType.RESPONSE} type
 * @property {ID} actionId
 * @property {string} name
 * @property {ID[]} conditionList
 * @property {Map<ID | ID>} elementTrackMap
 * @property {number} duration
 * @property {number} speed
 * @property {boolean} looping
 */

/** @typedef {'AND' | 'OR'} Logic */
/** @typedef {'HAS_STYLE' | 'NOT_HAVE_STYLE' | 'NONE'} Operation */

/**
 * @typedef {object} Condition
 * @property {ID} id
 * @property {InteractionEntityType.CONDITION} type
 * @property {ID} responseId
 * @property {Logic} logic
 * @property {Operation} operation
 * @property {ID} elementId
 */

/**
 * @typedef {object} ElementTrack
 * @property {ID} id
 * @property {InteractionEntityType.ELEMENT_TRACK} type
 * @property {ID} responseId
 * @property {ID} elementId
 * @property {Map<ID | ID>} propertyTrackMap
 * @property {ID[]} presets
 * // layer list & presets
 */

/**
 * @typedef {object} PropertyTrack
 * @property {ID} id
 * @property {InteractionEntityType.PROPERTY_TRACK} type
 * @property {string} key
 * @property {ID} elementTrackId
 * @property {ID} parentId
 * @property {Set<string>} children
 * @property {ID[]} keyFrameList
 */

/** @typedef {'EXPLICIT' | 'INITIAL'} FrameType */
/** @typedef {'LINEAR' | 'EASE' | 'EASE_IN' | 'EASE_OUT' | 'EASE_IN_OUT' | 'EASE_IN_SIN' | 'EASE_OUT_SINE' | 'EASE_IN_OUT_SINE' | 'EASE_IN_QUAD' | 'EASE_OUT_QUAD' | 'EASE_IN_OUT_QUAD' | 'EASE_IN_CUBIC' | 'EASE_OUT_CUBIC' | 'EASE_IN_OUT_CUBIC' | 'EASE_IN_QUART' | 'EASE_OUT_QUART' | 'EASE_IN_OUT_QUART' | 'EASE_IN_QUINT' | 'EASE_OUT_QUINT' | 'EASE_IN_OUT_QUINT' | 'EASE_IN_EXPO' | 'EASE_OUT_EXPO' | 'EASE_IN_OUT_EXPO' | 'EASE_IN_CIRC' | 'EASE_OUT_CIRC' | 'EASE_IN_OUT_CIRC' | 'EASE_IN_BACK' | 'EASE_OUT_BACK' | 'EASE_IN_OUT_BACK'} EasingType */
/** @typedef {[number, number, number, number]} Bezier */


/**
 * @typedef {object} KeyFrame
 * @property {ID} id
 * @property {InteractionEntityType.KEY_FRAME} type
 * @property {FrameType} frameType
 * @property {ID} trackId
 * @property {EasingType} easingType
 * @property {boolean} animatable
 * @property {Bezier} bezier
 * @property {number} steps
 * @property {number} time
 * @property {boolean} delta
 * @property {any} value
 * @property {ID} ref
 * @property {Unit} unit
 */

/**
 * @typedef {object} AnimationPreset
 * @property {ID} id
 * @property {InteractionEntityType.ANIMATION_PRESET} type
 * @property {ID} elementTrackId
 * @property {string} key
 * @property {EasingType} easingType
 * @property {Bezier} bezier
 * @property {AnimationPresetType} presetType
 * @property {number} startTime
 * @property {number} duration
 * @property {number} delay
 */

/** @typedef {Action | Response | Trigger | Condition | ElementTrack | PropertyTrack | KeyFrame | AnimationPreset} Entity */

/**
 * @typedef {object} InteractionData
 * @property {Map<ID | Entity>} entityMap
 */
