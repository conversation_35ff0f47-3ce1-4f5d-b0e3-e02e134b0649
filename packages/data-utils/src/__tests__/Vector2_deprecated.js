import { _Vector2 as Vector2 } from '../Vector2_deprecated'

const PRECISION = 7

describe('Vector2:', () => {
    const expectAllGettersToReturnCorrectValues = (v, a, b, precision = PRECISION) => {
        expect(v[0]).toBeCloseTo(a, precision)
        expect(v[1]).toBeCloseTo(b, precision)
        expect(v.x).toBeCloseTo(a, precision)
        expect(v.y).toBeCloseTo(b, precision)
        expect(v.width).toBeCloseTo(a, precision)
        expect(v.height).toBeCloseTo(b, precision)
    }

    /**
     * @param {() => void} fn
     * @returns {boolean}
     */
    const threw = (fn) => {
        try {
            fn()
        } catch (e) {
            return true
        }
        return false
    }

    it('should create zero Vector2', () => {
        const v = new Vector2()

        expect(v instanceof Float32Array).toBe(true)
        expectAllGettersToReturnCorrectValues(v, 0, 0)
    })

    it('should create a Vector2 from 2 components', () => {
        const v1 = new Vector2(0, 0)
        const v2 = new Vector2(1, 2)
        const v3 = new Vector2(-2, -1)
        const v4 = new Vector2(0.1, -0.1)
        const v5 = new Vector2(-0.2, 0.2)
        const v6 = new Vector2(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER)
        const v7 = new Vector2(Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
        const v8 = new Vector2(Number.EPSILON, -Number.EPSILON)
        const v9 = new Vector2(Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        const v10 = new Vector2(Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should create a Vector2 from another Vector2', () => {
        const v1 = new Vector2(new Vector2(0, 0))
        const v2 = new Vector2(new Vector2(1, 2))
        const v3 = new Vector2(new Vector2(-2, -1))
        const v4 = new Vector2(new Vector2(0.1, -0.1))
        const v5 = new Vector2(new Vector2(-0.2, 0.2))
        const v6 = new Vector2(new Vector2(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER))
        const v7 = new Vector2(new Vector2(Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER))
        const v8 = new Vector2(new Vector2(Number.EPSILON, -Number.EPSILON))
        const v9 = new Vector2(new Vector2(Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY))
        const v10 = new Vector2(new Vector2(Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY))

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should create a Vector2 from two-number array', () => {
        const v1 = new Vector2([0, 0])
        const v2 = new Vector2([1, 2])
        const v3 = new Vector2([-2, -1])
        const v4 = new Vector2([0.1, -0.1])
        const v5 = new Vector2([-0.2, 0.2])
        const v6 = new Vector2([Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER])
        const v7 = new Vector2([Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER])
        const v8 = new Vector2([Number.EPSILON, -Number.EPSILON])
        const v9 = new Vector2([Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY])
        const v10 = new Vector2([Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY])

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should create a Vector2 from {x, y} object', () => {
        const v1 = new Vector2({ x: 0, y: 0 })
        const v2 = new Vector2({ x: 1, y: 2 })
        const v3 = new Vector2({ x: -2, y: -1 })
        const v4 = new Vector2({ x: 0.1, y: -0.1 })
        const v5 = new Vector2({ x: -0.2, y: 0.2 })
        const v6 = new Vector2({ x: Number.MAX_SAFE_INTEGER, y: Number.MIN_SAFE_INTEGER })
        const v7 = new Vector2({ x: Number.MIN_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER })
        const v8 = new Vector2({ x: Number.EPSILON, y: -Number.EPSILON })
        const v9 = new Vector2({ x: Number.NEGATIVE_INFINITY, y: Number.POSITIVE_INFINITY })
        const v10 = new Vector2({ x: Number.POSITIVE_INFINITY, y: Number.NEGATIVE_INFINITY })

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should create a Vector2 from {width, height} object', () => {
        const v1 = new Vector2({ width: 0, height: 0 })
        const v2 = new Vector2({ width: 1, height: 2 })
        const v3 = new Vector2({ width: -2, height: -1 })
        const v4 = new Vector2({ width: 0.1, height: -0.1 })
        const v5 = new Vector2({ width: -0.2, height: 0.2 })
        const v6 = new Vector2({ width: Number.MAX_SAFE_INTEGER, height: Number.MIN_SAFE_INTEGER })
        const v7 = new Vector2({ width: Number.MIN_SAFE_INTEGER, height: Number.MAX_SAFE_INTEGER })
        const v8 = new Vector2({ width: Number.EPSILON, height: -Number.EPSILON })
        const v9 = new Vector2({ width: Number.NEGATIVE_INFINITY, height: Number.POSITIVE_INFINITY })
        const v10 = new Vector2({ width: Number.POSITIVE_INFINITY, height: Number.NEGATIVE_INFINITY })

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should copy values from another Vector2', () => {
        const v1 = new Vector2()
        v1.copy(new Vector2(0, 0))
        const v2 = new Vector2()
        v2.copy(new Vector2(1, 2))
        const v3 = new Vector2()
        v3.copy(new Vector2(-2, -1))
        const v4 = new Vector2()
        v4.copy(new Vector2(0.1, -0.1))
        const v5 = new Vector2()
        v5.copy(new Vector2(-0.2, 0.2))
        const v6 = new Vector2()
        v6.copy(new Vector2(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER))
        const v7 = new Vector2()
        v7.copy(new Vector2(Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER))
        const v8 = new Vector2()
        v8.copy(new Vector2(Number.EPSILON, -Number.EPSILON))
        const v9 = new Vector2()
        v9.copy(new Vector2(Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY))
        const v10 = new Vector2()
        v10.copy(new Vector2(Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY))

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should throw an exception when copying NaN values', () => {
        const v = new Vector2()

        expect(threw(() => { v.copy(['a', 1]) })).toBe(true)
        expect(threw(() => { v.copy([1, 'a']) })).toBe(true)
        expect(threw(() => { v.copy({ x: 'a', y: 1 }) })).toBe(true)
        expect(threw(() => { v.copy({ width: 'a', height: 1 }) })).toBe(true)
        expect(threw(() => { v.copy({ width: 1, height: 'a' }) })).toBe(true)
    })

    it('should copy values from two-number array', () => {
        const v1 = new Vector2()
        v1.copy([0, 0])
        const v2 = new Vector2()
        v2.copy([1, 2])
        const v3 = new Vector2()
        v3.copy([-2, -1])
        const v4 = new Vector2()
        v4.copy([0.1, -0.1])
        const v5 = new Vector2()
        v5.copy([-0.2, 0.2])
        const v6 = new Vector2()
        v6.copy([Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER])
        const v7 = new Vector2()
        v7.copy([Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER])
        const v8 = new Vector2()
        v8.copy([Number.EPSILON, -Number.EPSILON])
        const v9 = new Vector2()
        v9.copy([Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY])
        const v10 = new Vector2()
        v10.copy([Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY])

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should copy value from {x, y} object', () => {
        const v1 = new Vector2()
        v1.copy({ x: 0, y: 0 })
        const v2 = new Vector2()
        v2.copy({ x: 1, y: 2 })
        const v3 = new Vector2()
        v3.copy({ x: -2, y: -1 })
        const v4 = new Vector2()
        v4.copy({ x: 0.1, y: -0.1 })
        const v5 = new Vector2()
        v5.copy({ x: -0.2, y: 0.2 })
        const v6 = new Vector2()
        v6.copy({ x: Number.MAX_SAFE_INTEGER, y: Number.MIN_SAFE_INTEGER })
        const v7 = new Vector2()
        v7.copy({ x: Number.MIN_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER })
        const v8 = new Vector2()
        v8.copy({ x: Number.EPSILON, y: -Number.EPSILON })
        const v9 = new Vector2()
        v9.copy({ x: Number.NEGATIVE_INFINITY, y: Number.POSITIVE_INFINITY })
        const v10 = new Vector2()
        v10.copy({ x: Number.POSITIVE_INFINITY, y: Number.NEGATIVE_INFINITY })

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should copy values from {width, height} object', () => {
        const v1 = new Vector2()
        v1.copy({ width: 0, height: 0 })
        const v2 = new Vector2()
        v2.copy({ width: 1, height: 2 })
        const v3 = new Vector2()
        v3.copy({ width: -2, height: -1 })
        const v4 = new Vector2()
        v4.copy({ width: 0.1, height: -0.1 })
        const v5 = new Vector2()
        v5.copy({ width: -0.2, height: 0.2 })
        const v6 = new Vector2()
        v6.copy({ width: Number.MAX_SAFE_INTEGER, height: Number.MIN_SAFE_INTEGER })
        const v7 = new Vector2()
        v7.copy({ width: Number.MIN_SAFE_INTEGER, height: Number.MAX_SAFE_INTEGER })
        const v8 = new Vector2()
        v8.copy({ width: Number.EPSILON, height: -Number.EPSILON })
        const v9 = new Vector2()
        v9.copy({ width: Number.NEGATIVE_INFINITY, height: Number.POSITIVE_INFINITY })
        const v10 = new Vector2()
        v10.copy({ width: Number.POSITIVE_INFINITY, height: Number.NEGATIVE_INFINITY })

        expectAllGettersToReturnCorrectValues(v1, 0, 0)
        expectAllGettersToReturnCorrectValues(v2, 1, 2)
        expectAllGettersToReturnCorrectValues(v3, -2, -1)
        expectAllGettersToReturnCorrectValues(v4, 0.1, -0.1)
        expectAllGettersToReturnCorrectValues(v5, -0.2, 0.2)
        expectAllGettersToReturnCorrectValues(v6, Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v7, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, -1)
        expectAllGettersToReturnCorrectValues(v8, Number.EPSILON, -Number.EPSILON)
        expectAllGettersToReturnCorrectValues(v9, Number.NEGATIVE_INFINITY, Number.POSITIVE_INFINITY)
        expectAllGettersToReturnCorrectValues(v10, Number.POSITIVE_INFINITY, Number.NEGATIVE_INFINITY)
    })

    it('should check two vectors as equal', () => {
        const v1 = new Vector2(1, 2)
        const v2 = new Vector2(1, 2)

        expect(v1.eq(v2)).toBe(true)
        expect(v1.eq({ x: 1, y: 2 })).toBe(true)
        expect(v1.eq({ width: 1, height: 2 })).toBe(true)
    })

    it('should check two vectors are not equal', () => {
        const v1 = new Vector2(1, 2)
        const v2 = new Vector2(2, 1)

        expect(v1.eq(v2)).toBe(false)
        expect(v1.eq()).toBe(false)
        expect(v1.eq(null)).toBe(false)
        expect(v1.eq({ x: 2, y: 1 })).toBe(false)
        expect(v1.eq({ width: 2, height: 1 })).toBe(false)
    })

    it('should check two vectors are equal with different precision', () => {
        const v1 = new Vector2(1, 2)
        const v2 = new Vector2(1.1, 2.1)

        expect(v1.eq(v2, 0.1)).toBe(true)
        expect(v1.eq(v2, 0.05)).toBe(false)
    })

    it('should assign with x, y', () => {
        const v = new Vector2()
        v.x = 1
        v.y = 2

        expectAllGettersToReturnCorrectValues(v, 1, 2)
    })

    it('should assign with width, height', () => {
        const v = new Vector2()
        v.width = 1
        v.height = 2

        expectAllGettersToReturnCorrectValues(v, 1, 2)
    })

    it('should assign ONE and ZERO constants', () => {
        expect(Vector2.ZERO).toEqual(new Vector2())
        expectAllGettersToReturnCorrectValues(Vector2.ZERO, 0, 0)
        expect(Vector2.ONE).toEqual(new Vector2(1, 1))
        expectAllGettersToReturnCorrectValues(Vector2.ONE, 1, 1)
    })

    it('should throw an exception on setting NaN for x, y, width, and height', () => {
        const vector = new Vector2()

        expect(threw(() => { vector.x = 'a' })).toBe(true)
        expect(threw(() => { vector.y = 'a' })).toBe(true)
        expect(threw(() => { vector.width = 'a' })).toBe(true)
        expect(threw(() => { vector.height = 'a' })).toBe(true)
    })
})
