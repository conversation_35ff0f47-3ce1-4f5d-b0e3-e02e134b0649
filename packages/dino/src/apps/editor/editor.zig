const std = @import("std");
const builtin = @import("builtin");

const dino = @import("dino");
const col = dino.color;
const math = dino.math;
const utils = dino.utils;
const nameHash = utils.nameHash;
const app = dino.app;
const fetch = dino.fetch;
const gfx = dino.gfx;
const gui = dino.gui;
const image = dino.image;
const sd = dino.scene_data;
const SceneTree = dino.SceneTree;
const PathData = sd.PathData;
const Path = dino.Path;
const Page = dino.text.Page;
const shaping = dino.text.shaping;
const NoteDoc = dino.NoteDoc;
const RIMesh = dino.RIMesh;
const stroker = dino.stroker;
const FontDB = dino.text.FontDB;
const TargetPath = dino.TargetPath;
const postprocess = dino.postprocess;
const compose = dino.compose;
const winding_buffer = dino.winding_buffer;
const tile_render = dino.tile_render;
const TileCanvas = tile_render.TileCanvas;
const canvas = dino.canvas;
const TileSet = dino.TileSet;
const TileMap = dino.TileMap;
const TriMesh = dino.TriMesh;
const unicode_ranges = dino.text.unicode_ranges;

const theme = struct {
    var character_border_color = dino.color.Color.initWithHex("#9966ff");
    var line_border_color = dino.color.Color.initWithHex("#5cc9f5");
    var mouse_position_color = dino.color.Color.initWithHex("#ff0000");

    var background_color = dino.color.Color.initWithHex("#F4CBC6");
    var page_background_color = dino.color.Color.initWithHex("#FFF7EC");
    var text_color = dino.color.black;

    var cursor_color = dino.color.black;
    var selection_color = dino.color.Color.init(0.3, 0.5, 1.0, 0.4);
    var drag_handle_color = dino.color.Color.initWithHex("#C0BFC1");
};

const main_font_file = @embedFile("../../assets/fonts/inter/Inter-Regular.ttf");

var assets_url_buf: [1024]u8 = undefined;

fn assetPath(path: []const u8) [:0]const u8 {
    return std.fmt.bufPrintZ(&assets_url_buf, "{s}/{s}", .{ state.assets_dir, path }) catch &.{};
}
fn fontPath(family_name: []const u8, italic: bool, weight: FontDB.Weight) [:0]const u8 {
    var name_buf: [128]u8 = undefined;
    const name = removeSpaces(&name_buf, family_name);
    var buf: [2048]u8 = undefined;
    switch (weight) {
        .thin,
        .extra_light,
        .light,
        .medium,
        .semi_bold,
        .bold,
        .extra_bold,
        .black,
        => return assetPath(std.fmt.bufPrintZ(&buf, "fonts/{s}-{s}{s}.ttf", .{
            name,
            @tagName(weight),
            if (italic) "-italic" else "",
        }) catch &.{}),
        else => return assetPath(std.fmt.bufPrintZ(&buf, "fonts/{s}{s}.ttf", .{
            name,
            if (italic) "-italic" else "",
        }) catch &.{}),
    }
    return &.{};
}

fn removeSpaces(buf: []u8, input: []const u8) []u8 {
    var index: usize = 0;
    for (input) |c| {
        if (c != ' ') {
            buf[index] = c;
            index += 1;
        }
    }
    return buf[0..index];
}

const AppMode = enum {
    none,
    preinit,
    preloading,
    initializing,
    loading,
    pausing,
    loaded,
    editing,
};

const Camera = struct {
    offset: math.Vec2f = math.vec2f(0.0, 0.0),
    zoom: f32 = 1.0,

    pub fn zoomToPoint(camera: *Camera, pos: math.Vec2f, delta: f32) void {
        camera.zoom *= delta;
        _ = pos;
    }
};

const Capture = struct {
    const size_min: i32 = 1;
    const size_max: i32 = 4096; // TODO: support larger sizes

    const ImageFormat = enum {
        png,
        jpg,
    };

    const Kind = enum {
        none,
        full,
        fixed_size,
    };

    const ViewSetting = struct {
        size: math.Vec2f,
        offset: math.Vec2f,
        zoom: f32,
        clip_box: math.Rect2f,
    };

    root: u32 = 0,
    clip_bbox: ?math.Rect2f = null,
    kind: Kind = .none,

    format: ImageFormat = .png,
    quality: u32 = 90,

    origin_size: [2]i32 = .{ 16, 16 },
    size: [2]i32 = .{ 16, 16 },
    keep_ratio: bool = true,

    bg_color: col.Color = col.transparent,

    att: gfx.Attachments = .{},
    img: gfx.Image = .{},

    pub fn init() Capture {
        return .{};
    }
    pub fn deinit(capture: *Capture) void {
        gfx.destroyAttachments(capture.att);
        gfx.destroyImage(capture.img);
    }

    /// Whether a capture is requested.
    pub inline fn hasRequest(capture: *Capture) bool {
        return capture.kind != .none;
    }

    /// Adjust size values base on other settings.
    pub fn calcNewSize(capture: *Capture, based_on_w: bool) void {
        if (capture.keep_ratio) {
            const o_w: f32 = @floatFromInt(capture.origin_size[0]);
            const o_h: f32 = @floatFromInt(capture.origin_size[1]);
            const ratio = o_w / o_h;
            if (based_on_w) {
                var w = std.math.clamp(capture.size[0], size_min, size_max);
                var wf: f32 = @floatFromInt(w);
                const hf: f32 = wf / ratio;
                var h: i32 = @intFromFloat(hf);
                if (h < size_min) {
                    h = size_min;
                    wf = hf * ratio;
                    w = @intFromFloat(wf);
                    w = std.math.clamp(w, size_min, size_max);
                } else if (h > size_max) {
                    h = size_max;
                    wf = hf * ratio;
                    w = @intFromFloat(wf);
                    w = std.math.clamp(w, size_min, size_max);
                }
                capture.size[0] = w;
                capture.size[1] = h;
            } else {
                var h = std.math.clamp(capture.size[1], size_min, size_max);
                var hf: f32 = @floatFromInt(h);
                const wf: f32 = hf * ratio;
                var w: i32 = @intFromFloat(wf);
                if (w < size_min) {
                    w = size_min;
                    hf = wf / ratio;
                    h = @intFromFloat(hf);
                    h = std.math.clamp(h, size_min, size_max);
                } else if (w > size_max) {
                    w = size_max;
                    hf = wf / ratio;
                    h = @intFromFloat(hf);
                    h = std.math.clamp(h, size_min, size_max);
                }
                capture.size[0] = w;
                capture.size[1] = h;
            }
        } else {
            capture.size[0] = std.math.clamp(capture.size[0], size_min, size_max);
            capture.size[1] = std.math.clamp(capture.size[1], size_min, size_max);
        }
    }

    /// Prepare for capture of provided tree. Returns view settings for proper render set up.
    pub fn prepare(capture: *Capture, tree: *SceneTree, root: u32) ?ViewSetting {
        const bbox = blk: {
            if (state.capture.clip_bbox) |bbox| {
                break :blk bbox;
            }
            if (tree.nodes.getBBoxPtr(root)) |bbox| {
                if (bbox.visual_aabb) |aabb| {
                    break :blk aabb;
                }
            }
            break :blk null;
        };
        if (bbox) |aabb| {
            switch (capture.kind) {
                .full => {
                    capture.resize(
                        @intFromFloat(aabb.width()),
                        @intFromFloat(aabb.height()),
                    );
                    return .{
                        .size = aabb.size(),
                        .offset = aabb.topLeft(),
                        .zoom = 1.0,
                        .clip_box = aabb,
                    };
                },
                .fixed_size => {
                    capture.resize(
                        capture.size[0],
                        capture.size[1],
                    );
                    const view = math.rect2f(0.0, 0.0, @floatFromInt(capture.size[0]), @floatFromInt(capture.size[1]));
                    const fixed_aabb = math.fitBox(f32, aabb.size(), view, .inner);
                    const zoom = fixed_aabb.width() / aabb.width();
                    return .{
                        .size = view.size(),
                        .offset = aabb.topLeft().mul(zoom).sub(fixed_aabb.topLeft()),
                        .zoom = zoom,
                        .clip_box = view,
                    };
                },
                else => {},
            }
        }
        capture.kind = .none;
        return null;
    }

    pub fn beginPass(capture: *Capture) void {
        gfx.beginPass(.{
            .action = blk: {
                var action: gfx.PassAction = .{
                    .depth = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                    .stencil = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                };
                action.colors[0] = .{
                    .load_action = .CLEAR,
                    .store_action = .STORE,
                    .clear_value = capture.bg_color,
                };
                break :blk action;
            },
            .attachments = capture.att,
        });
    }
    pub fn endPass(capture: *Capture) void {
        _ = capture;
        gfx.endPass();
    }

    /// Fetch captured image.
    ///
    /// NOTE: can only be called outside pass!!!
    pub fn fetch(capture: *Capture) ?dino.image.Image {
        const desc = gfx.queryImageDesc(capture.img);
        var img = dino.image.Image.createEmpty(@intCast(desc.width), @intCast(desc.height), 4, .{}) catch {
            return null;
        };
        const px_fmt = blk: {
            const n = gfx.queryImagePixels(img.data, state.capture.img);
            const fmt: dino.image.PixelFormat = @enumFromInt(n);
            break :blk fmt;
        };
        switch (px_fmt) {
            .BGRA8 => {
                img.convertPixelFormat(px_fmt, .RGBA8);
            },
            else => {},
        }
        return img;
    }

    /// Cleanup request and reset.
    pub fn finish(capture: *Capture) void {
        capture.kind = .none;
    }

    //
    // implementation
    //

    fn resize(capture: *Capture, width: i32, height: i32) void {
        var make_new_pass = true;
        if (gfx.queryImageState(capture.img) == .VALID) {
            const desc = gfx.queryImageDesc(capture.img);
            if ((width == desc.width) and (height == desc.height)) {
                make_new_pass = false;
            } else {
                gfx.destroyAttachments(capture.att);
                gfx.destroyImage(capture.img);
            }
        }
        if (make_new_pass) {
            capture.img = gfx.makeImage(.{
                .render_target = true,
                .width = width,
                .height = height,
                .label = "Capture.image",
            });
            var desc: gfx.AttachmentsDesc = .{};
            desc.colors[0] = .{
                .image = capture.img,
            };
            capture.att = gfx.makeAttachments(desc);
        }
    }
};

/// Text path caching system, caches paths generated from TextDoc+TextStyle.
// const TextComputeCache = struct {
//     const Item = struct {
//         compute: TextEngine.ComputeCache,
//         path_id: u32,
//         tight_bbox: math.Rect2f,
//     };

//     allocator: std.mem.Allocator,
//     tree: *SceneTree,
//     engine: *TextEngine,

//     table: std.AutoHashMap(u32, Item),

//     pub fn init(allocator: std.mem.Allocator, tree: *SceneTree, engine: *TextEngine) TextComputeCache {
//         return TextComputeCache{
//             .allocator = allocator,
//             .tree = tree,
//             .engine = engine,
//             .table = std.AutoHashMap(u32, Item).init(allocator),
//         };
//     }
//     pub fn deinit(cache: *TextComputeCache) void {
//         var it = cache.table.valueIterator();
//         while (it.next()) |value_ptr| {
//             if (state.tree.paths.getDataPtr(value_ptr.path_id)) |path_data| {
//                 _ = path_data;
//                 api.destroyPath(value_ptr.path_id);
//             }
//             value_ptr.compute.deinit();
//         }
//         cache.table.deinit();
//     }

//     pub fn computeAndCache(cache: *TextComputeCache, node_rid: u32) !void {
//         try cache.remove(node_rid);
//         if (cache.tree.nodes.getNodePtr(node_rid)) |node_ptr| {
//             if (node_ptr.data == .text) {
//                 const node = &node_ptr.data.text;
//                 if (cache.tree.text_docs.getDataPtr(node.doc_id)) |doc_ptr| {
//                     if (cache.tree.text_styles.getDataPtr(node.style_id)) |style_ptr| {
//                         var compute = try TextEngine.ComputeCache.init(cache.allocator);
//                         compute.doc = doc_ptr;
//                         compute.style = style_ptr;
//                         try cache.engine.layout(&compute);
//                         // cache compute
//                         if (compute.visual_lines.items.len > 0) {
//                             const entry = try cache.table.getOrPut(node_rid);
//                             errdefer _ = cache.table.remove(node_rid);
//                             entry.value_ptr.compute = compute;
//                             const path = try cache.engine.flattenVisualSpansAsSinglePath(&compute);
//                             entry.value_ptr.path_id = api.makePath(path);
//                             entry.value_ptr.tight_bbox = path.bbox();
//                         } else {
//                             // free
//                             compute.deinit();
//                         }
//                     }
//                 }
//             }
//         }
//     }
//     pub fn remove(cache: *TextComputeCache, node_rid: u32) !void {
//         if (cache.table.fetchRemove(node_rid)) |pair| {
//             api.destroyPath(pair.value.path_id);
//             @constCast(&pair.value.compute).deinit();
//         }
//     }

//     pub fn isEmpty(cache: *TextComputeCache, node_rid: u32) bool {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             return item_ptr.compute.visual_lines.items.len == 0;
//         }
//         return true;
//     }
//     pub fn getPath(cache: *TextComputeCache, node_rid: u32) ?Path {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             if (item_ptr.path_id == 0) {
//                 const path = cache.engine.flattenVisualSpansAsSinglePath(&item_ptr.compute) catch return null;
//                 item_ptr.path_id = api.makePath(path);
//                 item_ptr.tight_bbox = path.bbox();
//             }
//             return state.tree.paths.getDataPtr(item_ptr.path_id).?.path;
//         }
//         return null;
//     }
//     pub fn getTextBBox(cache: *TextComputeCache, node_rid: u32) ?math.Rect2f {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             return item_ptr.compute.getLayoutBox();
//         }
//         return null;
//     }
//     pub fn getVisualBBox(cache: *TextComputeCache, node_rid: u32) ?math.Rect2f {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             return item_ptr.tight_bbox;
//         }
//         return null;
//     }

//     pub fn getVisualLineList(cache: *TextComputeCache, node_rid: u32) ?std.ArrayList(TextEngine.VisualLine) {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             return item_ptr.compute.visual_lines;
//         }
//         return null;
//     }

//     pub fn getTextOffset(cache: *TextComputeCache, node_rid: u32) ?[*c]f32 {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             const buf = allocArena32(2);
//             const offset = math.vec2f(0.0, item_ptr.compute.section_bboxes.get(1).?.top());
//             buf[0] = offset.x();
//             buf[1] = offset.y();

//             return buf;
//         }
//         return null;
//     }
//     pub fn getTextLayoutBox(cache: *TextComputeCache, node_rid: u32) ?[*c]f32 {
//         if (cache.table.getPtr(node_rid)) |item_ptr| {
//             const buf = allocArena32(4);
//             const bbox = item_ptr.compute.getLayoutBox();
//             buf[0] = bbox.left();
//             buf[1] = bbox.top();
//             buf[2] = bbox.width();
//             buf[3] = bbox.height();

//             return buf;
//         }
//         return null;
//     }
// };

var arena: std.heap.ArenaAllocator = undefined;
const fetch_buf = struct {
    var next_index: u32 = 0;

    var font: [2][]u8 = undefined;
    var image: []u8 = &.{};
    var other: []u8 = &.{};
};
var img_checkboard: gfx.Image = undefined;

var wb: winding_buffer.WindingBuffer = undefined;

const state = struct {
    var app_mode: AppMode = .none;

    // render
    var camera: Camera = .{};
    var view_size = math.vec2f(480.0, 480.0);

    var tileset_default: TileSet = .{};
    var tileset_xray: TileSet = .{};
    var tileset_outline: TileSet = .{};
    var tilemap: TileMap = .{};

    const tilemap_debug = struct {
        const TileKey = struct {
            col: i32,
            row: i32,
        };
        const TileInfo = struct {
            nodes_total: u32 = 0,
            fill_total: u32 = 0,
            stroke_total: u32 = 0,
            clip_total: u32 = 0,
            mask_total: u32 = 0,
            pub fn isEmpty(info: *TileInfo) bool {
                return ((info.nodes_total == 0) and
                    (info.fill_total == 0) and
                    (info.stroke_total == 0) and
                    (info.clip_total == 0) and
                    (info.mask_total == 0));
            }
        };
        var tile_infos = std.AutoHashMap(TileKey, TileInfo).init(dino.galloc);

        pub fn reset() void {
            tile_infos.clearRetainingCapacity();
        }
    };

    var capture: Capture = .{};

    var bg_color = col.white;

    // runtime
    var tree: SceneTree = undefined;

    // var text_engine: TextEngine = undefined;
    var page: Page = undefined;

    const UpdateList = std.AutoHashMap(u32, void);
    var update_list: UpdateList = undefined;
    var selected: u32 = 0;

    var assets_dir: []u8 = "";

    var num_nodes_render: u32 = 0;
    var num_tiles_render: u32 = 0;

    const StrokePathKey = struct {
        stroke: u32,
        path: u32,
    };
    const PathAndMesh = struct {
        path: ?PathData,
        mesh: ?RIMesh,
        pub fn fetchMesh(self: *PathAndMesh) ?RIMesh {
            if (self.mesh) |mesh| {
                return mesh;
            }
            if (self.path) |path_data| {
                switch (path_data) {
                    .rect => |rect| {
                        const mesh = RIMesh.initWithRect(dino.galloc, rect) catch return null;
                        self.mesh = mesh;
                    },
                    .oval => |oval| {
                        var pb = Path.Builder.init(arena.allocator());
                        pb.addOval(oval.left(), oval.top(), oval.width(), oval.height()) catch return null;
                        const mesh = RIMesh.initWithPath(dino.galloc, pb, 0.5) catch return null;
                        if (mesh.vertices.len > 0) {
                            self.mesh = mesh;
                        }
                    },
                    .path => |path| {
                        const mesh = RIMesh.initWithPath(dino.galloc, path, 0.01) catch return null;
                        if (mesh.vertices.len > 0) {
                            self.mesh = mesh;
                        }
                    },
                }
            }
            return self.mesh;
        }
        pub fn deinit(self: PathAndMesh) void {
            if (self.path) |path_data| {
                switch (path_data) {
                    .path => |path| safeDestroyPath(dino.galloc, path),
                    else => {},
                }
            }
            if (self.mesh) |mesh| {
                dino.galloc.free(mesh.vertices);
            }
        }
    };
    const PathTable = std.AutoHashMap(u32, PathAndMesh);
    var base_path_cache: PathTable = undefined;
    // TODO: add a new cache for node path with unique id
    // var new_path_cache: PathTable = undefined;
    const StrokePathTable = std.AutoHashMap(StrokePathKey, PathAndMesh);
    var stroke_path_cache: StrokePathTable = undefined;

    const ElementResizeTable = std.AutoHashMap(u32, bool);
    var update_size_cache: ElementResizeTable = undefined;

    const view = struct {
        var dock_first_time: bool = true;

        const ViewMode = enum {
            default,
            xray,
            outline,
        };
        var mode: ViewMode = .default;

        var show_ui: bool = false;

        var show_tile_grid: bool = false;
        var show_tile_info: bool = false;
        var show_tileset: bool = false;
        var force_redraw: bool = false;
        var show_storage: bool = false;

        var path_preview_idx: ?u32 = null;

        var draw_canvas_bounds: bool = false;

        var canvas_to_world = math.mat2df_identity();

        var mouse_world = math.vec2fs(0.0);
    };

    var font_data: []const u8 = &.{};

    const project = struct {
        var curr_file_path: ?[:0]const u8 = null;
    };

    const file = struct {
        const Record = struct {
            complete: bool = false,
            file: []const u8 = undefined,
        };
        const RecordMap = std.AutoHashMap(u32, Record);

        var requests: std.ArrayList(FileRequest) = undefined;
        var records: RecordMap = undefined;
    };

    const config = struct {
        const snapping = struct {
            var axis = false;
            const obb = struct {
                var points = true;
                var center = true;
            };
        };
    };

    const edit = struct {
        var mouse_world = math.vec2fs(0.0);

        const Tool = enum {
            move,
            rect,
            oval,
            text,
        };
        var tool: Tool = .move;

        const move = struct {
            var state: enum { none, click, single_select, multi_select, box_select } = .none;
            var hover: ?u32 = null;
            var selected = std.ArrayList(u32).init(dino.galloc);
            var selection_box_p1 = math.vec2fs(0.0);
            var selection_box_p2 = math.vec2fs(0.0);
        };
        const insert = struct {};
        const resize = struct {};
        const text = struct {
            var mode: enum { insert, edit } = .insert;

            var selecting: bool = false;
            var select_begin: usize = 0;
            var select_end: usize = 0;
            var cursor_timer: f32 = 0.0;
            var mouse = math.vec2fs(0.0);
        };
    };
};

pub fn setPublicPathAndPreload(path: []const u8) !void {
    if (@intFromEnum(state.app_mode) >= @intFromEnum(AppMode.preloading)) return;
    state.app_mode = .preloading;

    const fixed_path = if (path[path.len - 1] == '/') path[0 .. path.len - 1] else path;
    state.assets_dir = if (fixed_path.len > 0) try dino.galloc.dupe(u8, fixed_path) else "";
    // dino.log.debug(@src(), .assets, "assets folder is '{s}'", .{state.assets_dir});
}

pub fn init() !void {
    state.app_mode = .preinit;

    // memory
    arena = std.heap.ArenaAllocator.init(dino.galloc);
    // fetch
    fetch_buf.font[0] = try dino.galloc.alloc(u8, 1024 * 1024 * 32);
    fetch_buf.font[1] = try dino.galloc.alloc(u8, 1024 * 1024 * 32);
    fetch_buf.image = try dino.galloc.alloc(u8, 1024 * 1024 * 16);
    fetch_buf.other = try dino.galloc.alloc(u8, 1024 * 1024 * 16);
    fetch.init(.{
        .num_channels = 4,
        .num_lanes = 1,
    });
    state.file.requests = std.ArrayList(FileRequest).init(dino.galloc);
    state.file.records = state.file.RecordMap.init(dino.galloc);
    // gfx
    gfx.init(.{
        .shader_pool_size = 1024,
        .pipeline_pool_size = 1024,
        .buffer_pool_size = 1024 * 32,
        .image_pool_size = 1024 * 8,
        .uniform_buffer_size = 1024 * 1024,
        .disable_validation = builtin.mode != .Debug,
        .attachments_pool_size = 1024 * 8,
        .environment = dino.environment(),
        .logger = .{ .func = dino.log.func },
    });
    dino.log.info(.gfx, "Backend: {}", .{gfx.queryBackend()});
    // image
    image.init(dino.galloc);
    // gui
    try gui.init(dino.galloc);

    img_checkboard = blk: {
        const rows = 8;
        const cols = 8;
        var desc: gfx.ImageDesc = .{
            .width = cols,
            .height = rows,
            .pixel_format = .RGBA8,
        };
        var buf: [rows * cols]u32 = undefined;
        for (0..rows) |r| {
            for (0..cols) |q| {
                buf[r * cols + q] = if ((r % 2) == 0)
                    if ((q % 2) == 0) 0xFFFFFFFF else 0xFF000000
                else if ((q % 2) == 1) 0xFFFFFFFF else 0xFF000000;
            }
        }
        desc.data.subimage[0][0] = utils.asRange(&buf);
        break :blk gfx.makeImage(desc);
    };

    postprocess.init();
    compose.init();

    winding_buffer.init(dino.galloc);
    wb = winding_buffer.make(TileMap.tile_size * 2, TileMap.tile_size * 2);
    tile_render.init(dino.galloc);

    try canvas.init(dino.galloc); // Assuming init does not return a value

    state.tileset_default = TileSet.init(dino.galloc);
    state.tileset_xray = TileSet.init(dino.galloc);
    state.tileset_outline = TileSet.init(dino.galloc);
    state.tilemap = TileMap.init();

    state.capture = Capture.init();

    state.tree = try SceneTree.init(dino.galloc);

    // state.text_engine = TextEngine.init(dino.galloc, state.tree.font_db);
    state.page = Page.init(dino.galloc);

    {
        const family_buffer = try dupeZ("Inter");
        var setting = FontDB.FontSetting{
            .family = family_buffer,
            .style = .normal,
            .stretch = .normal,
            .weight = .regular,
        };
        const font_rid = try state.tree.font_db.loadFromMemory(setting, main_font_file);
        const font_info = state.tree.font_db.infos.get(font_rid).?;
        setting.family = font_info.family;
        dino.galloc.free(family_buffer);
        const fallback_font_id = try state.tree.font_db.loadFromMemory(.{
            .family = "OpenSans",
        }, @embedFile("../../dino/OpenSans-subset.ttf"));
        try dino.text.shaping.init(dino.galloc, state.tree.font_db, fallback_font_id);
    }

    try addDefaultRoot(&state.tree);
    state.update_list = state.UpdateList.init(dino.galloc);
    state.selected = state.tree.root;

    state.base_path_cache = state.PathTable.init(dino.galloc);
    state.stroke_path_cache = state.StrokePathTable.init(dino.galloc);
    state.update_size_cache = state.ElementResizeTable.init(dino.galloc);

    state.app_mode = .loaded;

    ext.onReady();
}

const anim_state = struct {
    var time: f32 = 0.0;
};

pub fn tick(delta: f32) !void {
    fetch.dowork();

    if (@intFromEnum(state.app_mode) < @intFromEnum(AppMode.loaded)) return;

    defer _ = arena.reset(.retain_capacity);

    state.num_nodes_render = 0;
    state.num_tiles_render = 0;
    var bg_color = state.bg_color;

    gui.newFrame(delta); // Assuming newFrame takes parameters directly
    const c = canvas;
    c.beginFrame(delta);
    winding_buffer.prepare();

    // UI
    var menu_bar_height: f32 = 0.0;
    var canvas_area = app.fbViewbox();
    if (state.view.show_ui) {
        // - main menu
        if (gui.beginMainMenuBar()) {
            if (gui.beginMenu("View", true)) {
                if (gui.beginMenu("View Mode", true)) {
                    var draw_default: bool = false;
                    var draw_xray: bool = false;
                    var draw_outline: bool = false;
                    switch (state.view.mode) {
                        .default => {
                            draw_default = true;
                            draw_xray = false;
                            draw_outline = false;
                        },
                        .xray => {
                            draw_default = false;
                            draw_xray = true;
                            draw_outline = false;
                        },
                        .outline => {
                            draw_default = false;
                            draw_xray = false;
                            draw_outline = true;
                        },
                    }
                    if (gui.checkbox("Vector", .{ .v = &draw_default })) {
                        state.view.mode = .default;
                        draw_xray = false;
                        draw_outline = false;
                    }
                    if (gui.checkbox("X-ray", .{ .v = &draw_xray })) {
                        state.view.mode = .xray;
                        draw_default = false;
                        draw_outline = false;
                    }
                    if (gui.checkbox("Outline", .{ .v = &draw_outline })) {
                        state.view.mode = .outline;
                        draw_default = false;
                        draw_xray = false;
                    }
                    gui.endMenu();
                }

                gui.separator();

                if (gui.beginMenu("Debug", true)) {
                    _ = gui.checkbox("Tile Grid", .{ .v = &state.view.show_tile_grid });
                    gui.beginDisabled(.{ .disabled = !state.view.show_tile_grid });
                    _ = gui.checkbox("Tile Info (only when grid is visible)", .{ .v = &state.view.show_tile_info });
                    gui.endDisabled();

                    _ = gui.checkbox("Show TileSet", .{ .v = &state.view.show_tileset });
                    _ = gui.checkbox("Show Storage", .{ .v = &state.view.show_storage });

                    gui.separator();

                    _ = gui.checkbox("Force Redraw", .{ .v = &state.view.force_redraw });
                    gui.endMenu();
                }

                gui.separator();

                _ = gui.checkbox("Toggle UI", .{ .v = &state.view.show_ui });

                gui.endMenu();
            }

            menu_bar_height = gui.getWindowSize()[1];

            gui.endMainMenuBar();
        }

        // - setup workspace
        {
            gui.setNextWindowPos(.{ .x = 0.0, .y = 0.0 });
            gui.setNextWindowSize(.{ .w = app.fbWidthf(), .h = app.fbHeightf() });
            _ = gui.begin("ws_container", .{ .flags = .{
                .no_docking = true,
                .no_title_bar = true,
                .no_collapse = true,
                .no_resize = true,
                .no_move = true,
                .no_bring_to_front_on_focus = true,
                .no_nav_focus = true,
                .no_background = true,
                .no_mouse_inputs = true,
            } });
            var workspace = gui.DockSpaceOverViewport(0, gui.getMainViewport(), .{
                .passthru_central_node = true,
                .no_tab_bar = true,
            });

            if (state.view.dock_first_time) {
                state.view.dock_first_time = false;

                gui.dockBuilderRemoveNode(workspace);
                _ = gui.dockBuilderAddNode(workspace, .{ .dock_space = true });
                gui.dockBuilderSetNodeSize(workspace, gui.getWindowSize());

                const win_outline = gui.dockBuilderSplitNode(workspace, .left, 0.15, null, &workspace);
                gui.dockBuilderDockWindow("Outline", win_outline);

                const win_node = gui.dockBuilderSplitNode(workspace, .right, 0.18, null, &workspace);
                gui.dockBuilderDockWindow("Node", win_node);

                gui.dockBuilderFinish(workspace);
            }

            gui.end();
        }

        // - panels
        canvas_area = blk: {
            const vp = gui.getMainViewport();
            break :blk math.rect2f(vp.getWorkPos()[0], vp.getWorkPos()[1], vp.getWorkSize()[0], vp.getWorkSize()[1]);
        };

        if (gui.begin("Outline", .{})) {
            _ = try guiOutlinePanel(state.tree.root);

            if (state.selected > 0) {
                const node_id = state.selected;
                if (state.tree.nodes.getNodePtr(node_id)) |node| {
                    switch (node.data) {
                        .group => |*group| {
                            gui.separatorText("Compose");
                            for (group.compose, 0..) |comp_id, i| {
                                if (state.tree.composes.getComposePtr(comp_id)) |comp| {
                                    gui.text("{d:0>4}. [id={d:0>8}]", .{ i, comp_id });
                                    switch (comp.*) {
                                        .clip, .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |cc| {
                                            try guiOutlinePanel(cc.root);
                                        },
                                        else => {},
                                    }
                                }
                            }
                        },
                        else => {},
                    }
                }
            }
        }
        // exclude window out of canvas area
        canvas_area = math.rect2fse(
            canvas_area.left() + gui.getWindowSize()[0],
            canvas_area.top(),
            canvas_area.right(),
            canvas_area.bottom(),
        );
        gui.end();

        // - Node editor
        _ = try guiNodePropertyPanel(&state.tree, state.selected);
        // exclude window out of canvas area
        canvas_area = math.rect2fse(
            canvas_area.left(),
            canvas_area.top(),
            canvas_area.right() - gui.getWindowSize()[0],
            canvas_area.bottom(),
        );
        gui.end();

        if (state.view.show_storage) {
            gui.setNextWindowPos(.{ .x = 400.0, .y = 200.0, .cond = .first_use_ever });
            gui.setNextWindowSize(.{ .w = app.fbWidthf() * 0.4, .h = app.fbHeightf() * 0.4, .cond = .first_use_ever });
            if (gui.begin("Resource View", .{ .flags = .{ .no_docking = true } })) {
                if (gui.beginTabBar("Tabs", .{})) {
                    if (gui.beginTabItem("Colors", .{})) {
                        var store = &state.tree.colors;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            var i: u32 = 1;
                            while (it.next()) |hdl| {
                                const id: u32 = @bitCast(hdl);
                                if (store.getDataPtr(id)) |color| {
                                    gui.text("{d:0>4}. [id={d:0>8}]", .{ i, id });
                                    gui.sameLine(.{});
                                    gui.setNextItemWidth(120.0 * app.dpiScale());
                                    var rgba = color.toRGBA32();
                                    if (gui.colorEdit4(try std.fmt.allocPrintZ(arena.allocator(), "##rv-color-{d}", .{id}), .{
                                        .col = @ptrCast(&rgba),
                                        .flags = .{
                                            .display_hex = true,
                                            .alpha_bar = true,
                                        },
                                    })) {
                                        switch (color.*) {
                                            .rgba => color.* = .{ .rgba = .{ rgba.r, rgba.g, rgba.b, rgba.a } },
                                            .hsla => {
                                                const hsl = col.rgb2hsl(rgba.r, rgba.g, rgba.b);
                                                color.* = .{ .hsla = .{ hsl[0], hsl[1], hsl[2], rgba.a } };
                                            },
                                        }
                                        color.* = .{
                                            .rgba = .{ rgba.r, rgba.g, rgba.b, rgba.a },
                                        };
                                    }
                                    gui.sameLine(.{});
                                    if (gui.button(try std.fmt.allocPrintZ(arena.allocator(), "X##rv-color-{d}", .{id}), .{ .w = 32, .h = 32 })) {
                                        try store.destroy(id);
                                        break;
                                    }
                                    i += 1;
                                }
                            }
                        } else {
                            gui.textUnformatted("No Colors");
                        }

                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Gradients", .{})) {
                        // TODO: grid view
                        var store = &state.tree.gradients;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            var i: u32 = 1;
                            while (it.next()) |hdl| : (i += 1) {
                                const id: u32 = @bitCast(hdl);
                                gui.text("{d:0>4}. [id={d:0>8}] {s}", .{ i, id, @tagName(store.getDataPtr(id).?.tag) });
                            }
                        } else {
                            gui.textUnformatted("No Gradients");
                        }
                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Images", .{})) {
                        // TODO: more information
                        var store = &state.tree.images;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            const max_item_size: f32 = 64.0 * app.dpiScale();
                            var i: u32 = 0;
                            while (it.next()) |hdl| {
                                const id: u32 = @bitCast(hdl);
                                if (store.getGfxImage(id)) |img| {
                                    const desc = gfx.queryImageDesc(img);
                                    const w: f32 = @floatFromInt(desc.width);
                                    const h: f32 = @floatFromInt(desc.height);
                                    gui.image(@ptrFromInt(img.id), .{
                                        .w = if (w / h > 1.0) max_item_size else h * (max_item_size / w),
                                        .h = if (w / h > 1.0) h * (max_item_size / w) else max_item_size,
                                    });
                                    gui.sameLine(.{});
                                    gui.text("{d:0>4}. [id={d:0>8}] {d}x{d}, {s}", .{ i, id, desc.width, desc.height, @tagName(desc.pixel_format) });
                                    gui.sameLine(.{});
                                    if (gui.button(try std.fmt.allocPrintZ(arena.allocator(), "X##rv-image-{d}", .{id}), .{ .w = 32, .h = 32 })) {
                                        try store.destroy(id);
                                        break;
                                    }
                                    i += 1;
                                }
                            }
                        } else {
                            gui.textUnformatted("No Images");
                        }
                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Strokes", .{})) {
                        // TODO: property edits
                        var store = &state.tree.strokes;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            var i: u32 = 0;
                            while (it.next()) |hdl| : (i += 1) {
                                const id: u32 = @bitCast(hdl);
                                const s = store.getDataPtr(id).?;
                                gui.text("{d:0>4}. [id={d:0>8}] join={s} cap={s} width={d}", .{ i, id, @tagName(s.linejoin), @tagName(s.linecap), s.linewidth });
                            }
                        } else {
                            gui.textUnformatted("No Strokes");
                        }
                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Paths", .{})) {
                        // TODO: add button to open in path view
                        var store = &state.tree.paths;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            var i: u32 = 0;
                            while (it.next()) |hdl| : (i += 1) {
                                const id: u32 = @bitCast(hdl);
                                if (store.getDataPtr(id)) |path_data| {
                                    switch (path_data.*) {
                                        .rect => |rect| {
                                            gui.text("{d:0>4}. [id={d:0>8}] Rect(x={d}, y={d}, w={d}, h={d})", .{
                                                i,
                                                id,
                                                rect.left(),
                                                rect.top(),
                                                rect.width(),
                                                rect.height(),
                                            });
                                        },
                                        .oval => |oval| {
                                            gui.text("{d:0>4}. [id={d:0>8}] Oval(x={d}, y={d}, w={d}, h={d})", .{
                                                i,
                                                id,
                                                oval.left(),
                                                oval.top(),
                                                oval.width(),
                                                oval.height(),
                                            });
                                        },
                                        .path => |path| {
                                            gui.text("{d:0>4}. [id={d:0>8}] Path(cmd={d}, vts={d})", .{
                                                i,
                                                id,
                                                path.commands.len,
                                                path.vertices.len,
                                            });
                                        },
                                    }
                                    gui.sameLine(.{});
                                    gui.pushFont(0);
                                    // if (gui.button(try std.fmt.allocPrintZ(arena.allocator(), "{s}##path_preview-{d}", .{ gui.default_font.magnifying_glass, id }), .{})) {
                                    //     state.view.path_preview_idx = id;
                                    // }
                                    gui.popFont();
                                }
                            }
                        } else {
                            gui.textUnformatted("No Paths");
                        }
                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Nodes", .{})) {
                        var store = &state.tree.nodes;
                        const total = store.pool.liveHandleCount();
                        if (total > 0) {
                            var i: u32 = 0;
                            var it = store.pool.liveHandles();
                            while (it.next()) |hdl| : (i += 1) {
                                const id: u32 = @bitCast(hdl);
                                if (store.getNodePtr(id)) |node| {
                                    gui.text("{d:0>4}. [id={d:0>8}] {s}", .{ i, id, @tagName(node.data) });
                                }
                            }
                        } else {
                            gui.textUnformatted("No Nodes");
                        }
                        gui.endTabItem();
                    }

                    if (gui.beginTabItem("Composes", .{})) {
                        var store = &state.tree.composes;
                        if (store.pool.liveHandleCount() > 0) {
                            var it = store.pool.liveHandles();
                            var i: u32 = 0;
                            while (it.next()) |hdl| : (i += 1) {
                                const id: u32 = @bitCast(hdl);
                                if (store.getComposePtr(id)) |comp| {
                                    const c_root = switch (comp.*) {
                                        .alpha_mask => |*cc| cc.root,
                                        .inv_alpha_mask => |*cc| cc.root,
                                        .luma_mask => |*cc| cc.root,
                                        .inv_luma_mask => |*cc| cc.root,
                                        else => 0,
                                    };
                                    gui.text("{d:0>4}. [id={d:0>8}] {s} root={d}", .{ i, id, @tagName(comp.*), c_root });
                                }
                            }
                        } else {
                            gui.textUnformatted("No Composes");
                        }
                        gui.endTabItem();
                    }

                    gui.endTabBar();
                }
            }
            gui.end();

            if (state.view.path_preview_idx) |idx| {
                gui.setNextWindowPos(.{ .x = 1000.0, .y = 200.0, .cond = .first_use_ever });
                gui.setNextWindowSize(.{ .w = 512.0, .h = 512.0, .cond = .once });
                if (gui.begin("Path Preview", .{ .flags = .{ .no_docking = true } })) {
                    const content_margin: f32 = 10.0;
                    const line_color = col.Color.initWithHex("#a4c0ef").toU32();
                    var tl = gui.getWindowPos();
                    tl[0] += content_margin;
                    tl[1] += content_margin;
                    const win_region = gui.getContentRegionAvail();
                    const win_w = win_region[0] - content_margin * 2.0;
                    const win_h = win_region[1] - content_margin * 2.0;
                    if (state.tree.paths.getDataPtr(idx)) |path_data| {
                        var path: Path = undefined;
                        switch (path_data.*) {
                            .rect => |rect| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                                path = try pb.toOwnedPath();
                            },
                            .oval => |oval| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                                path = try pb.toOwnedPath();
                            },
                            .path => |p| {
                                path = p;
                            },
                        }
                        // Calculate convex AABB
                        var min_x: f32 = std.math.inf(f32);
                        var min_y: f32 = std.math.inf(f32);
                        var max_x: f32 = -std.math.inf(f32);
                        var max_y: f32 = -std.math.inf(f32);
                        const points = path.vertices;
                        for (0..points.len / 2) |i| {
                            const x = points[i * 2 + 0];
                            const y = points[i * 2 + 1];
                            min_x = @min(min_x, x);
                            max_x = @max(max_x, x);
                            min_y = @min(min_y, y);
                            max_y = @max(max_y, y);
                        }
                        const w = max_x - min_x;
                        const h = max_y - min_y;
                        // Calculate offset and scale to center the path
                        const scale = @min(win_w / w, win_h / h);
                        const cx = (min_x + max_x) / 2.0;
                        const cy = (min_y + max_y) / 2.0;
                        const win_cx = win_w / 2.0;
                        const win_cy = win_h / 2.0;
                        const dx = win_cx - cx;
                        const dy = win_cy - cy;
                        const draw = gui.getWindowDrawList();
                        // Draw segments
                        const uv = gui.getFontTexUvWhitePixel();
                        const mat = math.mat2df_translate(dx, dy)
                            .mul(math.mat2df_translate(cx, cy))
                            .mul(math.mat2df_scales(scale))
                            .mul(math.mat2df_translate(-cx, -cy));
                        var p = TriMesh.ShapeProcessor.init(arena.allocator());
                        const mesh = p.buildStrokeMesh(arena.allocator(), path, mat, null, 1.0 / scale, .butt, .bevel, 4.0);
                        draw.primReserve(@intCast(mesh.ind.len), @intCast(mesh.pos.len / 2));
                        const tri = draw.getCurrentIndex();
                        for (0..mesh.pos.len / 2) |i| {
                            draw.primWriteVtx(.{ mesh.pos[i * 2], mesh.pos[i * 2 + 1] }, uv, line_color);
                        }
                        for (mesh.ind) |i| {
                            draw.primWriteIdx(@intCast(tri + i));
                        }
                        // Draw points
                        var vi: u32 = 0;
                        for (path.commands) |cmd| {
                            var draw_point = false;
                            var curr_x: f32 = 0.0;
                            var curr_y: f32 = 0.0;
                            switch (cmd) {
                                1 => { // move_to
                                    curr_x = (points[vi + 0] - cx) * scale + cx + dx;
                                    curr_y = (points[vi + 1] - cy) * scale + cy + dy;
                                    vi += 2;
                                    draw_point = true;
                                },
                                2 => { // line_to
                                    curr_x = (points[vi + 0] - cx) * scale + cx + dx;
                                    curr_y = (points[vi + 1] - cy) * scale + cy + dy;
                                    vi += 2;
                                    draw_point = true;
                                },
                                3 => { // quadratic_to
                                    curr_x = (points[vi + 2] - cx) * scale + cx + dx;
                                    curr_y = (points[vi + 3] - cy) * scale + cy + dy;
                                    vi += 4;
                                    draw_point = true;
                                },
                                4 => { // cubic_to
                                    curr_x = (points[vi + 4] - cx) * scale + cx + dx;
                                    curr_y = (points[vi + 5] - cy) * scale + cy + dy;
                                    vi += 6;
                                    draw_point = true;
                                },
                                else => {},
                            }
                            if (draw_point) {
                                draw.addCircleFilled(.{
                                    .p = [_]f32{ curr_x, curr_y },
                                    .r = 5.0,
                                    .col = 0xFF_FF_FF_FF,
                                });
                                draw.addCircle(.{
                                    .p = [_]f32{ curr_x, curr_y },
                                    .r = 7.0,
                                    .col = line_color,
                                    .thickness = 1.0,
                                });
                            }
                        }
                    }
                }
                gui.end();
            }
        }
    }

    ext.onFrameBegin();

    ext.onUpdateSceneTree();

    switch (state.view.mode) {
        .default => {
            try state.tileset_default.prepare(delta);
        },
        .xray => {
            try state.tileset_xray.prepare(delta);
        },
        .outline => {
            try state.tileset_outline.prepare(delta);
        },
    }
    // update tree
    try updateTree(&state.tree, state.tree.root);

    // canvas bounds
    if (state.view.draw_canvas_bounds) {
        const cb_size = 2.0;
        c.stroke.width = cb_size;
        c.stroke.color = col.teal;
        c.drawRect(3, canvas_area.left() / app.dpiScale() + cb_size * 0.5, canvas_area.top() / app.dpiScale() + cb_size * 0.5, canvas_area.width() / app.dpiScale() - cb_size * 1.0, canvas_area.height() / app.dpiScale() - cb_size * 1.0);
    }

    // Setup center viewport. (for rendering or capture)
    const compose_tilemap = ext.onPrepareCamera() or state.capture.hasRequest();
    var view_size = math.vec2fs(0.0);
    var camera = state.camera;
    // viewport of the center area (tilemap)
    var canvas_view = math.rect2f(0, 0, 0, 0);
    if (state.capture.hasRequest()) {
        if (state.capture.prepare(&state.tree, state.capture.root)) |setting| {
            view_size = setting.size;
            camera.offset = setting.offset;
            camera.zoom = setting.zoom;
            canvas_view = setting.clip_box;
        }
    }
    if (!state.capture.hasRequest()) {
        view_size = math.vec2f(canvas_area.width(), canvas_area.height());
        camera.offset = camera.offset.mul(app.dpiScale());
        camera.zoom *= app.dpiScale();
        canvas_view = math.rect2f(-canvas_area.left(), -canvas_area.top(), app.fbWidthf(), app.fbHeightf());
    }

    // draw tilemap
    if (compose_tilemap) {
        // update tilemap information
        const tilemap = &state.tilemap;
        tilemap.prepare(camera.offset, view_size, camera.zoom);
        // clear tilemap
        gfx.beginPass(.{
            .action = blk: {
                var action: gfx.PassAction = .{
                    .depth = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                    .stencil = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                };
                action.colors[0] = .{ .load_action = .CLEAR, .clear_value = col.transparent };
                break :blk action;
            },
            .attachments = tilemap.att,
        });
        gfx.endPass();

        // fetch tileset to render to
        switch (state.view.mode) {
            .default => {
                const tileset = &state.tileset_default;

                if (state.view.force_redraw) {
                    try tileset.freeTilesFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                }
                _ = try tileset.ensureSpaceFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                try composeVectorTiles(tilemap, tileset, &state.tree);
            },
            .xray => {
                const tileset = &state.tileset_xray;

                if (state.view.force_redraw) {
                    try tileset.freeTilesFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                }
                _ = try tileset.ensureSpaceFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                try composeXrayTiles(tilemap, tileset, &state.tree);

                bg_color = col.white;
            },
            .outline => {
                const tileset = &state.tileset_outline;

                if (state.view.force_redraw) {
                    try tileset.freeTilesFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                }
                _ = try tileset.ensureSpaceFor(tilemap.level, tilemap.col_begin, tilemap.col_end, tilemap.row_begin, tilemap.row_end);
                try composeOutlineTiles(tilemap, tileset, &state.tree);

                bg_color = col.white;
            },
        }
    }

    canvas.transform = math.mat2df_identity();
    ext.onOverlay();
    if (state.page.edit_mode == .editing) {
        state.page.drawGizmos(delta);
        if (state.page.isPointInContent(state.page.mouse.world.x(), state.page.mouse.world.y())) {
            if (!state.page.force_default_cursor) {
                app.setMouseCursor(app.MouseCursor.IBEAM);
            }
        }
    }

    // canvas ui
    {
        const tilemap = &state.tilemap;
        const size: f32 = TileMap.tile_size * tilemap.scale / app.dpiScale();
        const tilemap_world_view = tilemap.worldBBox();
        const topleft = tilemap_world_view.topLeft().add(canvas_area.topLeft()).div(app.dpiScale());
        const tile_offset = math.vec2f(@mod((camera.offset.x() - canvas_area.left()) / app.dpiScale(), size), @mod((camera.offset.y() - canvas_area.top()) / app.dpiScale(), size));

        const world_to_canvas = math.mat2df_translate(canvas_area.left(), canvas_area.top())
            .mul(math.mat2df_translate(-camera.offset.x(), -camera.offset.y()))
            .mul(math.mat2df_scales(camera.zoom));
        state.view.canvas_to_world = world_to_canvas.inverse();

        // grid
        if (state.view.show_tile_grid) {
            canvas.stroke.color = col.blueviolet;
            canvas.stroke.width = 1.0;

            var y: f32 = -tile_offset.y();
            while (y < canvas_area.bottom() / app.dpiScale()) : (y += size) {
                canvas.drawLine(0, 0.0, y, app.widthf(), y);
            }
            var x: f32 = -tile_offset.x();
            while (x < canvas_area.right() / app.dpiScale()) : (x += size) {
                canvas.drawLine(0, x, 0.0, x, app.heightf());
            }

            y = topleft.y();
            var r: i32 = tilemap.row_begin;
            while (r <= tilemap.row_end) : (r += 1) {
                x = topleft.x();
                var q: i32 = tilemap.col_begin;
                while (q <= tilemap.col_end) : (q += 1) {
                    var buf: [512]u8 = undefined;
                    const text = std.fmt.bufPrint(&buf, "[{d},{d}]", .{ q, r }) catch "";
                    try canvas.drawText(1, x + 4.0, y, text, .{
                        .color = col.red,
                    });
                    x += size;
                }
                y += size;
            }
        }

        // tile info
        if (state.view.show_tile_grid and state.view.show_tile_info) {
            var buf: [512]u8 = undefined;
            var text = std.fmt.bufPrint(&buf, "tilemap: [{d},{d}] -> [{d},{d}]", .{ tilemap.col_begin, tilemap.row_begin, tilemap.col_end, tilemap.row_end }) catch "";
            try canvas.drawText(1, (canvas_area.right() - 10.0) / app.dpiScale(), (canvas_area.top() + 6.0) / app.dpiScale(), text, .{
                .color = col.pink,
                .h_align = .right,
                .v_align = .top,
            });

            const off_x: f32 = 2.0;
            const off_y: f32 = 2.0;
            const line: f32 = 13.0;
            const info_color = col.green;
            const empty_color = col.pink;

            var y: f32 = topleft.y();
            var r: i32 = tilemap.row_begin;
            while (r <= tilemap.row_end) : (r += 1) {
                var x: f32 = topleft.x() + size;
                var q: i32 = tilemap.col_begin;
                while (q <= tilemap.col_end) : (q += 1) {
                    if (state.tilemap_debug.tile_infos.getPtr(.{ .col = q, .row = r })) |info| {
                        text = std.fmt.bufPrint(&buf, "masks: {d}", .{info.mask_total}) catch "";
                        try canvas.drawText(1, x - off_x, y + size - off_y - line * 0, text, .{
                            .color = info_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                        text = std.fmt.bufPrint(&buf, "clips: {d}", .{info.clip_total}) catch "";
                        try canvas.drawText(1, x - off_x, y + size - off_y - line * 1, text, .{
                            .color = info_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                        text = std.fmt.bufPrint(&buf, "strokes: {d}", .{info.stroke_total}) catch "";
                        try canvas.drawText(1, x - off_x, y + size - off_y - line * 2, text, .{
                            .color = info_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                        text = std.fmt.bufPrint(&buf, "fills: {d}", .{info.fill_total}) catch "";
                        try canvas.drawText(1, x - off_x, y + size - off_y - line * 3, text, .{
                            .color = info_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                        text = std.fmt.bufPrint(&buf, "nodes: {d}", .{info.nodes_total}) catch "";
                        try canvas.drawText(1, x - off_x, y + size - off_y - line * 4, text, .{
                            .color = info_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                    } else {
                        try canvas.drawText(1, x - off_x, y + size - off_y, "empty tile", .{
                            .color = empty_color,
                            .h_align = .right,
                            .v_align = .bottom,
                        });
                    }
                    x += size;
                }
                y += size;
            }
        }
    }

    // Render tilemap
    if (compose_tilemap) {
        const tilemap = &state.tilemap;
        // a. to capture buffer
        if (state.capture.hasRequest()) {
            state.capture.beginPass();
            var view = canvas_view;
            // Flip upside-down for GL backends
            if ((gfx.queryBackend() == .GLCORE) or (gfx.queryBackend() == .GLES3)) {
                view = math.rect2f(canvas_view.left(), canvas_view.bottom(), canvas_view.width(), -canvas_view.height());
            }
            postprocess.copy(.{
                .src = tilemap.img,
                .dst_rect = tilemap.worldBBox(),
                .dst_view = view,
                .filter = .smooth,
                .to_swapchain = false,
            });
            state.capture.endPass();
        }
        // b. to screen back buffer
        else {
            gfx.beginPass(.{
                .action = blk: {
                    var action: gfx.PassAction = .{
                        .depth = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                        .stencil = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
                    };
                    action.colors[0] = .{ .load_action = .CLEAR, .clear_value = bg_color };
                    break :blk action;
                },
                .swapchain = dino.swapchain(),
            });
            postprocess.copy(.{
                .src = tilemap.img,
                .dst_rect = tilemap.worldBBox(),
                .dst_view = canvas_view,
                .filter = .smooth,
                .to_swapchain = true,
            });
            gfx.endPass();
        }
    }

    // Render overlay and GUI
    gfx.beginPass(.{
        .action = blk: {
            var action: gfx.PassAction = .{
                .depth = .{ .load_action = .DONTCARE, .store_action = .DONTCARE },
            };
            action.colors[0] = .{ .load_action = .LOAD, .store_action = .STORE };
            break :blk action;
        },
        .swapchain = dino.swapchain(),
    });
    {
        if (!state.capture.hasRequest()) {
            // tileset debug view
            if (state.view.show_tileset) {
                const tileset = switch (state.view.mode) {
                    .default => &state.tileset_default,
                    .xray => &state.tileset_xray,
                    .outline => &state.tileset_outline,
                };

                // dimmer
                postprocess.fill(.{
                    .color = col.Color.init(0.0, 0.0, 0.0, 0.25),
                    .rect = canvas_view,
                    .view = canvas_view,
                    .blend = true,
                    .to_swapchain = true,
                });

                const tileset_size: f32 = 600;
                const dpi = app.dpiScale();

                const img = gfx.queryAttachmentsDesc(tileset.att).colors[0].image;
                postprocess.copy(.{
                    .src = img,
                    .dst_rect = math.rect2f(
                        canvas_area.width() - (tileset_size) * dpi,
                        canvas_area.height() - (tileset_size) * dpi,
                        tileset_size * dpi,
                        tileset_size * dpi,
                    ),
                    .dst_view = canvas_view,
                    .filter = .smooth,
                    .blend = false,
                    .to_swapchain = true,
                });

                const xx = canvas_area.right() / 2 - tileset_size;
                const yy = canvas_area.bottom() / 2 - tileset_size;
                // show tileset outline
                const stroke_w = 2.0;
                c.stroke.width = stroke_w;
                c.stroke.color = col.blue;
                c.drawRect(0, xx + stroke_w / 2, yy + stroke_w / 2, tileset_size - stroke_w, tileset_size - stroke_w);
                // show freeing tiles
                const tile_size = tileset_size / TileSet.page_tile_row_count;
                for (0..tileset.tiles.len) |idx| {
                    if (tileset.tiles[idx]) continue;
                    const x = @as(f32, @floatFromInt((idx % TileSet.page_tile_row_count))) * tile_size;
                    const y = @as(f32, @floatFromInt((idx / TileSet.page_tile_row_count))) * tile_size;
                    c.stroke.color = col.red;
                    c.stroke.width = 0.5;
                    // red cross
                    c.drawLine(0, x + xx, y + yy, x + xx + tile_size, y + yy + tile_size);
                    c.drawLine(0, x + xx + tile_size, y + yy, x + xx, y + yy + tile_size);
                }
            }

            c.render();
        }

        gui.render();
    }
    gfx.endPass();

    c.endFrame();
    ext.onFrameEnd();

    // Query pixels from capture
    if (state.capture.hasRequest()) {
        if (state.capture.fetch()) |img| {
            defer @constCast(&img).deinit();
            state.capture.finish();

            switch (state.capture.format) {
                .png => {
                    ext.onCapture(@ptrCast(img.data.ptr), img.data.len);
                },
                .jpg => {
                    ext.onCapture(@ptrCast(img.data.ptr), img.data.len);
                },
            }
        }
    }

    gfx.commit();
}

pub fn event(ev: *const app.Event) !void {
    const page = &state.page;
    // if the edit_mode is .normal, we want to pause the gui event listener
    const mouse_world = math.vec2f(
        ev.mouse_x / app.dpiScale(),
        ev.mouse_y / app.dpiScale(),
    );
    state.view.mouse_world = mouse_world;
    if (page.edit_mode == .normal) return;
    if (!gui.handleEvent(ev)) {
        try page.handleEvent(ev);
        if (page.edit_node_id) |node_id| {
            if (ev.key_code != app.Keycode.INVALID or ev.composition_len != 0) {
                ext.onNodeUpdate(node_id, 2);
                api.markNodeChanged(@bitCast(node_id));
            }
        }
    }
}

pub fn deinit() !void {
    {
        var it = state.stroke_path_cache.valueIterator();
        while (it.next()) |pnm| {
            pnm.deinit();
        }
        state.stroke_path_cache.deinit();
    }
    {
        var it = state.base_path_cache.valueIterator();
        while (it.next()) |pnm| {
            // we don't want to destroy base path, they belongs to tree and its storage
            pnm.path = null;
            pnm.deinit();
        }
        state.base_path_cache.deinit();
    }

    state.update_list.deinit();
    state.update_size_cache.deinit();

    // state.text_engine.deinit();
    shaping.deinit();

    state.tree.deinit();

    state.capture.deinit();

    state.tilemap.deinit();
    state.tileset_outline.deinit();
    state.tileset_xray.deinit();
    state.tileset_default.deinit();

    canvas.deinit();

    tile_render.deinit();
    winding_buffer.destroy(wb);
    winding_buffer.deinit();

    compose.deinit();
    postprocess.deinit();
    gfx.destroyImage(img_checkboard);

    // gui.plot.deinit();
    gui.deinit();

    if (state.assets_dir.len > 0) {
        dino.galloc.free(state.assets_dir);
    }

    image.deinit();
    gfx.deinit();
    {
        var it = state.file.records.valueIterator();
        while (it.next()) |rec| {
            if (rec.complete and rec.file.len > 0) {
                dino.galloc.free(rec.file);
            }
        }
        state.file.records.deinit();
    }
    {
        for (state.file.requests.items) |req| {
            dino.galloc.free(req.url);
            switch (req.payload) {
                .font => |font_settings| {
                    dino.galloc.free(font_settings.family);
                },
                else => {},
            }
        }
        state.file.requests.deinit();
    }
    fetch.deinit();
    dino.galloc.free(fetch_buf.font[0]);
    dino.galloc.free(fetch_buf.font[1]);
    dino.galloc.free(fetch_buf.image);
    dino.galloc.free(fetch_buf.other);
    arena.deinit();
}

const FileType = enum(u32) {
    unknown,

    png,
    jpg,
    gif,

    svg,
    park,

    ttf,
    otf,

    pub fn guess(name: []const u8) FileType {
        const basename = std.fs.path.basename(name);
        // pixel image
        if (std.ascii.endsWithIgnoreCase(basename, ".png")) {
            return .png;
        } else if (std.ascii.endsWithIgnoreCase(basename, "jpeg") or std.ascii.endsWithIgnoreCase(basename, "jpg")) {
            return .jpg;
        } else if (std.ascii.endsWithIgnoreCase(basename, "gif")) {
            return .gif;
        }
        // scene
        if (std.ascii.endsWithIgnoreCase(basename, "park")) {
            return .park;
        } else if (std.ascii.endsWithIgnoreCase(basename, "svg")) {
            return .svg;
        }
        // font
        if (std.ascii.endsWithIgnoreCase(basename, "ttf")) {
            return .ttf;
        }
        return .unknown;
    }
};

const FetchUserData = extern struct {
    file_type: u32 = 0,
    res_idx: u32 = 0,
};
fn fileLoaded(url: []const u8, file: []const u8, ud: *FetchUserData) void {
    const tree = &state.tree;
    if (std.meta.intToEnum(FileType, ud.file_type)) |file_type| {
        switch (file_type) {
            .png, .jpg, .gif => {
                var img = dino.image.Image.loadFromMemory(file, 4) catch unreachable;
                defer img.deinit();

                tree.images.clearData(ud.res_idx) catch {};
                if (tree.images.initWithData(ud.res_idx, .{ .data = img.data, .size = math.vec2f(@floatFromInt(img.width), @floatFromInt(img.height)), .tag = switch (file_type) {
                    .png => .png,
                    .jpg => .jpg,
                    .gif => .gif,
                    else => .data,
                } })) {
                    tree.images.setState(ud.res_idx, .valid) catch unreachable;

                    _ = gui.backend.makeImage(.{
                        .image = tree.images.getGfxImage(ud.res_idx) orelse .{},
                    });
                } else |_| {
                    tree.images.setState(ud.res_idx, .failed) catch unreachable;
                }

                // mark node updated that is using this image
                var it = tree.nodes.pool.liveHandles();
                while (it.next()) |hdl| {
                    if (tree.nodes.getNodePtr(@bitCast(hdl))) |node| {
                        if (node.data == .path) {
                            if (node.data.path.fills[0]) |fill| {
                                if (fill.paint_tag == .image and fill.paint_id == ud.res_idx) {
                                    api.markNodeChanged(@bitCast(hdl));
                                }
                            }
                            if (node.data.path.strokes[0]) |stroke| {
                                if (stroke.paint_tag == .image and stroke.paint_id == ud.res_idx) {
                                    api.markNodeChanged(@bitCast(hdl));
                                }
                            }
                        }
                    }
                }
            },
            .otf, .ttf => {
                // find matching request from loading queue and trigger DAG propagation
                handleLoadRequest(&state.file.requests, url, file, ud) catch |err| {
                    dino.log.err(.file, "Failed to handle '{s}': {s}", .{
                        url,
                        @errorName(err),
                    });
                };
            },
            else => {},
        }
    } else |_| {}
}

const FileRequest = struct {
    url: []const u8,
    payload: union(enum) {
        image: struct {
            /// image ResID
            target_rid: u32,
        },
        font: FontDB.FontSetting,
    },
};
const FileRequestQueue = std.ArrayList(FileRequest);

/// Duplicate using `galloc` and make sure it is compatible with C APIs
fn dupeZ(text: []const u8) ![]const u8 {
    if (text[text.len - 1] == 0) {
        return try dino.galloc.dupe(u8, text);
    } else {
        return try dino.galloc.dupeZ(u8, text);
    }
}
/// Get the string without sentinal terminate
fn noSentinalStr(text: []const u8) []const u8 {
    return if (text[text.len - 1] == 0) text[0 .. text.len - 1] else text;
}

fn fetchFont(font: FontDB.FontSetting, url: []const u8) !void {
    // const testUrl = if (font.style == .normal) testUrl_normal(font.weight) else testUrl_italic(font.weight);
    var req = FileRequest{
        .url = url,
        .payload = .{
            .font = font,
        },
    };
    var ud: FetchUserData = .{
        .file_type = @intFromEnum(FileType.ttf),
    };
    const url_hash = dino.utils.nameHash(noSentinalStr(url));
    if (state.file.records.getPtr(url_hash)) |rec| {
        if (rec.complete) {
            std.log.debug("[font] already cached '{s}'", .{req.url});
            try handleLoadedFile(req, rec.file, &ud);
        }
    } else {
        req.url = try dupeZ(req.url);
        req.payload.font.family = try dino.galloc.dupe(u8, req.payload.font.family);
        try state.file.requests.append(req);

        _ = fetch.send(.{
            .path = @ptrCast(req.url),
            .callback = fileLoadedFromFetch,
            .buffer = utils.asRange(fetch_buf.font[fetch_buf.next_index]),
            .user_data = utils.asRange(&ud),
            .channel = fetch_buf.next_index,
        });
        fetch_buf.next_index += 1;
        fetch_buf.next_index %= 2;
        try state.file.records.put(dino.utils.nameHash(noSentinalStr(url)), .{});
        std.log.debug("[font] load from url '{s}'", .{url});
    }
}

fn handleLoadRequest(queue: *FileRequestQueue, url: []const u8, file: []const u8, ud: *FetchUserData) !void {
    for (0..queue.items.len) |i| {
        const req = queue.items[i];
        if (std.mem.eql(u8, url, req.url)) {
            try handleLoadedFile(req, file, ud);
            // cache loaded file and update record flag
            const url_hash = dino.utils.nameHash(url);
            if (state.file.records.getPtr(url_hash)) |rec| {
                rec.complete = true;
                rec.file = try dino.galloc.dupe(u8, file);
            }
            _ = queue.swapRemove(i);
            break;
        }
    }
}

fn handleLoadedFile(req: FileRequest, file: []const u8, ud: *FetchUserData) !void {
    switch (req.payload) {
        .image => {},
        .font => |font_setting| {
            _ = ud;
            var new_loaded = false;
            if (!state.tree.font_db.contains(font_setting)) {
                const font_rid = try state.tree.font_db.loadFromMemory(font_setting, file);
                const font_info = state.tree.font_db.infos.get(font_rid).?;
                var setting = font_setting;
                setting.family = font_info.family;
                setting.style = font_info.style;
                new_loaded = true;
            }
            // mark text nodes depending on this font dirty
            const pool = &state.tree.nodes.pool;
            var it = pool.liveHandles();
            while (it.next()) |h| {
                const node_ptr: *sd.NodeData = try pool.getColumnPtr(h, .data);
                if (node_ptr.data == .text) {
                    if (state.tree.text_docs.getDataPtr(node_ptr.data.text.doc_id)) |doc_ptr| {
                        const style_ptr = doc_ptr.spans.items[0].style;
                        const text_font = style_ptr.font orelse undefined;
                        if (state.tree.font_db.getPtr(text_font)) |font_ptr| {
                            if (std.mem.eql(u8, text_font.family, font_setting.family) or doc_ptr.hasFontFamily(font_ptr.id)) {
                                if (((text_font.style == .normal) and (font_setting.style == .normal)) or
                                    ((text_font.style == .italic) and (font_setting.style != .normal)))
                                {
                                    if (@intFromEnum(text_font.weight) == @intFromEnum(font_setting.weight)) {
                                        // FIXME: support `variable` weight
                                        const node_id: u32 = h.id;
                                        if (state.base_path_cache.getPtr(node_id)) |ptr| {
                                            if (ptr.mesh) |mesh| {
                                                dino.galloc.free(mesh.vertices);
                                            }
                                        }
                                        _ = state.base_path_cache.remove(node_id);
                                        // update node geometry
                                        ext.onNodeUpdate(node_id, 2);
                                        api.markNodeChanged(node_id);
                                        if (new_loaded) {
                                            try state.update_size_cache.put(node_id, true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
    }
}

fn fileLoadedFromFetch(res: [*c]const fetch.Response) callconv(.C) void {
    if (!res.*.fetched) {
        dino.log.err(.asset, ">> Failed to load '{s}': {any}", .{ res.*.path, res.* });
        return;
    }
    const ptr: [*]const u8 = @ptrCast(res.*.data.ptr);
    const base = res.*.data_offset;
    const file = ptr[base..(base + res.*.data.size)];

    const ud: *FetchUserData = @ptrCast(@alignCast(res.*.user_data));

    fileLoaded(std.mem.sliceTo(res.*.path, 0), file, ud);
}
fn fileLoadedFromHtml5Fetch(res: [*c]const app.Html5FetchResponse) callconv(.C) void {
    if (!res.*.succeeded) {
        dino.log.err(.asset, ">> Failed to load '{d}': {any}", .{ res.*.file_index, res.* });
        return;
    }

    const ptr: [*]const u8 = @ptrCast(res.*.data.ptr);
    const file = ptr[0..res.*.data.size];

    const ud: *FetchUserData = @ptrCast(@alignCast(res.*.user_data));
    defer dino.galloc.destroy(ud);

    fileLoaded(std.mem.sliceTo(res.*.path, 0), file, ud);
}

const composeVectorTiles = struct {
    pub fn composeTiles(tilemap: *TileMap, tileset: *TileSet, tree: *SceneTree) !void {
        // state.tilemap_debug.reset();

        var tile_walker = tile_render.walkTileMap(tilemap, tileset);
        while (tile_walker.next()) |tile| {
            const tile_bg = col.white;

            var debug_info: state.tilemap_debug.TileInfo = .{};
            var ctx: Context = .{
                .tree = tree,
                .debug_info = &debug_info,
                .tile_world_bbox = tile.tile_world_bbox,
                .world_to_tile = tile.world_to_tile,
                .tile_bg = tile_bg,
                .canvas_stack = CanvasStack.init(arena.allocator()),
            };
            ctx.setup();

            try renderTree(&ctx, tree.root);
            state.num_tiles_render += 1;

            const tile_canvas = ctx.popCanvas();
            try tile.finish(tile_bg, tile_canvas.*, state.capture.hasRequest());
            ctx.destroyCanvas(tile_canvas);

            ctx.canvas_stack.deinit();

            // Collect tile debug info
            if (!debug_info.isEmpty()) {
                try state.tilemap_debug.tile_infos.put(.{ .col = tile.col, .row = tile.row }, debug_info);
            }
        }
        tile_walker.finish();
    }

    const CanvasStack = std.ArrayList(*TileCanvas);
    const Context = struct {
        tree: *SceneTree,
        debug_info: *state.tilemap_debug.TileInfo,
        tile_world_bbox: math.Rect2f,
        world_to_tile: math.Mat2Df,
        tile_bg: col.Color,
        canvas_stack: CanvasStack,
        pub fn setup(ctx: *Context) void {
            const tile_canvas = ctx.makeCanvas();
            ctx.pushCanvas(tile_canvas);
        }

        pub fn topCanvas(ctx: *Context) *TileCanvas {
            return ctx.canvas_stack.getLast();
        }

        pub fn makeCanvas(ctx: *Context) *TileCanvas {
            _ = ctx;
            const c = arena.allocator().create(TileCanvas) catch undefined;
            const tile_canvas = tile_render.makeCanvas();
            c.* = tile_canvas;
            c.*.need_clear = true;
            return c;
        }
        pub fn destroyCanvas(ctx: *Context, tile_canvas: *TileCanvas) void {
            _ = ctx;
            tile_render.destroyCanvas(tile_canvas.*) catch {};
            arena.allocator().destroy(tile_canvas);
        }

        pub fn pushCanvas(ctx: *Context, tile_canvas: *TileCanvas) void {
            ctx.canvas_stack.append(tile_canvas) catch {};
        }
        /// Remember to call `destroyCanvas()`
        pub fn popCanvas(ctx: *Context) *TileCanvas {
            return ctx.canvas_stack.pop().?;
        }
    };

    const rect_one = math.rect2fse(0, 0, 1, 1);

    fn isNodeVisible(ctx: *Context, node: *sd.NodeData, node_id: u32) bool {
        switch (node.data) {
            .group => |*n| if (!node.visible or (n.opacity < 1e-2)) return false,
            .path => |_| if (!node.visible) return false,
            .text => |_| if (!node.visible) return false,
        }
        if (ctx.tree.nodes.getBBoxPtr(node_id)) |bbox| {
            if (bbox.visual_aabb) |aabb| {
                // check if it is within target tilemap area
                if (!aabb.isEmpty() and aabb.overlaps(ctx.tile_world_bbox)) {
                    return true;
                }
            }
        }
        return false;
    }

    fn renderTree(ctx: *Context, node_rid: u32) !void {
        const tree = ctx.tree;
        const node_ptr_opt = tree.nodes.getNodePtr(node_rid);
        if (node_ptr_opt) |node_ptr| {
            state.num_nodes_render += 1;
            switch (node_ptr.data) {
                .group => |*node| {
                    if (!isNodeVisible(ctx, node_ptr, node_rid)) return;

                    // [compose begin]
                    var clip_overlap: math.Intersect = .none;
                    for (0..node.compose.len) |i| {
                        if (tree.composes.getComposePtr(node.compose[i])) |comp| {
                            switch (comp.*) {
                                .clip => |*cd| {
                                    try updateSubTree(tree, cd.root, true);
                                    // clip based on computed obb
                                    if (ctx.tree.nodes.getOBBoxPtr(cd.root)) |obb| {
                                        clip_overlap = obb.world.intersectWithRect(ctx.tile_world_bbox);
                                        if ((clip_overlap == .overlaps) or (clip_overlap == .contained)) {
                                            var compose_ctx = ctx.*;
                                            compose_ctx.canvas_stack = CanvasStack.init(arena.allocator());
                                            compose_ctx.setup();

                                            const tile_bg = ctx.tile_bg;
                                            ctx.tile_bg = col.transparent;
                                            try renderClipTree(&compose_ctx, cd.root);
                                            ctx.tile_bg = tile_bg;

                                            const tile_canvas = compose_ctx.popCanvas();
                                            ctx.pushCanvas(tile_canvas);
                                            const new_canvas = ctx.makeCanvas();
                                            ctx.pushCanvas(new_canvas);

                                            compose_ctx.canvas_stack.deinit();

                                            ctx.debug_info.clip_total += 1;
                                        } else if (clip_overlap == .none) {
                                            return;
                                        }
                                    }
                                },
                                .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |*cd| {
                                    var compose_ctx = ctx.*;
                                    compose_ctx.canvas_stack = CanvasStack.init(arena.allocator());
                                    compose_ctx.setup();

                                    const tile_bg = ctx.tile_bg;
                                    ctx.tile_bg = col.transparent;
                                    try updateSubTree(tree, cd.root, true);
                                    try renderTree(&compose_ctx, cd.root);
                                    // TODO: render `cd.mask`
                                    ctx.tile_bg = tile_bg;

                                    const tile_canvas = compose_ctx.popCanvas();
                                    ctx.pushCanvas(tile_canvas);
                                    const new_canvas = ctx.makeCanvas();
                                    ctx.pushCanvas(new_canvas);

                                    compose_ctx.canvas_stack.deinit();

                                    ctx.debug_info.mask_total += 1;
                                },
                                else => {},
                            }
                        }
                    }

                    // [opacity begin]
                    var has_opacity_canvas = false;
                    if (node.opacity < 0.99) {
                        const tile_canvas = ctx.makeCanvas();
                        ctx.pushCanvas(tile_canvas);
                        has_opacity_canvas = true;
                    }

                    // [blend mode begin]
                    var has_blend_canvas = false;
                    if (node.blend != .normal) {
                        const tile_canvas = ctx.makeCanvas();
                        ctx.pushCanvas(tile_canvas);
                        has_blend_canvas = true;
                    }

                    ctx.debug_info.nodes_total += 1;

                    // render children
                    var iter = tree.nodes.children(node_rid);
                    // TODO: check whether this transparent bg setting is reasonable for fixing mask black bg issue
                    ctx.tile_bg = col.transparent;
                    while (iter.next()) |child_id| {
                        try renderTree(ctx, child_id);
                    }

                    // [opacity end]
                    if (has_opacity_canvas) {
                        const tile_canvas = ctx.popCanvas();
                        if (!tile_canvas.need_clear) {
                            const target_canvas = ctx.topCanvas();
                            if (target_canvas.need_clear) {
                                target_canvas.begin(.{ .clear = ctx.tile_bg });
                                target_canvas.need_clear = false;
                            } else {
                                target_canvas.begin(.keep);
                            }
                            compose.tintImage(.{
                                .src = tile_canvas.img,
                                .dst_view = rect_one,
                                .dst_rect = rect_one,
                                .tint = col.Color.init(1.0, 1.0, 1.0, node.opacity),
                                .filter = .pixel,
                                .to_swapchain = false,
                                .flip_y = true,
                            });
                            target_canvas.end();
                        }
                        ctx.destroyCanvas(tile_canvas);
                    }
                    // [blend mode end]
                    if (has_blend_canvas) {
                        const target = ctx.makeCanvas();
                        const src = ctx.popCanvas();
                        const dst = ctx.popCanvas();
                        if (!src.need_clear) {
                            if (dst.need_clear) {
                                dst.begin(.{ .clear = ctx.tile_bg });
                                dst.need_clear = false;
                                dst.end();
                            }

                            target.begin(.{ .clear = ctx.tile_bg });
                            target.need_clear = false;
                            compose.blendImage(.{
                                .src_1 = src.img,
                                .src_2 = dst.img,
                                .blend_mode = node.blend,
                                .dst_view = rect_one,
                                .dst_rect = rect_one,
                                .to_swapchain = false,
                                .flip_y = true,
                            });
                            target.end();

                            ctx.destroyCanvas(src);
                            ctx.destroyCanvas(dst);
                            ctx.pushCanvas(target);
                        } else {
                            ctx.destroyCanvas(src);
                            ctx.destroyCanvas(target);
                            ctx.pushCanvas(dst);
                        }
                    }

                    // [compose end]
                    for (0..node.compose.len) |inv_i| {
                        const i = node.compose.len - inv_i - 1;
                        if (tree.composes.getComposePtr(node.compose[i])) |comp| {
                            switch (comp.*) {
                                .clip => {
                                    if ((clip_overlap == .overlaps) or (clip_overlap == .contained)) {
                                        const src = ctx.popCanvas();
                                        const mask = ctx.popCanvas();
                                        const target_canvas = ctx.topCanvas();
                                        if (target_canvas.need_clear) {
                                            target_canvas.begin(.{ .clear = ctx.tile_bg });
                                            target_canvas.need_clear = false;
                                        } else {
                                            target_canvas.begin(.keep);
                                        }
                                        if (!src.need_clear and !mask.need_clear) {
                                            compose.maskImage(.{
                                                .src = src.img,
                                                .msk = mask.img,
                                                .dst_view = rect_one,
                                                .dst_rect = rect_one,
                                                .msk_type = .alpha,
                                                .filter = .pixel,
                                                .to_swapchain = false,
                                                .flip_y = true,
                                            });
                                        } else if (!src.need_clear and mask.need_clear) {
                                            // fully clipped
                                        }
                                        target_canvas.end();
                                        ctx.destroyCanvas(src);
                                        ctx.destroyCanvas(mask);
                                    }
                                },
                                .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => {
                                    const src = ctx.popCanvas();
                                    const mask = ctx.popCanvas();
                                    const target_canvas = ctx.topCanvas();
                                    if (target_canvas.need_clear) {
                                        target_canvas.begin(.{ .clear = ctx.tile_bg });
                                        target_canvas.need_clear = false;
                                    } else {
                                        target_canvas.begin(.keep);
                                    }
                                    if (!src.need_clear and !mask.need_clear) {
                                        compose.maskImage(.{
                                            .src = src.img,
                                            .msk = mask.img,
                                            .dst_view = rect_one,
                                            .dst_rect = rect_one,
                                            .msk_type = switch (comp.*) {
                                                .alpha_mask => .alpha,
                                                .inv_alpha_mask => .inv_alpha,
                                                .luma_mask => .luma,
                                                .inv_luma_mask => .inv_luma,
                                                else => .alpha,
                                            },
                                            .filter = .pixel,
                                            .to_swapchain = false,
                                            .flip_y = true,
                                        });
                                    } else if (!src.need_clear and mask.need_clear) {
                                        // mask not applied at all
                                    }
                                    target_canvas.end();
                                    ctx.destroyCanvas(src);
                                    ctx.destroyCanvas(mask);
                                },
                                else => {},
                            }
                        }
                    }
                },
                .path => |*node| {
                    if (!isNodeVisible(ctx, node_ptr, node_rid)) return;

                    const target_canvas = ctx.topCanvas();

                    if (tree.paths.getDataPtr(node.path_id)) |path_data| {
                        if (tree.nodes.getTransformPtr(node_rid)) |transform| {
                            const path_mat = transform.world orelse math.mat2df_identity();
                            const mat = ctx.world_to_tile.mul(path_mat);
                            // TODO: reduce the fill node and stroke node to 1, and add the fill/stroke data to the fill/stroke list
                            // Update the pattern matching logic for fills and strokes
                            if (node.fills[0]) |fill| {
                                if (node.strokes[0]) |stroke| {
                                    try renderPath(ctx, target_canvas, node.path_id, fill, stroke, path_data, mat);
                                } else {
                                    const default_stroke = sd.StrokePaintData{}; // Default initialization
                                    try renderPath(ctx, target_canvas, node.path_id, fill, default_stroke, path_data, mat);
                                }
                            } else if (node.strokes[0]) |stroke| {
                                const default_fill = sd.FillPaintData{}; // Default initialization
                                try renderPath(ctx, target_canvas, node.path_id, default_fill, stroke, path_data, mat);
                            }
                        }
                    }
                },
                .text => |*node| {
                    if (!isNodeVisible(ctx, node_ptr, node_rid)) return;

                    const target_canvas = ctx.topCanvas();

                    // render as path
                    if (state.tree.text_docs.getPathsPtr(node.doc_id)) |block_path_list| {
                        var builder = Path.Builder.init(arena.allocator());
                        for (block_path_list.items) |block| {
                            const p = block.path;
                            builder.append(p) catch |err| {
                                dino.log.err(.api, "getTextPathEx error: {}", .{err});
                            };
                        }
                        const path = Path.cast(builder);
                        if (tree.nodes.getTransformPtr(node_rid)) |transform| {
                            const path_mat = transform.world orelse math.mat2df_identity();
                            const mat = ctx.world_to_tile.mul(path_mat);
                            var path_data = PathData{ .path = path };
                            const path_id = ext.onGetTextPathId(node_rid);
                            try renderPath(ctx, target_canvas, path_id, node.fill, node.stroke, &path_data, mat);
                        }
                    }
                },
            }
        }
    }

    inline fn geomId(elem_id: u32, sub_id: u16, stroke_id: u16) u64 {
        const Pack = packed struct {
            elem_id: u32,
            sub_id: u16,
            stroke_id: u16,
        };
        return @bitCast(Pack{
            .elem_id = elem_id,
            .sub_id = sub_id,
            .stroke_id = stroke_id,
        });
    }

    fn renderWB(path_rid: u32, sub_id: u16, stroke_id: u16, geom_data: *const PathData, stroke_data: *const sd.StrokeData, mat: math.Mat2Df) !void {
        const geom_id = geomId(path_rid, sub_id, stroke_id);
        if (stroke_id > 0) {
            var stroked_path: Path = .{};
            if (!winding_buffer.hasCachedPath(geom_id)) {
                var stroke_path: Path = undefined;
                switch (geom_data.*) {
                    .rect => |rect| {
                        var pb = Path.Builder.init(arena.allocator());
                        try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                        stroke_path = try pb.toOwnedPath();
                    },
                    .oval => |oval| {
                        var pb = Path.Builder.init(arena.allocator());
                        try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                        stroke_path = try pb.toOwnedPath();
                    },
                    .path => |p| stroke_path = p,
                }
                stroked_path = stroker.strokePath(dino.galloc, stroke_path, stroke_data.*);
                defer if (geom_data.* != .path) stroke_path.deinit(dino.galloc);
            }
            try winding_buffer.drawPathWithID(&wb, geom_id, stroked_path, mat, 0.5);
        } else {
            switch (geom_data.*) {
                .rect => |rect| {
                    var pb = Path.Builder.init(arena.allocator());
                    try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                    try winding_buffer.drawPathWithID(&wb, geom_id, pb, mat, 0.5);
                },
                .oval => |oval| {
                    var pb = Path.Builder.init(arena.allocator());
                    try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                    try winding_buffer.drawPathWithID(&wb, geom_id, pb, mat, 0.5);
                },
                .path => |p| {
                    const base_path = try state.base_path_cache.getOrPut(path_rid);
                    if (!base_path.found_existing) {
                        base_path.value_ptr.* = .{
                            .path = geom_data.*,
                            .mesh = null,
                        };
                    }
                    if (base_path.value_ptr.fetchMesh()) |mesh| {
                        _ = mesh;
                        try winding_buffer.drawPathWithID(&wb, geom_id, p, mat, 0.01);
                    }
                },
            }
        }
    }

    fn renderCover(tree: *const SceneTree, paint: struct { paint_tag: sd.PaintTag, paint_id: u32, opacity: f32, geom_bbox: math.Rect2f }) !void {
        switch (paint.paint_tag) {
            .color => {
                if (tree.colors.getRGBA32(paint.paint_id)) |color| {
                    winding_buffer.fillColor(.{
                        .src = &wb,
                        .color = color,
                        // .opacity = paint.opacity,
                        .to_swapchain = false,
                        .mat = math.mat2df_identity(),
                    });
                }
            },
            .gradient => {
                if (tree.gradients.getDataPtr(paint.paint_id)) |gradient_data| {
                    winding_buffer.fillGradient(.{
                        .src = &wb,
                        .mat = math.mat2df_identity(),
                        .paint = .{
                            .img = tree.gradients.img,
                            .tex_y = try tree.gradients.getPos(paint.paint_id),
                            .tag = gradient_data.tag,
                            .mat = gradient_data.mat,
                            .opacity = paint.opacity,
                        },
                        .to_swapchain = false,
                    });
                }
            },
            .image => {
                const img_state = try @constCast(&tree.images).getState(paint.paint_id);
                switch (img_state) {
                    .loading => {
                        // Draw checkboard pattern
                        if (tree.images.getDataPtr(paint.paint_id)) |image_data| {
                            const desc = gfx.queryImageDesc(img_checkboard);
                            winding_buffer.fillImage(.{
                                .src = &wb,
                                .mat = math.mat2df_identity(),
                                .paint = .{
                                    .img = img_checkboard,
                                    .mat = sd.ImageFillMode.toMat(
                                        image_data.mode,
                                        math.vec2f(@floatFromInt(desc.width), @floatFromInt(desc.height)),
                                        paint.geom_bbox.size(),
                                    ),
                                    .opacity = paint.opacity,
                                },
                                .to_swapchain = false,
                            });
                        }
                    },
                    .valid => {
                        if (tree.images.getDataPtr(paint.paint_id)) |image_data| {
                            const img = tree.images.getGfxImage(paint.paint_id) orelse img_checkboard;
                            const desc = gfx.queryImageDesc(img);
                            winding_buffer.fillImage(.{
                                .src = &wb,
                                .mat = math.mat2df_identity(),
                                .paint = .{
                                    .img = img,
                                    .mat = sd.ImageFillMode.toMat(
                                        image_data.mode,
                                        math.vec2f(@floatFromInt(desc.width), @floatFromInt(desc.height)),
                                        paint.geom_bbox.size(),
                                    ),
                                    .opacity = paint.opacity,
                                },
                                .to_swapchain = false,
                            });
                        }
                    },
                    else => {},
                }
            },
            else => {},
        }
    }

    fn renderPath(ctx: *Context, target_canvas: *TileCanvas, path_rid: u32, path_fill: sd.FillPaintData, path_stroke: sd.StrokePaintData, path_data: *PathData, mat: math.Mat2Df) !void {
        const tree = ctx.tree;

        // fill
        if (path_fill.paint_tag != .none and path_data.path.vertices.len > 0) {
            // winding buffer
            try renderWB(path_rid, 0, 0, path_data, undefined, mat);

            // the path mesh's vertices could be 0, so we need to check if it is valid
            const base_path = try state.base_path_cache.getOrPut(path_rid);
            if (base_path.value_ptr.*.mesh) |mesh| {
                _ = mesh;
                // cover
                if (target_canvas.need_clear) {
                    target_canvas.begin(.{ .clear = ctx.tile_bg });
                    target_canvas.need_clear = false;
                } else {
                    target_canvas.begin(.keep);
                }
                const geom_bbox = path_data.bbox();
                try renderCover(tree, .{
                    .paint_tag = path_fill.paint_tag,
                    .paint_id = path_fill.paint_id,
                    .opacity = path_fill.opacity,
                    .geom_bbox = geom_bbox,
                });
                target_canvas.end();

                ctx.debug_info.fill_total += 1;
            }
        }

        // stroke
        if (path_stroke.paint_tag != .none and path_data.path.vertices.len > 0) {
            if (tree.strokes.getDataPtr(path_stroke.data_id)) |stroke_data| {
                if (stroke_data.linewidth == 0.0) return;
                // winding buffer
                try renderWB(path_rid, 0, 1, path_data, stroke_data, mat);

                // cover
                if (target_canvas.need_clear) {
                    target_canvas.begin(.{ .clear = ctx.tile_bg });
                    target_canvas.need_clear = false;
                } else {
                    target_canvas.begin(.keep);
                }
                const geom_bbox = path_data.bbox();
                try renderCover(tree, .{
                    .paint_tag = path_stroke.paint_tag,
                    .paint_id = path_stroke.paint_id,
                    .opacity = path_stroke.opacity,
                    .geom_bbox = geom_bbox,
                });
                target_canvas.end();

                ctx.debug_info.stroke_total += 1;
            }
        }
    }

    fn renderClipTree(ctx: *Context, node_id: u32) !void {
        const tree = ctx.tree;
        const node_ptr_opt = tree.nodes.getNodePtr(node_id);
        if (node_ptr_opt) |node_ptr| {
            switch (node_ptr.*.data) {
                .group => |*node| {
                    if (!isNodeVisible(ctx, node_ptr, node_id)) return;

                    // [compose begin]
                    for (0..node.compose.len) |i| {
                        if (tree.composes.getComposePtr(node.compose[i])) |comp| {
                            switch (comp.*) {
                                .clip => |*cd| {
                                    var compose_ctx = ctx.*;
                                    compose_ctx.canvas_stack = CanvasStack.init(arena.allocator());
                                    compose_ctx.setup();

                                    const tile_bg = ctx.tile_bg;
                                    ctx.tile_bg = col.transparent;
                                    try updateSubTree(tree, cd.root, true);
                                    try renderClipTree(&compose_ctx, cd.root);
                                    // TODO: render `cd.mask`
                                    ctx.tile_bg = tile_bg;

                                    const tile_canvas = compose_ctx.popCanvas();
                                    ctx.pushCanvas(tile_canvas);
                                    const new_canvas = ctx.makeCanvas();
                                    ctx.pushCanvas(new_canvas);

                                    compose_ctx.canvas_stack.deinit();
                                },
                                .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |*cd| {
                                    var compose_ctx = ctx.*;
                                    compose_ctx.canvas_stack = CanvasStack.init(arena.allocator());
                                    compose_ctx.setup();

                                    const tile_bg = ctx.tile_bg;
                                    ctx.tile_bg = col.transparent;
                                    try updateSubTree(tree, cd.root, true);
                                    try renderClipTree(&compose_ctx, cd.root);
                                    // TODO: render `cd.mask`
                                    ctx.tile_bg = tile_bg;

                                    const tile_canvas = compose_ctx.popCanvas();
                                    ctx.pushCanvas(tile_canvas);
                                    const new_canvas = ctx.makeCanvas();
                                    ctx.pushCanvas(new_canvas);

                                    compose_ctx.canvas_stack.deinit();
                                },
                                else => {},
                            }
                        }
                    }

                    // render children
                    var iter = tree.nodes.children(node_id);
                    while (iter.next()) |child_id| {
                        try renderClipTree(ctx, child_id);
                    }

                    // [compose end]
                    for (0..node.compose.len) |inv_i| {
                        const i = node.compose.len - inv_i - 1;
                        if (tree.composes.getComposePtr(node.compose[i])) |comp| {
                            switch (comp.*) {
                                .clip => {
                                    const src = ctx.popCanvas();
                                    const mask = ctx.popCanvas();
                                    if (!src.need_clear) {
                                        const target_canvas = ctx.topCanvas();
                                        if (target_canvas.need_clear) {
                                            target_canvas.begin(.{ .clear = ctx.tile_bg });
                                            target_canvas.need_clear = false;
                                        } else {
                                            target_canvas.begin(.keep);
                                        }
                                        // TODO: replace with clip rendering
                                        compose.maskImage(.{
                                            .src = src.img,
                                            .msk = mask.img,
                                            .dst_view = rect_one,
                                            .dst_rect = rect_one,
                                            .msk_type = .alpha,
                                            .filter = .pixel,
                                            .to_swapchain = false,
                                            .flip_y = true,
                                        });
                                        target_canvas.end();
                                    }
                                },
                                .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => {
                                    const src = ctx.popCanvas();
                                    const mask = ctx.popCanvas();
                                    if (!src.need_clear) {
                                        const target_canvas = ctx.topCanvas();
                                        if (target_canvas.need_clear) {
                                            target_canvas.begin(.{ .clear = ctx.tile_bg });
                                            target_canvas.need_clear = false;
                                        } else {
                                            target_canvas.begin(.keep);
                                        }
                                        compose.maskImage(.{
                                            .src = src.img,
                                            .msk = mask.img,
                                            .dst_view = rect_one,
                                            .dst_rect = rect_one,
                                            .msk_type = switch (comp.*) {
                                                .alpha_mask => .alpha,
                                                .inv_alpha_mask => .inv_alpha,
                                                .luma_mask => .luma,
                                                .inv_luma_mask => .inv_luma,
                                                else => .alpha,
                                            },
                                            .filter = .pixel,
                                            .to_swapchain = false,
                                            .flip_y = true,
                                        });
                                        target_canvas.end();
                                    }
                                },
                                else => {},
                            }
                        }
                    }
                },
                .path => |*node| {
                    if (!isNodeVisible(ctx, node_ptr, node_id)) return;

                    const target_canvas = ctx.topCanvas();

                    if (tree.paths.getDataPtr(node.path_id)) |path_data| {
                        if (tree.nodes.getTransformPtr(node_id)) |transform| {
                            const path_mat = transform.world orelse math.mat2df_identity();
                            const mat = ctx.world_to_tile.mul(path_mat);

                            const base_path = try state.base_path_cache.getOrPut(node.path_id);
                            if (!base_path.found_existing) {
                                base_path.value_ptr.* = .{
                                    .path = path_data.*,
                                    .mesh = null,
                                };
                            }

                            if (base_path.value_ptr.fetchMesh()) |mesh| {
                                // winding buffer
                                try winding_buffer.drawRIMesh(&wb, &mesh, mat);
                                // cover
                                if (target_canvas.need_clear) {
                                    target_canvas.begin(.{ .clear = ctx.tile_bg });
                                    target_canvas.need_clear = false;
                                } else {
                                    target_canvas.begin(.keep);
                                }
                                winding_buffer.fillColor(.{
                                    .src = &wb,
                                    .mat = math.mat2df_identity(),
                                    .color = col.green,
                                    .to_swapchain = false,
                                });
                                target_canvas.end();
                            }
                        }
                    }
                },
                .text => {
                    if (!isNodeVisible(ctx, node_ptr, node_id)) return;

                    // TODO
                },
            }
        }
    }
}.composeTiles;

const composeXrayTiles = struct {
    pub fn composeTiles(tilemap: *TileMap, tileset: *TileSet, tree: *SceneTree) !void {
        var tile_walker = tile_render.walkTileMap(tilemap, tileset);
        while (tile_walker.next()) |tile| {
            const tile_bg = col.white;

            var tile_canvas = tile_render.makeCanvas();
            var ctx: Context = .{
                .tree = tree,
                .tile_world_bbox = tile.tile_world_bbox,
                .world_to_tile = tile.world_to_tile,
                .clear_tile = true,
                .target_canvas = tile_canvas,
                .tile_bg = tile_bg,
            };
            try renderTree(&ctx, tree.root);

            tile_canvas.need_clear = ctx.clear_tile;
            try tile.finish(tile_bg, tile_canvas, false);
            try tile_render.destroyCanvas(tile_canvas);
        }
        tile_walker.finish();
    }

    const Context = struct {
        tree: *SceneTree,
        tile_world_bbox: math.Rect2f,
        world_to_tile: math.Mat2Df,
        tile_bg: col.Color,
        clear_tile: bool,
        target_canvas: TileCanvas,
    };

    const outline_stroke: sd.StrokeData = .{
        .linewidth = 1.0,
        .linejoin = .bevel,
        .linecap = .butt,
    };
    const outline_color = col.Color.init(0.0, 0.0, 0.0, 0.25);

    fn isNodeVisible(ctx: *Context, node_id: u32) bool {
        if (ctx.tree.nodes.getBBoxPtr(node_id)) |bbox| {
            if (bbox.visual_aabb) |aabb| {
                // check if it is within target tilemap area
                if (aabb.expand(outline_stroke.linewidth * 2.0).overlaps(ctx.tile_world_bbox)) {
                    return true;
                }
            }
        }
        return false;
    }

    fn renderTree(ctx: *Context, node_id: u32) !void {
        const tree = ctx.tree;
        const node_ptr_opt = tree.nodes.getNodePtr(node_id);
        if (node_ptr_opt) |node_ptr| {
            switch (node_ptr.data) {
                .group => {
                    if (!isNodeVisible(ctx, node_id)) return;

                    // render children nodes
                    var iter = tree.nodes.children(node_id);
                    while (iter.next()) |child_id| {
                        try renderTree(ctx, child_id);
                    }
                },
                .path => |*node| {
                    if (!isNodeVisible(ctx, node_id)) return;

                    if (tree.paths.getDataPtr(node.path_id)) |path_data| {
                        // TODO: rect/oval fast path rendering
                        var path: Path = undefined;
                        switch (path_data.*) {
                            .rect => |rect| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                                path = try pb.toOwnedPath();
                            },
                            .oval => |oval| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                                path = try pb.toOwnedPath();
                            },
                            .path => |p| path = p,
                        }

                        if (tree.nodes.getTransformPtr(node_id)) |transform| {
                            const path_mat = transform.world orelse math.mat2df_identity();
                            const mat = ctx.world_to_tile.mul(path_mat);

                            var fill_color = col.black;
                            if (node.fills[0]) |fill| {
                                if (fill.paint_tag == .color) {
                                    if (tree.colors.getRGBA32(fill.paint_id)) |color| {
                                        fill_color = color;
                                    }
                                }
                            }

                            // 1. fill
                            // winding buffer
                            try winding_buffer.drawPath(&wb, path, mat);
                            // cover
                            if (ctx.clear_tile) {
                                ctx.target_canvas.begin(.{ .clear = ctx.tile_bg });
                                ctx.clear_tile = false;
                            } else {
                                ctx.target_canvas.begin(.keep);
                            }
                            winding_buffer.fillColor(.{
                                .src = &wb,
                                .mat = math.mat2df_identity(),
                                .color = col.Color.init(fill_color.r, fill_color.g, fill_color.b, 0.85),
                                .to_swapchain = false,
                            });
                            ctx.target_canvas.end();

                            // 2. outline
                            const outline_path = stroker.strokePath(dino.galloc, path, outline_stroke);
                            defer safeDestroyPath(dino.galloc, outline_path);

                            // winding buffer
                            try winding_buffer.drawPath(&wb, outline_path, mat);
                            // cover
                            if (ctx.clear_tile) {
                                ctx.target_canvas.begin(.{ .clear = ctx.tile_bg });
                                ctx.clear_tile = false;
                            } else {
                                ctx.target_canvas.begin(.keep);
                            }
                            winding_buffer.fillColor(.{
                                .src = &wb,
                                .mat = math.mat2df_identity(),
                                .color = outline_color,
                                .to_swapchain = false,
                            });
                            ctx.target_canvas.end();
                        }
                    }
                },
                .text => {
                    if (!isNodeVisible(ctx, node_id)) return;

                    // TODO
                },
            }
        }
    }
}.composeTiles;

const composeOutlineTiles = struct {
    pub fn composeTiles(tilemap: *TileMap, tileset: *TileSet, tree: *SceneTree) !void {
        var tile_walker = tile_render.walkTileMap(tilemap, tileset);
        while (tile_walker.next()) |tile| {
            const tile_bg = col.white;

            var tile_canvas = tile_render.makeCanvas();
            var ctx: Context = .{
                .tree = tree,
                .tile_world_bbox = tile.tile_world_bbox,
                .world_to_tile = tile.world_to_tile,
                .tile_bg = tile_bg,
                .clear_tile = true,
                .target_canvas = tile_canvas,
            };
            try renderTree(&ctx, tree.root);

            tile_canvas.need_clear = ctx.clear_tile;
            try tile.finish(tile_bg, tile_canvas, false);
            try tile_render.destroyCanvas(tile_canvas);
        }
        tile_walker.finish();
    }

    const Context = struct {
        tree: *SceneTree,
        tile_world_bbox: math.Rect2f,
        world_to_tile: math.Mat2Df,
        tile_bg: col.Color,
        clear_tile: bool,
        target_canvas: TileCanvas,
    };

    const outline_stroke: sd.StrokeData = .{
        .linewidth = 1.0,
        .linejoin = .bevel,
        .linecap = .butt,
    };
    const outline_color = col.Color.init(0.0, 0.0, 0.0, 0.25);

    fn isNodeVisible(ctx: *Context, node_id: u32) bool {
        if (ctx.tree.nodes.getBBoxPtr(node_id)) |bbox| {
            if (bbox.visual_aabb) |aabb| {
                // check if it is within target tilemap area
                if (aabb.expand(outline_stroke.linewidth * 2.0).overlaps(ctx.tile_world_bbox)) {
                    return true;
                }
            }
        }
        return false;
    }

    fn renderTree(ctx: *Context, node_id: u32) !void {
        const tree = ctx.tree;
        const node_ptr_opt = tree.nodes.getNodePtr(node_id);
        if (node_ptr_opt) |node_ptr| {
            switch (node_ptr.data) {
                .group => {
                    if (!isNodeVisible(ctx, node_id)) return;

                    // render children nodes
                    var iter = tree.nodes.children(node_id);
                    while (iter.next()) |child_id| {
                        try renderTree(ctx, child_id);
                    }
                },
                .path => |*node| {
                    if (!isNodeVisible(ctx, node_id)) return;

                    if (tree.paths.getDataPtr(node.path_id)) |path_data| {
                        // TODO: rect/oval fast path rendering
                        var path: Path = undefined;
                        switch (path_data.*) {
                            .rect => |rect| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                                path = try pb.toOwnedPath();
                            },
                            .oval => |oval| {
                                var pb = Path.Builder.init(arena.allocator());
                                try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                                path = try pb.toOwnedPath();
                            },
                            .path => |p| path = p,
                        }

                        if (tree.nodes.getTransformPtr(node_id)) |transform| {
                            const path_mat = transform.world orelse math.mat2df_identity();
                            const mat = ctx.world_to_tile.mul(path_mat);

                            const outline_path = stroker.strokePath(dino.galloc, path, outline_stroke);
                            defer safeDestroyPath(dino.galloc, outline_path);

                            // winding buffer
                            try winding_buffer.drawPath(&wb, outline_path, mat);
                            // cover
                            if (ctx.clear_tile) {
                                ctx.target_canvas.begin(.{ .clear = ctx.tile_bg });
                                ctx.clear_tile = false;
                            } else {
                                ctx.target_canvas.begin(.keep);
                            }
                            winding_buffer.fillColor(.{
                                .src = &wb,
                                .mat = math.mat2df_identity(),
                                .color = outline_color,
                                .to_swapchain = false,
                            });
                            ctx.target_canvas.end();
                        }
                    }
                },
                .text => |*node| {
                    if (!isNodeVisible(ctx, node_id)) return;
                    _ = node;
                },
            }
        }
    }
}.composeTiles;

fn safeDestroyPath(allocator: std.mem.Allocator, path: anytype) void {
    Path.typeCheck(path);
    defer allocator.free(path.commands);
    const v_ptr: [*]const u8 = @alignCast(@ptrCast(path.vertices.ptr));
    defer allocator.free(v_ptr[0 .. path.vertices.len * 4]);
}
fn destroyBasePathCache(key: u32) void {
    if (state.base_path_cache.getPtr(key)) |ptr| {
        ptr.deinit();
    }
}
fn destroyStrokePathCache(key: state.StrokePathKey) void {
    if (state.stroke_path_cache.fetchRemove(key)) |pair| {
        pair.value.deinit();
    }
}

const guiOutlinePanel = struct {
    pub fn guiOutlinePanel(root_id: u32) !void {
        try drawNode(&state.tree, &state.selected, root_id);
    }

    fn drawNode(tree: *SceneTree, selected: *u32, node_id: u32) !void {
        var buf: [256]u8 = undefined;
        if (tree.nodes.getNodePtr(node_id)) |node| {
            const id_key = try std.fmt.bufPrintZ(&buf, "##{s}@{d}", .{ @tagName(node.data), node_id });

            const is_opened = gui.treeNodeFlags(id_key, .{
                .leaf = (node.data != .group),
                .default_open = true,
            });
            gui.sameLine(.{});
            if (gui.selectable(id_key[2..], .{
                .selected = (selected.* == node_id),
            })) {
                selected.* = node_id;
            }

            if (is_opened) {
                var it = tree.nodes.children(node_id);
                while (it.next()) |child_id| {
                    try drawNode(tree, selected, child_id);
                }
                gui.treePop();
            }
        }
    }
}.guiOutlinePanel;

/// `transform` should be POINTER to a struct with `position/rotation/scale/skew/size`
fn guiTransform(node_id: u32, transform: anytype) void {
    gui.separatorText("Transform");
    var changed = false;

    if (gui.dragFloat2("position", .{ .v = &transform.position.data, .min = -4096.0, .max = 4096.0, .speed = 1.0, .cfmt = "%.0f" })) {
        changed = true;
    }

    var rotation_degrees: f32 = std.math.radiansToDegrees(transform.rotation);
    if (gui.dragFloat("rotation", .{ .v = &rotation_degrees, .min = -720.0, .max = 720.0, .speed = 1.0, .cfmt = "%.0f deg" })) {
        transform.rotation = std.math.degreesToRadians(rotation_degrees);
        changed = true;
    }

    if (gui.dragFloat2("scale", .{ .v = &transform.scale.data, .min = -8.0, .max = 8.0, .speed = 0.01, .cfmt = "%.2f" })) {
        changed = true;
    }

    if (changed) {
        api.setNodeTransform(
            node_id,
            transform.position.x(),
            transform.position.y(),
            transform.rotation,
            transform.scale.x(),
            transform.scale.y(),
            transform.skew.x(),
            transform.skew.y(),
        );
    }
}
fn guiLayerDeleteButton(key: []const u8) bool {
    gui.sameLine(.{});

    const btn_size: f32 = 36.0;
    gui.setCursorPosX(gui.getCursorPosX() + gui.getContentRegionAvail()[0] - (gui.getStyle().item_spacing[0] + btn_size));
    var buf: [128]u8 = undefined;
    const id = std.fmt.bufPrintZ(&buf, "-##{s}", .{key}) catch "-##unknown";
    return gui.button(id, .{ .w = btn_size });
}
fn guiGradientStop(key: []const u8, node_id: u32, gradient_id: u32, grad: *sd.GradientData) void {
    var buf: [256]u8 = undefined;

    var gradient_changed = false;
    for (0..grad.stop_len) |i| {
        var changed = false;

        const col_id = std.fmt.bufPrintZ(&buf, "##{s}-gstop-clr-{d}", .{ key, i }) catch "-##unknown";
        if (gui.colorEdit4(col_id, .{ .col = @ptrCast(&grad.stops[i].color), .flags = .{ .no_label = true, .display_hex = true, .alpha_bar = true } })) {
            changed = true;
        }

        const pos_id = std.fmt.bufPrintZ(&buf, "##{s}-gstop-pos-{d}", .{ key, i }) catch "-##unknown";
        if (gui.dragFloat(pos_id, .{ .v = &grad.stops[i].pos, .min = 0.0, .max = 1.0, .speed = 0.005, .cfmt = "%.3f" })) {
            changed = true;
        }

        if (changed) {
            api.setGradientStop(gradient_id, @intCast(i), grad.stops[i].pos, grad.stops[i].color);
            gradient_changed = true;
        }

        if (grad.stop_len > 2) {
            if (guiLayerDeleteButton(pos_id)) {
                for (i..grad.stop_len - 1) |j| {
                    api.setGradientStop(gradient_id, @intCast(j), grad.stops[j + 1].pos, grad.stops[j + 1].color);
                }
                api.setGradientStopLen(gradient_id, grad.stop_len - 1);
                gradient_changed = true;
            }
        }
    }

    const add_id = std.fmt.bufPrintZ(&buf, "Add {s} grad stop", .{key}) catch "-##unknown";
    if (gui.button(add_id, .{ .w = gui.getContentRegionAvail()[0] })) {
        const new_idx = grad.stop_len;
        api.setGradientStopLen(gradient_id, grad.stop_len + 1);
        api.setGradientStop(gradient_id, @intCast(new_idx), 1, col.Color.init(0, 0, 0, 1));
        gradient_changed = true;
    }

    // TODO: use a `gradient_update_list` for updates
    if (gradient_changed) {
        api.updateGradientPixels(gradient_id);
        api.markNodeChanged(node_id);
    }
}
fn guiNodePropertyPanel(tree: *SceneTree, selected: u32) !bool {
    if (gui.begin("Node", .{})) {
        if (selected > 0) {
            const node_id = selected;
            const node_ptr_opt = tree.nodes.getNodePtr(node_id);
            if (node_ptr_opt) |node_ptr| {
                switch (node_ptr.data) {
                    .group => |*group| {
                        gui.separatorText("Group");

                        gui.text("id: {}", .{node_id});

                        if (gui.checkbox("visible", .{ .v = &node_ptr.visible })) {
                            api.setNodeVisible(node_id, node_ptr.visible);
                        }
                        guiTransform(node_id, node_ptr);

                        gui.separatorText("Layer");

                        var opacity: f32 = group.opacity * 100.0;
                        if (gui.dragFloat("opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                            group.opacity = opacity / 100.0;
                            api.setNodeOpacity(node_id, group.opacity);
                        }

                        if (gui.comboFromEnum("blend", &group.blend)) {
                            api.setNodeBlend(node_id, group.blend);
                        }
                    },
                    .path => |*path| {
                        gui.separatorText("Path");
                        gui.text("path_id = {d}", .{path.path_id});

                        if (gui.checkbox("visible", .{ .v = &node_ptr.visible })) {
                            api.setNodeVisible(node_id, node_ptr.visible);
                        }
                        guiTransform(node_id, node_ptr);

                        gui.separatorText("Fill");
                        // paint add/remove/swap
                        if (path.fills[0].?.paint_tag == .none) {
                            if (gui.button("Add Fill", .{ .w = gui.getContentRegionAvail()[0] })) {
                                const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                api.setNodeFillPaint(node_id, .color, color_id);
                            }
                        } else {
                            const old_paint_tag = path.fills[0].?.paint_tag;
                            if (gui.comboFromEnum("Type##fill-", &path.fills[0].?.paint_tag)) {
                                const old_paint_id = path.fills[0].?.paint_id;
                                var change_confirmed = false;
                                // make a new one if necessary
                                switch (path.fills[0].?.paint_tag) {
                                    .color => {
                                        const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                        if (color_id > 0) {
                                            api.setNodeFillPaint(node_id, .color, color_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .gradient => {
                                        const mat = math.mat2df_identity();
                                        const gradient_id = api.makeGradient(.linear, mat);
                                        if (gradient_id > 0) {
                                            api.setGradientStopLen(gradient_id, 2);
                                            api.setGradientStop(gradient_id, 0, 0.0, col.Color.init(1, 1, 1, 0));
                                            api.setGradientStop(gradient_id, 1, 1.0, col.Color.init(0, 0, 0, 1));
                                            api.updateGradientPixels(gradient_id);
                                            api.setNodeFillPaint(node_id, .gradient, gradient_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .image => {},
                                    else => {},
                                }

                                // destroy old one when change confirmed
                                if (change_confirmed) {
                                    switch (old_paint_tag) {
                                        .color => {
                                            api.destroyColor(old_paint_id);
                                        },
                                        .gradient => {
                                            api.destroyGradient(old_paint_id);
                                        },
                                        .image => {
                                            api.destroyImage(old_paint_id);
                                        },
                                        else => {},
                                    }
                                }
                            }

                            // paint properties
                            switch (path.fills[0].?.paint_tag) {
                                .color => {
                                    if (tree.colors.getRGBA32(path.fills[0].?.paint_id)) |color| {
                                        var rgba = color;
                                        if (gui.colorEdit4("##fill-color", .{ .col = @ptrCast(&rgba), .flags = .{ .no_label = true, .display_hex = true, .alpha_bar = true } })) {
                                            api.setColor(path.fills[0].?.paint_id, rgba);
                                            api.markNodeChanged(node_id);
                                        }

                                        if (guiLayerDeleteButton("fill")) {
                                            api.destroyColor(path.fills[0].?.paint_id);
                                            api.removeNodeFillPaint(node_id);
                                        }
                                    }
                                },
                                .gradient => {
                                    const gradient_id = path.fills[0].?.paint_id;
                                    gui.text("gradient@{d}##fill-gradi", .{gradient_id});

                                    if (guiLayerDeleteButton("fill")) {
                                        api.destroyGradient(gradient_id);
                                        api.removeNodeFillPaint(node_id);
                                    }

                                    var opacity: f32 = path.fills[0].?.opacity * 100.0;
                                    if (gui.dragFloat("opacity##fill-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                        path.fills[0].?.opacity = opacity / 100.0;
                                        api.setNodeFillOpacity(node_id, path.fills[0].?.opacity);
                                    }

                                    if (tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
                                        if (gui.comboFromEnum("Type##fill-grad-tag", &gradient_data.tag)) {
                                            api.setNodeFillGradientTag(node_id, gradient_data.tag);
                                        }

                                        // TODO? gradient matrix
                                        // nodes_api.changeFillGradientMatrix(node_id, grad.mat.a(), grad.mat.b(), grad.mat.c(), grad.mat.d(), grad.mat.x(), grad.mat.y());

                                        guiGradientStop("fill", node_id, gradient_id, gradient_data);
                                    }
                                },
                                .image => {
                                    const image_id = path.fills[0].?.paint_id;
                                    gui.text("image@{d}##fill-image", .{image_id});

                                    if (guiLayerDeleteButton("fill")) {
                                        api.destroyImage(image_id);
                                        api.removeNodeFillPaint(node_id);
                                    }

                                    var opacity: f32 = path.fills[0].?.opacity * 100.0;
                                    if (gui.dragFloat("opacity##fill-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                        path.fills[0].?.opacity = opacity / 100.0;
                                        api.setNodeFillOpacity(node_id, path.fills[0].?.opacity);
                                    }

                                    if (tree.images.getDataPtr(image_id)) |image_data| {
                                        if (gui.comboFromEnum("Type##fill-image-mode", &image_data.mode)) {
                                            api.setImageFillMode(image_id, image_data.mode);
                                            api.markNodeChanged(node_id);
                                        }
                                    }
                                },
                                else => {},
                            }
                        }

                        gui.separatorText("Stroke");
                        if (path.strokes[0].?.paint_tag == .none) {
                            if (gui.button("Add Stroke", .{ .w = gui.getContentRegionAvail()[0] })) {
                                const color_id = api.makeColor(col.black);
                                const stroke_id = api.makeStroke(10.0, .butt, .bevel, 4.0, 0, 0);

                                api.setNodeStrokeData(node_id, stroke_id);
                                api.setNodeStrokePaint(node_id, .color, color_id);
                            }
                        } else {
                            const old_paint_tag = path.strokes[0].?.paint_tag;
                            if (gui.comboFromEnum("Type##stroke-", &path.strokes[0].?.paint_tag)) {
                                const old_paint_id = path.strokes[0].?.paint_id;
                                var change_confirmed = false;
                                // make a new one if necessary
                                switch (path.strokes[0].?.paint_tag) {
                                    .color => {
                                        const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                        if (color_id > 0) {
                                            api.setNodeStrokePaint(node_id, .color, color_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .gradient => {
                                        const mat = math.mat2df_identity();
                                        const gradient_id = api.makeGradient(.linear, mat);
                                        if (gradient_id > 0) {
                                            api.setGradientStopLen(gradient_id, 2);
                                            api.setGradientStop(gradient_id, 0, 0.0, col.Color.init(1, 1, 1, 0));
                                            api.setGradientStop(gradient_id, 1, 1.0, col.Color.init(0, 0, 0, 1));
                                            api.updateGradientPixels(gradient_id);
                                            api.setNodeStrokePaint(node_id, .gradient, gradient_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .image => {},
                                    else => {},
                                }

                                // destroy old one when change confirmed
                                if (change_confirmed) {
                                    switch (old_paint_tag) {
                                        .color => {
                                            api.destroyColor(old_paint_id);
                                        },
                                        .gradient => {
                                            api.destroyGradient(old_paint_id);
                                        },
                                        .image => {
                                            api.destroyImage(old_paint_id);
                                        },
                                        else => {},
                                    }
                                }
                            }

                            if (tree.strokes.getDataPtr(path.strokes[0].?.data_id)) |stroke_data| {
                                switch (path.strokes[0].?.paint_tag) {
                                    .color => {
                                        if (tree.colors.getRGBA32(path.strokes[0].?.paint_id)) |color| {
                                            var rgba = color;
                                            if (gui.colorEdit4("##stroke-color", .{ .col = @ptrCast(&rgba), .flags = .{ .no_label = true, .display_hex = true, .alpha_bar = true } })) {
                                                api.setColor(path.strokes[0].?.paint_id, rgba);
                                                api.markNodeChanged(node_id);
                                            }

                                            if (guiLayerDeleteButton("stroke")) {
                                                destroyStrokePathCache(.{
                                                    .stroke = path.strokes[0].?.data_id,
                                                    .path = path.path_id,
                                                });
                                                api.destroyStroke(path.strokes[0].?.data_id);
                                                api.destroyColor(path.strokes[0].?.paint_id);
                                                api.removeNodeStrokePaint(node_id);
                                            }
                                        }
                                    },
                                    .gradient => {
                                        const gradient_id = path.strokes[0].?.paint_id;
                                        gui.text("gradient@{d}##stroke-gradi", .{gradient_id});

                                        if (guiLayerDeleteButton("stroke")) {
                                            destroyStrokePathCache(.{
                                                .stroke = path.strokes[0].?.data_id,
                                                .path = path.path_id,
                                            });
                                            api.destroyStroke(path.strokes[0].?.data_id);
                                            api.destroyColor(path.strokes[0].?.paint_id);
                                            api.removeNodeStrokePaint(node_id);
                                        }

                                        var opacity: f32 = path.strokes[0].?.opacity * 100.0;
                                        if (gui.dragFloat("opacity##stroke-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                            path.strokes[0].?.opacity = opacity / 100.0;
                                            api.setNodeStrokeOpacity(node_id, path.strokes[0].?.opacity);
                                        }

                                        if (tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
                                            if (gui.comboFromEnum("Type##stroke-grad-tag", &gradient_data.tag)) {
                                                api.setNodeStrokeGradientTag(node_id, gradient_data.tag);
                                            }

                                            // TODO? gradient matrix
                                            // nodes_api.changeStrokeGradientMatrix(node_id, grad.mat.a(), grad.mat.b(), grad.mat.c(), grad.mat.d(), grad.mat.x(), grad.mat.y());

                                            guiGradientStop("stroke", node_id, gradient_id, gradient_data);
                                        }
                                    },
                                    .image => {
                                        const image_id = path.strokes[0].?.paint_id;
                                        gui.text("image@{d}##stroke-image", .{image_id});

                                        if (guiLayerDeleteButton("stroke")) {
                                            destroyStrokePathCache(.{
                                                .stroke = path.strokes[0].?.data_id,
                                                .path = path.path_id,
                                            });
                                            api.destroyStroke(path.strokes[0].?.data_id);
                                            api.destroyImage(path.strokes[0].?.paint_id);
                                            api.removeNodeStrokePaint(node_id);
                                        }

                                        var opacity: f32 = path.strokes[0].?.opacity * 100.0;
                                        if (gui.dragFloat("opacity##stroke-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                            path.strokes[0].?.opacity = opacity / 100.0;
                                            api.setNodeStrokeOpacity(node_id, path.strokes[0].?.opacity);
                                        }

                                        if (tree.images.getDataPtr(image_id)) |image_data| {
                                            if (gui.comboFromEnum("Type##stroke-image-mode", &image_data.mode)) {
                                                api.setImageFillMode(image_id, image_data.mode);
                                                api.markNodeChanged(node_id);
                                            }
                                        }
                                    },
                                    else => {},
                                }

                                if (gui.dragFloat("line width", .{ .v = &stroke_data.linewidth, .min = 0.1, .max = 512.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.comboFromEnum("line cap", &stroke_data.linecap) or
                                    gui.comboFromEnum("line join", &stroke_data.linejoin) or
                                    gui.dragFloat("miter limit", .{ .v = &stroke_data.miterlimit, .min = 1.0, .max = 32.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.dragFloat("dash", .{ .v = &stroke_data.dash_array[0], .min = 0.0, .max = 100.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.dragFloat("gap", .{ .v = &stroke_data.dash_array[1], .min = 0.0, .max = 100.0, .speed = 0.1, .cfmt = "%.1f" }))
                                {
                                    destroyStrokePathCache(.{
                                        .stroke = path.strokes[0].?.data_id,
                                        .path = path.path_id,
                                    });
                                    api.setStroke(path.strokes[0].?.data_id, stroke_data.linewidth, stroke_data.linecap, stroke_data.linejoin, stroke_data.miterlimit, stroke_data.dash_array[0], stroke_data.dash_array[1]);
                                    api.markNodeChanged(node_id);
                                }
                            }
                        }
                    },
                    .text => |*text| {
                        gui.separatorText("Text");
                        gui.text("doc_id = {d}", .{text.doc_id});
                        gui.text("style_id = {d}", .{text.style_id});

                        _ = gui.checkbox("visible", .{ .v = &node_ptr.visible });
                        guiTransform(node_id, node_ptr);

                        gui.separatorText("Fill");
                        // paint add/remove/swap
                        if (text.fill.paint_tag == .none) {
                            if (gui.button("Add Text Fill", .{ .w = gui.getContentRegionAvail()[0] })) {
                                const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                api.setNodeFillPaint(node_id, .color, color_id);
                            }
                        } else {
                            const old_paint_tag = text.fill.paint_tag;
                            if (gui.comboFromEnum("Type##text-fill-", &text.fill.paint_tag)) {
                                const old_paint_id = text.fill.paint_id;
                                var change_confirmed = false;
                                // make a new one if necessary
                                switch (text.fill.paint_tag) {
                                    .color => {
                                        const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                        if (color_id > 0) {
                                            api.setNodeFillPaint(node_id, .color, color_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .gradient => {},
                                    .image => {},
                                    else => {},
                                }

                                // destroy old one when change confirmed
                                if (change_confirmed) {
                                    switch (old_paint_tag) {
                                        .color => {
                                            api.destroyColor(old_paint_id);
                                        },
                                        .gradient => {},
                                        .image => {},
                                        else => {},
                                    }
                                }
                            }

                            // paint properties
                            switch (text.fill.paint_tag) {
                                .color => {
                                    if (tree.colors.getRGBA32(text.fill.paint_id)) |color| {
                                        var rgba = color;
                                        if (gui.colorEdit4("##text-fill-color", .{ .col = @ptrCast(&rgba), .flags = .{ .no_label = true, .display_hex = true, .alpha_bar = true } })) {
                                            api.setColor(text.fill.paint_id, rgba);
                                            api.markNodeChanged(node_id);
                                        }

                                        if (guiLayerDeleteButton("text-fill")) {
                                            api.destroyColor(text.fill.paint_id);
                                            api.removeNodeFillPaint(node_id);
                                        }
                                    }
                                },
                                .gradient => {},
                                .image => {},
                                else => {},
                            }
                        }

                        gui.separatorText("Stroke");
                        if (text.stroke.paint_tag == .none) {
                            if (gui.button("Add Text Stroke", .{ .w = gui.getContentRegionAvail()[0] })) {
                                const color_id = api.makeColor(col.black);
                                const stroke_id = api.makeStroke(10.0, .butt, .bevel, 4.0, 0, 0);

                                api.setNodeStrokeData(node_id, stroke_id);
                                api.setNodeStrokePaint(node_id, .color, color_id);
                            }
                        } else {
                            const old_paint_tag = text.stroke.paint_tag;
                            if (gui.comboFromEnum("Type##text-stroke-", &text.stroke.paint_tag)) {
                                const old_paint_id = text.stroke.paint_id;
                                var change_confirmed = false;
                                // make a new one if necessary
                                switch (text.stroke.paint_tag) {
                                    .color => {
                                        const color_id = api.makeColor(col.Color.initWithHex("E7E8E9"));
                                        if (color_id > 0) {
                                            api.setNodeStrokePaint(node_id, .color, color_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .gradient => {
                                        const mat = math.mat2df_identity();
                                        const gradient_id = api.makeGradient(.linear, mat);
                                        if (gradient_id > 0) {
                                            api.setGradientStopLen(gradient_id, 2);
                                            api.setGradientStop(gradient_id, 0, 0.0, col.Color.init(1, 1, 1, 0));
                                            api.setGradientStop(gradient_id, 1, 1.0, col.Color.init(0, 0, 0, 1));
                                            api.updateGradientPixels(gradient_id);
                                            api.setNodeStrokePaint(node_id, .gradient, gradient_id);
                                            change_confirmed = true;
                                        }
                                    },
                                    .image => {},
                                    else => {},
                                }

                                // destroy old one when change confirmed
                                if (change_confirmed) {
                                    switch (old_paint_tag) {
                                        .color => {
                                            api.destroyColor(old_paint_id);
                                        },
                                        .gradient => {
                                            api.destroyGradient(old_paint_id);
                                        },
                                        .image => {
                                            api.destroyImage(old_paint_id);
                                        },
                                        else => {},
                                    }
                                }
                            }

                            if (tree.strokes.getDataPtr(text.stroke.data_id)) |stroke_data| {
                                switch (text.stroke.paint_tag) {
                                    .color => {
                                        if (tree.colors.getRGBA32(text.stroke.paint_id)) |color| {
                                            var rgba = color;
                                            if (gui.colorEdit4("##text-stroke-color", .{ .col = @ptrCast(&rgba), .flags = .{ .no_label = true, .display_hex = true, .alpha_bar = true } })) {
                                                api.setColor(text.stroke.paint_id, rgba);
                                                api.markNodeChanged(node_id);
                                            }

                                            if (guiLayerDeleteButton("text-stroke")) {
                                                destroyStrokePathCache(.{
                                                    .stroke = text.stroke.data_id,
                                                    .path = node_id,
                                                });
                                                api.destroyStroke(text.stroke.data_id);
                                                api.destroyColor(text.stroke.paint_id);
                                                api.removeNodeStrokePaint(node_id);
                                            }
                                        }
                                    },
                                    .gradient => {
                                        const gradient_id = text.stroke.paint_id;
                                        gui.text("gradient@{d}##text-stroke-gradi", .{gradient_id});

                                        if (guiLayerDeleteButton("text-stroke")) {
                                            destroyStrokePathCache(.{
                                                .stroke = text.stroke.data_id,
                                                .path = node_id,
                                            });
                                            api.destroyStroke(text.stroke.data_id);
                                            api.destroyColor(text.stroke.paint_id);
                                            api.removeNodeStrokePaint(node_id);
                                        }

                                        var opacity: f32 = text.stroke.opacity * 100.0;
                                        if (gui.dragFloat("opacity##text-text-stroke-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                            text.stroke.opacity = opacity / 100.0;
                                            api.setNodeStrokeOpacity(node_id, text.stroke.opacity);
                                        }

                                        if (tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
                                            if (gui.comboFromEnum("Type##text-stroke-grad-tag", &gradient_data.tag)) {
                                                api.setNodeStrokeGradientTag(node_id, gradient_data.tag);
                                            }

                                            // TODO? gradient matrix
                                            // nodes_api.changeStrokeGradientMatrix(node_id, grad.mat.a(), grad.mat.b(), grad.mat.c(), grad.mat.d(), grad.mat.x(), grad.mat.y());

                                            guiGradientStop("text-stroke", node_id, gradient_id, gradient_data);
                                        }
                                    },
                                    .image => {
                                        const image_id = text.stroke.paint_id;
                                        gui.text("image@{d}##text-stroke-image", .{image_id});

                                        if (guiLayerDeleteButton("text-stroke")) {
                                            destroyStrokePathCache(.{
                                                .stroke = text.stroke.data_id,
                                                .path = node_id,
                                            });
                                            api.destroyStroke(text.stroke.data_id);
                                            api.destroyImage(text.stroke.paint_id);
                                            api.removeNodeStrokePaint(node_id);
                                        }

                                        var opacity: f32 = text.stroke.opacity * 100.0;
                                        if (gui.dragFloat("opacity##text-stroke-opacity", .{ .v = &opacity, .min = 0.0, .max = 100.0, .speed = 1.0, .cfmt = "%.0f%%" })) {
                                            text.stroke.opacity = opacity / 100.0;
                                            api.setNodeStrokeOpacity(node_id, text.stroke.opacity);
                                        }

                                        if (tree.images.getDataPtr(image_id)) |image_data| {
                                            if (gui.comboFromEnum("Type##text-stroke-image-mode", &image_data.mode)) {
                                                api.setImageFillMode(image_id, image_data.mode);
                                                api.markNodeChanged(node_id);
                                            }
                                        }
                                    },
                                    else => {},
                                }

                                if (gui.dragFloat("line width", .{ .v = &stroke_data.linewidth, .min = 0.1, .max = 512.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.comboFromEnum("line cap", &stroke_data.linecap) or
                                    gui.comboFromEnum("line join", &stroke_data.linejoin) or
                                    gui.dragFloat("miter limit", .{ .v = &stroke_data.miterlimit, .min = 1.0, .max = 32.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.dragFloat("dash", .{ .v = &stroke_data.dash_array[0], .min = 0.0, .max = 100.0, .speed = 0.1, .cfmt = "%.1f" }) or
                                    gui.dragFloat("gap", .{ .v = &stroke_data.dash_array[1], .min = 0.0, .max = 100.0, .speed = 0.1, .cfmt = "%.1f" }))
                                {
                                    destroyStrokePathCache(.{
                                        .stroke = text.stroke.data_id,
                                        .path = node_id,
                                    });
                                    api.setStroke(text.stroke.data_id, stroke_data.linewidth, stroke_data.linecap, stroke_data.linejoin, stroke_data.miterlimit, stroke_data.dash_array[0], stroke_data.dash_array[1]);
                                    api.markNodeChanged(node_id);
                                }
                            }
                        }

                        // if (tree.text_docs.getDataPtr(text.doc_id)) |doc_ptr| {
                        //     var text_buf = try TextBuffer.initWithText(arena.allocator(), doc_ptr.content);
                        //     if (gui.inputText("Content", .{
                        //         .buf = @ptrCast(text_buf.str()),
                        //     })) {
                        //         std.log.debug("text changed: '{s}'", .{
                        //             text_buf.str(),
                        //         });
                        //     }
                        // }

                        // if (tree.text_styles.getDataPtr(text.style_id)) |style_ptr| {
                        //     const FontGroup = struct {
                        //         const Subset = struct {
                        //             weight: FontDB.Weight,
                        //             italic: bool = false,
                        //         };

                        //         family: []const u8,
                        //         subsets: []const Subset,
                        //     };

                        //     const font_list = [_]FontGroup{
                        //         .{
                        //             .family = "Arial",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //                 .{ .weight = .bold },
                        //                 .{ .weight = .regular, .italic = true },
                        //                 .{ .weight = .bold, .italic = true },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Comic Sans MS",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //                 .{ .weight = .bold },
                        //                 .{ .weight = .bold, .italic = true },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Courier New",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //                 .{ .weight = .regular, .italic = true },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Impact",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //             },
                        //         },
                        //         .{
                        //             .family = "JetBrains Mono",
                        //             .subsets = &.{
                        //                 .{ .weight = .extra_bold },
                        //                 .{ .weight = .medium },
                        //                 .{ .weight = .medium, .italic = true },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Noto Sans SC",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Open Sans",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //             },
                        //         },
                        //         .{
                        //             .family = "Times New Roman",
                        //             .subsets = &.{
                        //                 .{ .weight = .regular },
                        //                 .{ .weight = .bold },
                        //                 .{ .weight = .regular, .italic = true },
                        //                 .{ .weight = .bold, .italic = true },
                        //             },
                        //         },
                        //     };

                        //     var font_changed = false;

                        //     // family options
                        //     var family_idx: i32 = 0;
                        //     {
                        //         var array = std.ArrayList(u8).init(arena.allocator());
                        //         defer array.deinit();
                        //         var writer = array.writer();
                        //         for (font_list) |f| {
                        //             _ = try writer.write(f.family);
                        //             try writer.writeByte(0);
                        //         }
                        //         try writer.writeByte(0);

                        //         for (font_list, 0..) |f, i| {
                        //             if (std.mem.eql(u8, style_ptr.font_family, f.family)) {
                        //                 family_idx = @intCast(i);
                        //                 break;
                        //             }
                        //         }

                        //         if (gui.combo("Font Family", .{
                        //             .current_item = &family_idx,
                        //             .items_separated_by_zeros = @ptrCast(array.items),
                        //         })) {
                        //             style_ptr.font_family = font_list[@intCast(family_idx)].family;
                        //             font_changed = true;
                        //         }
                        //     }

                        //     // style options
                        //     {
                        //         var subset_idx: i32 = 0;

                        //         const font = font_list[@intCast(family_idx)];
                        //         for (font.subsets, 0..) |subset, i| {
                        //             if (@intFromEnum(subset.weight) == @intFromEnum(style_ptr.font_weight)) {
                        //                 if (subset.italic == (style_ptr.font_style == .italic)) {
                        //                     subset_idx = @intCast(i);
                        //                 }
                        //             }
                        //         }

                        //         var array = std.ArrayList(u8).init(arena.allocator());
                        //         defer array.deinit();
                        //         var writer = array.writer();

                        //         // Weight only subsets
                        //         for (font.subsets) |subset| {
                        //             if (!subset.italic) {
                        //                 _ = try writer.write(subset.weight.name());
                        //                 try writer.writeByte(0);
                        //             }
                        //         }
                        //         // Italic subsets
                        //         for (font.subsets) |subset| {
                        //             if (subset.italic) {
                        //                 _ = try writer.write(subset.weight.name());
                        //                 _ = try writer.write(" Italic");
                        //                 try writer.writeByte(0);
                        //             }
                        //         }
                        //         try writer.writeByte(0);

                        //         if (gui.combo("Font Weight", .{
                        //             .current_item = &subset_idx,
                        //             .items_separated_by_zeros = @ptrCast(array.items),
                        //         })) {
                        //             const subset = font.subsets[@intCast(subset_idx)];
                        //             const font_weight: dino.TextDoc.Style.FontWeight = switch (subset.weight) {
                        //                 .thin => .thin,
                        //                 .extra_light => .extra_light,
                        //                 .light => .light,
                        //                 .regular => .regular,
                        //                 .medium => .medium,
                        //                 .semi_bold => .semi_bold,
                        //                 .bold => .bold,
                        //                 .extra_bold => .extra_bold,
                        //                 .black => .black,
                        //                 .variable => .regular, // FIXME: variable support
                        //             };
                        //             style_ptr.font_weight = font_weight;
                        //             style_ptr.font_style = if (subset.italic) .italic else .normal;
                        //             font_changed = true;
                        //         }
                        //     }

                        //     // fetch and mark nodes as dirty
                        //     if (font_changed) {
                        //         const font_weight: FontDB.Weight = switch (style_ptr.font_weight) {
                        //             .thin => .thin,
                        //             .extra_light => .extra_light,
                        //             .light => .light,
                        //             .regular => .regular,
                        //             .medium => .medium,
                        //             .semi_bold => .semi_bold,
                        //             .bold => .bold,
                        //             .extra_bold => .extra_bold,
                        //             .black => .black,
                        //             .variable => .regular, // FIXME: variable support
                        //         };
                        //         try fetchFont(.{
                        //             .family = style_ptr.font_family,
                        //             .style = if (style_ptr.font_style == .italic) .italic else .normal,
                        //             .weight = font_weight,
                        //         }, fontPath(style_ptr.font_family, style_ptr.font_style == .italic, font_weight));
                        //         api.markNodeChanged(node_id);
                        //     }
                        // }
                    },
                }
            } else {
                gui.text("Select any Nodes", .{});
                // TODO: color picker for background color
            }

            // show hierarchy
            gui.separatorText("Hierarchy");
            if (tree.nodes.getHirarchy(node_id)) |hier| {
                gui.text("parent id: {}", .{hier.parent});
                gui.text("prev id: {}", .{hier.prev});
                gui.text("next id: {}", .{hier.next});
                gui.text("children id: {}", .{hier.child});
            }
        }
        return true;
    }
    return false;
}

const api = struct {
    export fn setCaptureBackgroundColor(r: f32, g: f32, b: f32, a: f32) void {
        state.capture.bg_color = col.Color.init(r, g, b, a);
    }
    export fn capture(node_id: u32, clip_x: f32, clip_y: f32, clip_w: f32, clip_h: f32, output_w: u32, output_h: u32) void {
        state.capture.root = node_id;
        state.capture.clip_bbox = math.rect2f(clip_x, clip_y, clip_w, clip_h);
        state.capture.keep_ratio = true;

        if (state.capture.clip_bbox) |aabb| {
            state.capture.origin_size[0] = @intFromFloat(aabb.width());
            state.capture.origin_size[1] = @intFromFloat(aabb.height());
        }

        state.capture.size[0] = @intCast(output_w);
        state.capture.size[1] = @intCast(output_h);
        state.capture.kind = .fixed_size;
    }
    fn makeNode(t: sd.NodeTag) u32 {
        const tree = &state.tree;
        const content = "";
        const node_id = switch (t) {
            .group => if (tree.nodes.make(.{ .data = .{ .group = .{} } })) |id| id else |err| {
                dino.log.err(.api, "failed to make Group node: {}", .{err});
                return 0;
            },
            .path => if (tree.nodes.make(.{ .data = .{ .path = .{} } })) |id| id else |err| {
                dino.log.err(.api, "failed to make Path node: {}", .{err});
                return 0;
            },
            .text => if (tree.nodes.make(.{ .data = .{ .text = .{
                .doc_id = state.tree.text_docs.makeParagraphs() catch |err| {
                    dino.log.err(.api, "makeStroke error: {}", .{err});
                    return 0;
                },
            } } })) |id| id else |err| {
                dino.log.err(.api, "failed to make Text node: {}", .{err});
                return 0;
            },
        };
        if (node_id > 0) {
            if (tree.nodes.getNodePtr(node_id)) |node| {
                switch (t) {
                    .text => {
                        if (tree.text_docs.getDataPtr(node.data.text.doc_id)) |doc| {
                            doc.importFromString(content) catch unreachable;
                        }
                    },
                    else => {},
                }
                node.id = node_id;
            }
        }
        return node_id;
    }
    export fn makeNodeEx(t: u8) u32 {
        return switch (t) {
            0 => makeNode(.group),
            1 => makeNode(.path),
            2 => makeNode(.text),
            else => 0,
        };
    }
    export fn destroyNode(node_id: u32, include_children: bool) void {
        // mark parent as changed
        if (state.tree.nodes.getHirarchy(node_id)) |hire| {
            if (hire.parent > 0) api.markNodeChanged(hire.parent);
        }

        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            switch (node.data) {
                .text => |text| {
                    if (state.page.edit_mode == .editing and state.page.edit_node_id == node_id) {
                        // TODO: It needs to notify UI to blur the focus, so send a fake event to trigger the focus change and exit the editing mode, instead of using internal functions
                        state.page.handleEvent(&app.Event{
                            .type = .KEY_DOWN,
                            .key_code = .ESCAPE,
                            .mouse_x = 0,
                            .mouse_y = 0,
                            .mouse_button = .LEFT,
                        }) catch |err| {
                            std.log.warn("failed to handle escape event: {}", .{err});
                        };
                    }
                    if (state.tree.text_docs.getDataPtr(text.doc_id)) |doc| {
                        _ = doc;
                        state.tree.text_docs.destroy(text.doc_id) catch |err| {
                            dino.log.err(.api, "failed to destroy text doc: {}", .{err});
                        };
                    }
                },
                else => {},
            }
        }

        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            _ = node.data;
            state.tree.nodes.destroy(node_id, include_children) catch |err| {
                dino.log.err(.api, "failed to destroy node: {}", .{err});
            };
        }
    }
    export fn addNodeChild(parent: u32, child: u32) void {
        // if has old parent, mark it as changed
        if (state.tree.nodes.getHirarchy(child)) |hire| {
            if (hire.parent > 0) api.markNodeChanged(hire.parent);
        }
        // mark new parent as changed
        api.markNodeChanged(parent);

        state.tree.nodes.addChild(parent, child) catch |err| {
            dino.log.err(.api, "failed to add child node: {}", .{err});
        };
    }
    export fn getNodeChildCount(parent: u32) u32 {
        return state.tree.nodes.getNodeChildCount(parent);
    }
    export fn insertNodeChild(parent: u32, child: u32, index: u32) void {
        // if has old parent, mark it as changed
        if (state.tree.nodes.getHirarchy(child)) |hire| {
            if (hire.parent > 0) api.markNodeChanged(hire.parent);
        }
        // mark new parent as changed
        api.markNodeChanged(parent);

        state.tree.nodes.insertChild(parent, child, index) catch |err| {
            dino.log.err(.api, "failed to insert child node: {}", .{err});
        };
    }
    export fn reorderNode(node_id: u32, index: u32) void {
        // mark parent as changed
        if (state.tree.nodes.getHirarchy(node_id)) |hire| {
            if (hire.parent > 0) api.markNodeChanged(hire.parent);
        }

        state.tree.nodes.reorderNode(node_id, index) catch |err| {
            dino.log.err(.api, "failed to reorder node: {}", .{err});
        };
    }
    export fn removeNode(id: u32) void {
        // mark parent as changed
        if (state.tree.nodes.getHirarchy(id)) |hire| {
            if (hire.parent > 0) api.markNodeChanged(hire.parent);
        }

        state.tree.nodes.removeNode(id) catch |err| {
            dino.log.err(.api, "failed to remove node: {}", .{err});
        };
    }
    export fn addNodeToRoot(node_id: u32) void {
        // mark root as changed
        api.markNodeChanged(state.tree.root);

        state.tree.nodes.addChild(state.tree.root, node_id) catch |err| {
            dino.log.err(.api, "failed to add node to root: {}", .{err});
        };
    }

    export fn setNodeVisible(node_id: u32, visible: bool) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            node.visible = visible;
            api.markNodeChanged(node_id);
        }
    }

    export fn setNodeTransform(node_id: u32, x: f32, y: f32, r: f32, sx: f32, sy: f32, sk_x: f32, sk_y: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node_data| {
            node_data.position = math.vec2f(x, y);
            node_data.rotation = r;
            node_data.scale = math.vec2f(sx, sy);
            node_data.skew = math.vec2f(sk_x, sk_y);
            if (state.tree.nodes.getTransformPtr(node_id)) |transform| {
                transform.local = null;
                transform.world = null;
            }
            api.markNodeChanged(node_id);
        }
    }

    export fn setNodeOpacity(node_id: u32, opacity: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .group) {
                node.data.group.opacity = opacity;
                api.markNodeChanged(node_id);
            }
        }
    }

    fn setNodeBlend(node_id: u32, blend: sd.BlendMode) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .group) {
                node.data.group.blend = blend;
                api.markNodeChanged(node_id);
            }
        }
    }
    export fn setNodeBlendEx(node_id: u32, blend: u8) void {
        if (std.meta.intToEnum(sd.BlendMode, blend)) |b| {
            setNodeBlend(node_id, b);
        } else |_| {}
    }

    export fn setNodeCompose(node_id: u32, idx: u32, comp_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .group) {
                if (comp_id == 0) {
                    // same as remove
                    node.data.group.compose[idx] = 0;
                    api.markNodeChanged(node_id);
                }
                if (state.tree.composes.getComposePtr(comp_id)) |_| {
                    node.data.group.compose[idx] = comp_id;
                    api.markNodeChanged(node_id);
                }
            }
        }
    }
    export fn removeNodeCompose(node_id: u32, idx: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .group) {
                node.data.group.compose[idx] = 0;
                api.markNodeChanged(node_id);
            }
        }
    }
    export fn destroyNodeCompose(node_id: u32, idx: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .group) {
                if (state.tree.composes.getComposePtr(node.data.group.compose[idx])) |comp| {
                    switch (comp.*) {
                        .clip, .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |*cc| {
                            api.destroyNode(cc.root, true);
                        },
                        else => {},
                    }
                    api.destroyCompose(node.data.group.compose[idx]);
                    node.data.group.compose[idx] = 0;
                    api.markNodeChanged(node_id);
                }
            }
            if (node.data == .text) {
                api.markNodeChanged(node_id);
            }
        }
    }

    export fn setNodePath(node_id: u32, path_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                node.data.path.path_id = path_id;
            }
        }
    }

    export fn enterTextEditingMode(node_id: u32) void {
        const doc_id = api.getTextDocID(node_id);
        if (state.page.edit_mode == .editing and state.page.edit_node_id == node_id) return;
        if (state.tree.text_docs.getDataPtr(doc_id)) |doc| {
            state.page.doc = doc;
        }
        state.page.edit_mode = .editing;
        state.page.edit_node_id = node_id;
        // set doc.cursor_char_index to end of the text of the end block
        var end_block: usize = 0;
        var end_char: usize = 0;
        state.page.doc.cursor_char_index = 0;
        if (state.page.doc.blocks.items.len > 0) {
            end_block = state.page.doc.blocks.items.len - 1;
            state.page.doc.cursor_char_index = state.page.doc.blocks.items[state.page.doc.blocks.items.len - 1].data.string.length();
            end_char = state.page.doc.blocks.items[state.page.doc.blocks.items.len - 1].data.string.length();
        }
        // set doc.selection to the end of the text
        state.page.doc.selectRange(0, 0, end_block, end_char);
        state.page.updateTextRangeBySelection();
    }

    export fn changeTextEditingNode(node_id: u32, tabbing: bool) void {
        if (state.page.edit_node_id != 0) {
            state.page.doc.clearSelection();
        }

        if (tabbing) {
            enterTextEditingMode(node_id);
        } else {
            const doc_id = api.getTextDocID(node_id);
            if (state.tree.text_docs.getDataPtr(doc_id)) |doc| {
                state.page.doc = doc;
            }
            state.page.edit_mode = .editing;
            state.page.edit_node_id = node_id;
            ext.onUpdatePageOffset(node_id);
            const mouse_pos = math.vec2f(
                (state.view.mouse_world.x() - state.page.offset.x()) / state.page.view_zoom,
                (state.view.mouse_world.y() - state.page.offset.y()) / state.page.view_zoom,
            );
            state.page.searchHoverBlock(mouse_pos);
            if (state.page.hovered_block_info) |info| {
                // Single click behavior
                state.page.doc.active_block_index = info.block_index;
                state.page.doc.cursor_char_index = info.char_index;
                state.page.forceCursorVisible();
            }
            state.page.updateTextRangeByCursorPos();
        }
    }

    export fn exitTextEditingMode() void {
        if (state.page.edit_node_id != 0) {
            state.page.doc.clearSelection();
            state.page.doc = undefined;
        }
        state.page.edit_mode = .normal;
        state.page.edit_node_id = 0;
    }

    export fn setForceDefaultCursor(force: bool) void {
        state.page.force_default_cursor = force;
    }

    export fn setTextSelection(doc_id: u32, start_block: u32, start_char: u32, end_block: u32, end_char: u32, cursor: u32, active_block_index: u32) void {
        // if page.doc is the same as the doc_id, update the selection data
        if (state.tree.text_docs.getDataPtr(doc_id)) |doc| {
            if (state.page.doc == doc) {
                if (start_block == end_block and start_char == end_char) {
                    state.page.doc.clearSelection();
                } else {
                    state.page.doc.selectRange(start_block, start_char, end_block, end_char);
                    state.page.doc.cursor_char_index = cursor;
                    state.page.doc.active_block_index = active_block_index;
                }
            }
        }
    }
    export fn setNodeTextStyle(node_id: u32, style_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .text) {
                node.data.text.style_id = style_id;
            }
        }
    }

    fn setNodeFillPaint(node_id: u32, tag: sd.PaintTag, paint_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            switch (node.data) {
                .path => |*n| {
                    // TODO: validate node has no fill right now
                    // TODO: validate color with the id does exist
                    // TODO: validate tag
                    if (n.fills[0]) |*fill| {
                        fill.paint_tag = tag;
                        fill.paint_id = paint_id;
                    } else {
                        n.fills[0] = .{
                            .paint_tag = tag,
                            .paint_id = paint_id,
                            .opacity = 1.0,
                        };
                    }
                    api.markNodeChanged(node_id);
                },
                .text => |*n| {
                    n.fill.paint_tag = tag;
                    n.fill.paint_id = paint_id;
                    api.markNodeChanged(node_id);
                },
                else => {},
            }
        }
    }
    export fn setNodeFillPaintEx(node_id: u32, tag: u8, paint_id: u32) void {
        if (std.meta.intToEnum(sd.PaintTag, tag)) |t| {
            setNodeFillPaint(node_id, t, paint_id);
        } else |_| {}
    }

    export fn setNodeFillOpacity(node_id: u32, opacity: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.fills[0]) |*fill| {
                    fill.opacity = opacity;
                    api.markNodeChanged(node_id);
                }
            }
            if (node.data == .text) {
                node.data.text.fill.opacity = opacity;
                api.markNodeChanged(node_id);
            }
        }
    }
    fn removeNodeFillPaint(node_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.fills[0]) |*fill| {
                    fill.paint_tag = .none;
                    fill.paint_id = 0;
                }
                api.markNodeChanged(node_id);
            }
            if (node.data == .text) {
                node.data.text.fill.paint_tag = .none;
                node.data.text.fill.paint_id = 0;
                api.markNodeChanged(node_id);
            }
        }
    }
    fn setNodeFillGradientTag(node_id: u32, tag: sd.GradientTag) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.fills[0]) |fill| {
                    if (state.tree.gradients.getDataPtr(fill.paint_id)) |gradient_data| {
                        gradient_data.tag = tag;
                        api.markNodeChanged(node_id);
                    }
                }
            }
            if (node.data == .text) {
                if (state.tree.gradients.getDataPtr(node.data.text.fill.paint_id)) |gradient_data| {
                    gradient_data.tag = tag;
                    api.markNodeChanged(node_id);
                }
            }
        }
    }
    fn setNodeFillGradientMatrix(node_id: u32, a: f32, b: f32, c: f32, d: f32, e: f32, f: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.fills[0]) |fill| {
                    if (state.tree.gradients.getGradientPtr(fill.paint_id)) |grad| {
                        grad.mat = math.mat2df(a, b, c, d, e, f);
                        api.markNodeChanged(node_id);
                    }
                }
            }
            if (node.data == .text) {
                if (state.tree.gradients.getGradientPtr(node.data.text.fill.paint_id)) |grad| {
                    grad.mat = math.mat2df(a, b, c, d, e, f);
                    api.markNodeChanged(node_id);
                }
            }
        }
    }

    export fn setNodeStrokeData(node_id: u32, stroke_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            switch (node.data) {
                .path => |*n| {
                    // TODO: validate node has no stroke right now
                    if (n.strokes[0]) |*stroke| {
                        stroke.data_id = stroke_id;
                    } else {
                        n.strokes[0] = .{
                            .data_id = stroke_id,
                        };
                    }
                    api.markNodeChanged(node_id);
                },
                .text => |*n| {
                    n.stroke.data_id = stroke_id;
                    api.markNodeChanged(node_id);
                },
                else => {},
            }
        } else {
            dino.log.err(.api, "setNodeStrokePaint: node not found: {}", .{node_id});
        }
    }
    fn setNodeStrokePaint(node_id: u32, tag: sd.PaintTag, paint_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            switch (node.data) {
                .path => |*n| {
                    // TODO: validate node has stroke right now
                    // TODO: validate color with the id does exist
                    if (n.strokes[0]) |*stroke| {
                        stroke.paint_tag = tag;
                        stroke.paint_id = paint_id;
                    } else {
                        n.strokes[0] = .{
                            .paint_tag = tag,
                            .paint_id = paint_id,
                        };
                    }
                    api.markNodeChanged(node_id);
                },
                .text => |*n| {
                    // TODO: validate node has stroke right now
                    // TODO: validate color with the id does exist
                    n.stroke.paint_tag = tag;
                    n.stroke.paint_id = paint_id;
                    api.markNodeChanged(node_id);
                },
                else => {},
            }
        }
    }
    export fn setNodeStrokePaintEx(node_id: u32, tag: u8, paint_id: u32) void {
        if (std.meta.intToEnum(sd.PaintTag, tag)) |t| {
            setNodeStrokePaint(node_id, t, paint_id);
        } else |_| {}
    }
    export fn setNodeStrokeOpacity(node_id: u32, opacity: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.strokes[0]) |*stroke| {
                    stroke.opacity = opacity;
                    api.markNodeChanged(node_id);
                }
            }
            if (node.data == .text) {
                node.data.text.stroke.opacity = opacity;
                api.markNodeChanged(node_id);
            }
        }
    }
    fn removeNodeStrokeData(node_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.strokes[0]) |*path_stroke| {
                    path_stroke.data_id = 0;
                    api.markNodeChanged(node_id);
                }
            }
            if (node.data == .text) {
                node.data.text.stroke.data_id = 0;
                api.markNodeChanged(node_id);
            }
        }
    }
    fn removeNodeStrokePaint(node_id: u32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.strokes[0]) |*path_stroke| {
                    path_stroke.paint_tag = .none;
                    path_stroke.paint_id = 0;
                    api.markNodeChanged(node_id);
                }
            }
            if (node.data == .text) {
                node.data.text.stroke.paint_tag = .none;
                node.data.text.stroke.paint_id = 0;
                api.markNodeChanged(node_id);
            }
        }
    }
    fn setNodeStrokeGradientTag(node_id: u32, tag: sd.GradientTag) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.strokes[0]) |*path_stroke| {
                    if (state.tree.gradients.getDataPtr(path_stroke.paint_id)) |gradient_data| {
                        gradient_data.tag = tag;
                        api.markNodeChanged(node_id);
                    }
                }
            }
            if (node.data == .text) {
                if (state.tree.gradients.getDataPtr(node.data.text.stroke.paint_id)) |gradient_data| {
                    gradient_data.tag = tag;
                    api.markNodeChanged(node_id);
                }
            }
        }
    }
    fn setNodeStrokeGradientMatrix(node_id: u32, a: f32, b: f32, c: f32, d: f32, e: f32, f: f32) void {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            if (node.data == .path) {
                if (node.data.path.strokes[0]) |*path_stroke| {
                    if (state.tree.gradients.getGradientPtr(path_stroke.paint_id)) |grad| {
                        grad.mat = math.mat2df(a, b, c, d, e, f);
                        api.markNodeChanged(node_id);
                    }
                }
            }
            if (node.data == .text) {
                if (state.tree.gradients.getGradientPtr(node.data.text.stroke.paint_id)) |grad| {
                    grad.mat = math.mat2df(a, b, c, d, e, f);
                    api.markNodeChanged(node_id);
                }
            }
        }
    }

    // resources

    export fn setAssetsPathEx(ptr: [*c]u8, len: u32) void {
        setPublicPathAndPreload(ptr[0..len]) catch |err| {
            dino.log.err(.asset, "failed to set assets path: {s}", .{@errorName(err)});
        };
    }

    inline fn makePath(path: anytype) u32 {
        const p = Path.cast(path);
        return makePathEx(@constCast(p.commands.ptr), @intCast(p.commands.len), @constCast(p.vertices.ptr), @intCast(p.vertices.len));
    }
    export fn makePathEx(cmd_ptr: [*c]u8, cmd_len: u32, vtx_ptr: [*c]f32, vtx_len: u32) u32 {
        return state.tree.paths.make(.{
            .commands = cmd_ptr[0..cmd_len],
            .vertices = vtx_ptr[0..vtx_len],
        }) catch |err| blk: {
            dino.log.err(.api, "makePath error: {}", .{err});
            break :blk 0;
        };
    }
    export fn makeEmptyPath() u32 {
        return state.tree.paths.make(.{}) catch |err| blk: {
            dino.log.err(.api, "makeEmptyPath error: {}", .{err});
            break :blk 0;
        };
    }
    inline fn setPath(path_id: u32, path: anytype) void {
        const p = Path.cast(path);
        setPathEx(path_id, p.commands.ptr, @intCast(p.commands.len), p.vertices.ptr, @intCast(p.vertices.len));
    }
    export fn setEmptyPath(path_id: u32) void {
        state.tree.paths.update(path_id, .{}) catch |err| {
            dino.log.err(.api, "setEmptyPath error: path_id={} {}", .{ path_id, err });
        };

        // destroy cache
        if (state.base_path_cache.getPtr(path_id)) |base_path| {
            base_path.* = .{
                .path = .{ .rect = math.rect2f(0.0, 0.0, 0.0, 0.0) },
                .mesh = null,
            };
        }
        var it = state.stroke_path_cache.keyIterator();
        while (it.next()) |key| {
            if (key.path == path_id) {
                _ = state.stroke_path_cache.remove(key.*);
            }
        }
    }
    export fn setPathEx(path_id: u32, cmd_ptr: [*c]u8, cmd_len: u32, vtx_ptr: [*c]f32, vtx_len: u32) void {
        state.tree.paths.update(path_id, .{
            .commands = cmd_ptr[0..cmd_len],
            .vertices = vtx_ptr[0..vtx_len],
        }) catch |err| {
            dino.log.err(.api, "setPath error: path_id={} {}", .{ path_id, err });
        };

        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            // destroy cache
            if (state.base_path_cache.getPtr(path_id)) |base_path| {
                base_path.* = .{
                    .path = path_data.*,
                    .mesh = null,
                };
            }
            var it = state.stroke_path_cache.keyIterator();
            while (it.next()) |key| {
                if (key.path == path_id) {
                    _ = state.stroke_path_cache.remove(key.*);
                }
            }
        }
    }
    export fn destroyPath(path_id: u32) void {
        state.tree.paths.destroy(path_id) catch |err| {
            dino.log.err(.api, "destroyPath(rid={}) error: {}", .{ path_id, err });
        };
    }

    inline fn getTextDocID(node_id: u32) u32 {
        if (state.tree.nodes.getNodePtr(node_id)) |node| {
            return node.data.text.doc_id;
        }
        return 0;
    }
    export fn getTextDocIDEx(node_id: u32) u32 {
        return getTextDocID(node_id);
    }
    export fn destroyTextDoc(doc_rid: u32) void {
        if (state.tree.text_docs.getDataPtr(doc_rid)) |doc| {
            _ = doc;
            state.tree.text_docs.destroy(doc_rid) catch |err| {
                dino.log.err(.api, "destroyTextDoc(rid={}) error: {}", .{ doc_rid, err });
            };
        }
    }

    export fn updatePageOffset(x: f32, y: f32) void {
        state.page.offset = math.vec2f(x, y);
        state.page.view_zoom = state.camera.zoom;
    }

    export fn destoryTextPath(doc_rid: u32) void {
        state.tree.text_docs.destroyPath(doc_rid);
    }

    inline fn setTextDoc(doc_rid: u32, content: []const u8, cursor_index: u32, active_block_index: u32) void {
        if (state.tree.text_docs.getDataPtr(doc_rid)) |doc| {
            const block = doc.blocks.items[doc.active_block_index];
            if (block.data.composition_text) |composition_text| {
                state.tree.text_docs.resetParagraphsWithText(doc_rid, composition_text.comp_text, true);
            } else {
                state.tree.text_docs.resetParagraphsWithText(doc_rid, content, false);
            }
            if (doc == state.page.doc) {
                state.page.doc.cursor_char_index = cursor_index;
                state.page.doc.active_block_index = active_block_index;
            }
        }
    }
    export fn setTextDocEx(doc_rid: u32, content_ptr: [*c]u8, content_len: u32, cursor_index: u32, active_block_index: u32) void {
        if (content_len == 0) {
            setTextDoc(doc_rid, &.{}, cursor_index, active_block_index);
        } else {
            setTextDoc(doc_rid, content_ptr[0..content_len], cursor_index, active_block_index);
        }
    }

    export fn fetchFontEx(font_family_ptr: [*c]u8, font_family_len: u32, url_ptr: [*c]u8, url_len: u32, style: u8, weight: u16) void {
        const family = font_family_ptr[0..font_family_len];
        const url = url_ptr[0..url_len];
        fetchFont(.{
            .family = family,
            .style = if (style == 0) .normal else .italic,
            .weight = FontDB.Weight.from_value(weight),
        }, url) catch |err| {
            dino.log.err(.api, "fetchFontEx() error: {}", .{err});
        };
    }
    inline fn getHorizontalAlignment(alignment: u8) dino.text.HorizontalAlignment {
        return std.meta.intToEnum(dino.text.HorizontalAlignment, alignment) catch .left;
    }
    inline fn getVerticalAlignment(alignment: u8) dino.text.VerticalAlignment {
        return std.meta.intToEnum(dino.text.VerticalAlignment, alignment) catch .top;
    }
    inline fn setTextStyle(doc_rid: u32, font_family: []const u8, font_style: u8, font_weight: u16, font_size: f32, letter_spacing: f32, line_spacing: f32, paragraph_spacing: f32, horizontal_alignment: u8, vertical_alignment: u8) void {
        if (state.tree.text_docs.getDataPtr(doc_rid)) |doc| {
            var style = &doc.spans.items[0].style;
            const new_font_family = dino.galloc.dupe(u8, font_family) catch |err| blk: {
                dino.log.err(.api, "setTextStyle() error: {}", .{err});
                break :blk &.{};
            };

            style.font = FontDB.FontSetting{
                .family = new_font_family,
                .style = if (font_style == 0) .normal else .italic,
                .weight = dino.text.FontDB.Weight.from_value(font_weight),
                .stretch = .normal,
            };
            style.font_size = font_size;
            style.letter_spacing = letter_spacing;
            style.line_height = line_spacing;
            _ = paragraph_spacing;
            style.horizontal_alignment = getHorizontalAlignment(horizontal_alignment);
            style.vertical_alignment = getVerticalAlignment(vertical_alignment);

            for (doc.blocks.items) |*block| {
                block.data.spans.items[0].range.length = block.data.string.length();
                block.data.spans.items[0].style = style.*;
            }

            // ptr.* = .{
            //     .font_family = new_font_family,
            //     .font_style = if (font_style == 0) .normal else .italic,
            //     .font_weight = dino.TextDoc.Style.from_value(font_weight),
            //     .font_size = font_size,
            //     .letter_spacing = letter_spacing,
            //     .line_spacing = line_spacing,
            //     .paragraph_spacing = paragraph_spacing,
            // };
        }
    }
    export fn setTextStyleEx(doc_rid: u32, font_family_ptr: [*c]u8, font_family_len: u32, font_style: u8, font_weight: u16, font_size: f32, letter_spacing: f32, line_spacing: f32, paragraph_spacing: f32, horizontal_alignment: u8, vertical_alignment: u8) void {
        setTextStyle(doc_rid, font_family_ptr[0..font_family_len], font_style, font_weight, font_size, letter_spacing, line_spacing, paragraph_spacing, horizontal_alignment, vertical_alignment);
    }

    export fn buildLayoutAndPathEx(node_id: u32, doc_rid: u32, max_width: f32, max_height: f32) void {
        if (state.tree.text_docs.getDataPtr(doc_rid)) |doc| {
            doc.buildDocLayout(state.tree.font_db, max_width, max_height) catch |err| {
                dino.log.err(.api, "buildLayoutAndPathEx error: {}", .{err});
            };
            if (state.update_size_cache.get(node_id)) |_| {
                if (doc.docBBox()) |bbox| {
                    ext.onUpdateElementSize(node_id, bbox.width(), bbox.height());
                }
                _ = state.update_size_cache.remove(node_id);
            }
        }
    }

    export fn getTextSelectionBlockEx(node_id: u32) [*c]f32 {
        const doc_id = getTextDocID(node_id);
        const doc = state.tree.text_docs.getDataPtr(doc_id).?;
        const line_count = getTextLineLength(node_id);

        // Early exit if no lines
        if (line_count == 0) return null;

        const buf = allocArena32(4 * line_count);
        if (buf == null) return null;

        var buf_idx: usize = 0;
        const block_count = doc.blockCount();
        const font_size = doc.spans.items[0].style.font_size orelse 12.0;
        const line_sacle: f32 = if (doc.spans.items[0].style.line_height == -1) 1.2 else doc.spans.items[0].style.line_height.?;

        for (0..block_count) |block_index| {
            const block_bbox = state.page.blockBBox(block_index) orelse {
                dino.log.err(.api, "getTextSelectionBlockEx error: block_index={} has no bbox", .{block_index});
                continue; // Skip this block instead of returning null
            };

            // Get selection range for this block
            if (doc.getSelectionRange(block_index)) |range| {
                // Skip empty selections
                if (range.length == 0) {
                    // Add empty rectangle for this block
                    buf[buf_idx] = 0;
                    buf[buf_idx + 1] = 0;
                    buf[buf_idx + 2] = 0;
                    buf[buf_idx + 3] = 0;
                    buf_idx += 4;
                    continue;
                }

                const line = doc.blocks.items[block_index].data.layout.?.lines.items[0];
                const line_height = if (doc.spans.items[0].style.line_height.? < 0.0) line.ascent - line.descent else line_sacle * font_size;
                const scaled_height = @ceil(line_height); //block_bbox.height() / block_line_count;
                const original_height = @round(line.ascent - line.descent);
                const scaled_offset = if (line_sacle < original_height) @ceil((line.ascent - line.descent - @round(line_height)) / 2.0) else 0;
                const height = if (original_height > scaled_height) original_height else scaled_height;

                // Pre-calculate line indices for the selection range
                var line_bboxes = std.AutoHashMap(usize, math.Rect2f).init(dino.galloc);
                defer line_bboxes.deinit();

                // First pass: identify all lines in the selection and initialize their bounding boxes
                var prev_line_index: usize = std.math.maxInt(usize);

                for (range.start()..range.end()) |char_idx| {
                    const line_index = state.page.getLineIndexByBlockAndCharIndex(block_index, char_idx);

                    // Only process each line once during initialization
                    if (line_index != prev_line_index) {
                        prev_line_index = line_index;

                        // Get the line's bounding box
                        const lineBBox = state.page.lineBBox(block_index, line_index) orelse
                            math.rect2f(block_bbox.left(), block_bbox.top() + @as(f32, @floatFromInt(line_index)) * height, 0, height);
                        const final_height = if (lineBBox.height() < original_height) height else lineBBox.height();
                        const final_top = (if (lineBBox.height() < original_height) lineBBox.top() - scaled_offset else block_bbox.top() + @as(f32, @floatFromInt(line_index)) * final_height);
                        // Initialize with zero width to be expanded by characters
                        line_bboxes.put(line_index, math.rect2f(block_bbox.left(), final_top, 0, final_height)) catch continue;
                    }
                }

                // Second pass: expand each line's bounding box with character bounding boxes
                for (range.start()..range.end()) |char_idx| {
                    if (state.page.characterBBox(block_index, char_idx)) |char_bbox| {
                        const line_index = state.page.getLineIndexByBlockAndCharIndex(block_index, char_idx);

                        if (line_bboxes.getPtr(line_index)) |line_bbox_ptr| {
                            // For the first character in a line, initialize the left position
                            if (line_bbox_ptr.width() == 0) {
                                line_bbox_ptr.* = math.rect2f(char_bbox.left(), line_bbox_ptr.top(), char_bbox.width(), line_bbox_ptr.height());
                            } else {
                                // Expand the line's bounding box to include this character
                                const char_right = char_bbox.left() + char_bbox.width();
                                const line_right = line_bbox_ptr.left() + line_bbox_ptr.width();

                                // Update left edge if needed
                                if (char_bbox.left() < line_bbox_ptr.left()) {
                                    const width_diff = line_bbox_ptr.left() - char_bbox.left();
                                    line_bbox_ptr.* = math.rect2f(char_bbox.left(), line_bbox_ptr.top(), line_bbox_ptr.width() + width_diff, line_bbox_ptr.height());
                                }

                                // Update right edge if needed
                                if (char_right > line_right) {
                                    line_bbox_ptr.* = math.rect2f(line_bbox_ptr.left(), line_bbox_ptr.top(), char_right - line_bbox_ptr.left(), line_bbox_ptr.height());
                                }
                            }
                        }
                    }
                }

                // Store all line bounding boxes in the buffer
                var line_it = line_bboxes.iterator();
                while (line_it.next()) |entry| {
                    const bbox = entry.value_ptr.*;

                    // Only store non-empty bounding boxes
                    if (bbox.width() > 0) {
                        buf[buf_idx] = bbox.left();
                        buf[buf_idx + 1] = @floor(bbox.top());
                        buf[buf_idx + 2] = bbox.width();
                        buf[buf_idx + 3] = bbox.height();
                        buf_idx += 4;
                    }
                }
            } else {
                // No selection in this block, add empty rectangle
                buf[buf_idx] = 0;
                buf[buf_idx + 1] = 0;
                buf[buf_idx + 2] = 0;
                buf[buf_idx + 3] = 0;
                buf_idx += 4;
            }
        }
        return buf;
    }

    export fn getTextEditingNodeIdEx() u32 {
        return state.page.edit_node_id.?;
    }
    export fn getTextCaretPosEx() [*c]f32 {
        const buf = allocArena32(4);
        if (state.page.mouse.cursor_visible and !state.page.doc.hasSelection()) {
            if (state.page.cursorLine()) |line| {
                buf[0] = line.p1().x();
                buf[1] = line.p1().y();
                buf[2] = line.p2().x();
                buf[3] = line.p2().y();
            }
        } else {
            buf[0] = 0;
            buf[1] = 0;
            buf[2] = 0;
            buf[3] = 0;
        }
        return buf;
    }

    export fn getTextOffsetEx(node_id: u32) [*c]f32 {
        _ = node_id;
        // if (state.text_caches.getTextOffset(node_id)) |ptr| {
        //     return ptr;
        // }
        return 0;
    }

    export fn getTextLayoutBoxEx(doc_id: u32) [*c]f32 {
        if (state.tree.text_docs.getDataPtr(doc_id)) |ptr| {
            const buf = allocArena32(4);
            if (ptr.docBBox()) |bbox| {
                buf[0] = bbox.left();
                buf[1] = bbox.top();
                buf[2] = bbox.width();
                buf[3] = bbox.height();
                return buf;
            }
        }
        return 0;
    }

    export fn getTextAutoLineHeightValue(node_id: u32) f32 {
        const doc_id = getTextDocID(node_id);
        if (state.tree.text_docs.getDataPtr(doc_id)) |doc| {
            const line = doc.blocks.items[0].data.layout.?.lines.items[0];
            const cluster_height = line.ascent - line.descent;
            return @round(cluster_height) / doc.spans.items[0].style.font_size.?;
        } else {
            std.log.warn("getTextAutoLineHeightValue not found doc", .{});
        }
        return 0.0;
    }

    fn makeColor(color: col.Color) u32 {
        return state.tree.colors.make(color) catch |err| blk: {
            dino.log.err(.api, "makeColor() error: {}", .{err});
            break :blk 0;
        };
    }
    export fn makeColorEx(r: f32, g: f32, b: f32, a: f32) u32 {
        return makeColor(col.Color.init(r, g, b, a));
    }
    fn setColor(color_id: u32, color: col.Color) void {
        if (state.tree.colors.getDataPtr(color_id)) |ptr| {
            ptr.* = .{
                .rgba = .{ color.r, color.g, color.b, color.a },
            };
        }
    }
    export fn setColorEx(color_id: u32, r: f32, g: f32, b: f32, a: f32) void {
        setColor(color_id, col.Color.init(r, g, b, a));
    }
    export fn destroyColor(color_id: u32) void {
        state.tree.colors.destroy(color_id) catch |err| {
            dino.log.err(.api, "destroyColor error: color_id={} {}", .{ color_id, err });
        };
    }

    fn makeGradient(tag: sd.GradientTag, mat: math.Mat2Df) u32 {
        return state.tree.gradients.make(.{
            .tag = tag,
            .mat = mat,
        }) catch |err| blk: {
            dino.log.err(.api, "makeGradient error: {}", .{err});
            break :blk 0;
        };
    }
    export fn makeGradientEx(tag: sd.GradientTag, a: f32, b: f32, c: f32, d: f32, e: f32, f: f32) u32 {
        return makeGradient(tag, math.mat2df(a, b, c, d, e, f));
    }
    fn setGradientMatrix(grad_id: u32, mat: math.Mat2Df) void {
        if (state.tree.gradients.getDataPtr(grad_id)) |gradient_data| {
            gradient_data.mat = mat;
        }
    }
    export fn setGradientMatrixEx(gradient_id: u32, a: f32, b: f32, c: f32, d: f32, e: f32, f: f32) void {
        setGradientMatrix(gradient_id, math.mat2df(a, b, c, d, e, f));
    }
    export fn setGradientTag(gradient_id: u32, tag: sd.GradientTag) void {
        if (state.tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
            gradient_data.tag = tag;
        }
    }
    export fn setGradientStopLen(gradient_id: u32, len: u32) void {
        if (state.tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
            gradient_data.stop_len = @intCast(@min(len, 16));
        }
    }
    fn setGradientStop(gradient_id: u32, idx: u32, pos: f32, color: col.Color) void {
        if (state.tree.gradients.getDataPtr(gradient_id)) |gradient_data| {
            gradient_data.stops[idx].pos = pos;
            gradient_data.stops[idx].color = color;
        }
    }
    export fn setGradientStopEx(gradient_id: u32, idx: u32, pos: f32, r: f32, g: f32, b: f32, a: f32) void {
        setGradientStop(gradient_id, idx, pos, col.Color.init(r, g, b, a));
    }
    export fn updateGradientPixels(gradient_id: u32) void {
        state.tree.gradients.updatePixels(gradient_id);
        state.tree.gradients.dirty = true;
    }
    export fn destroyGradient(gradient_id: u32) void {
        state.tree.gradients.destroy(gradient_id) catch |err| {
            dino.log.err(.api, "destroyGradient error: grad_id={} {}", .{ gradient_id, err });
        };
    }

    export fn makeStroke(width: f32, cap: sd.LineCap, join: sd.LineJoin, miterlimit: f32, dash: f32, gap: f32) u32 {
        var stroke: sd.StrokeData = .{
            .linewidth = width,
            .linecap = cap,
            .linejoin = join,
            .miterlimit = miterlimit,
        };
        if (dash > 0.0 and gap > 0.0) {
            stroke.dash_array[0] = dash;
            stroke.dash_array[1] = gap;
            stroke.dash_len = 2;
        } else {
            stroke.dash_len = 0;
        }
        return state.tree.strokes.make(stroke) catch |err| {
            dino.log.err(.api, "makeStroke error: {}", .{err});
            return 0;
        };
    }

    export fn destroyStrokePathCacheEx(node_id: u32, stroke_id: u32) void {
        destroyStrokePathCache(.{
            .path = node_id,
            .stroke = stroke_id,
        });
    }

    // TODO: add dasharray support
    export fn setStroke(stroke_id: u32, width: f32, cap: sd.LineCap, join: sd.LineJoin, miterlimit: f32, dash: f32, gap: f32) void {
        if (state.tree.strokes.getDataPtr(stroke_id)) |stroke_data| {
            stroke_data.linewidth = width;
            stroke_data.linecap = cap;
            stroke_data.linejoin = join;
            stroke_data.miterlimit = miterlimit;
            if (dash > 0.0 and gap > 0.0) {
                stroke_data.dash_array[0] = dash;
                stroke_data.dash_array[1] = gap;
                stroke_data.dash_len = 2;
            } else {
                stroke_data.dash_len = 0;
            }
        }
    }
    export fn destroyStroke(stroke_id: u32) void {
        state.tree.strokes.destroy(stroke_id) catch |err| {
            dino.log.err(.api, "destroyStroke error: stroke_id={} {}", .{ stroke_id, err });
        };
    }

    /// Convert text to path and return data ranges for it.
    /// `buf` is a 4 number array for `cmd_ptr, cmd_len, vts_ptr, vts_len`.
    export fn getTextPathEx(text_doc_id: u32) [*c]u32 {
        const tree = &state.tree;
        // const engine = &state.text_engine;
        // var text_doc: *sd.TextDoc = undefined;
        if (tree.text_docs.getPathsPtr(text_doc_id)) |block_path_list| {
            var builder = Path.Builder.init(arena.allocator());
            for (block_path_list.items) |block| {
                const p = block.path;
                builder.append(p) catch |err| {
                    dino.log.err(.api, "getTextPathEx error: {}", .{err});
                    return null; // Return null or handle accordingly
                };
            }
            const path = Path.cast(builder);
            var buf = allocArenaU32(4);
            buf[0] = @intCast(@intFromPtr(path.commands.ptr));
            buf[1] = @intCast(path.commands.len);
            buf[2] = @intCast(@intFromPtr(path.vertices.ptr));
            buf[3] = @intCast(path.vertices.len);
            return buf;
        }

        return null;
    }

    export fn getPathVtxEx(path_id: u32) [*c]u32 {
        var buf = allocArenaU32(2);
        buf[0] = 0;
        buf[1] = 0;
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            buf[0] = @intCast(@intFromPtr(path_data.path.vertices.ptr));
            buf[1] = @intCast(path_data.path.vertices.len);
            return buf;
        }
        return buf;
    }

    export fn getPathCmdEx(path_id: u32) [*c]u32 {
        var buf = allocArenaU32(2);
        buf[0] = 0;
        buf[1] = 0;
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            buf[0] = @intCast(@intFromPtr(path_data.path.commands.ptr));
            buf[1] = @intCast(path_data.path.commands.len);
            return buf;
        }
        return buf;
    }

    export fn getStrokePathCmdEx(path_id: u32, stroke_id: u32) [*c]u32 {
        var buf = allocArenaU32(2);
        buf[0] = 0;
        buf[1] = 0;
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            if (state.tree.strokes.getDataPtr(stroke_id)) |stroke_data| {
                const stroke_path = state.stroke_path_cache.getOrPut(.{
                    .path = path_id,
                    .stroke = stroke_id,
                }) catch {
                    return buf;
                };
                if (!stroke_path.found_existing) {
                    var p: Path = .{};
                    if (!path_data.isEmpty()) {
                        // TODO: rect/oval fast path rendering
                        var path: Path = undefined;
                        switch (path_data.*) {
                            .rect => |rect| {
                                var pb = Path.Builder.init(arena.allocator());
                                pb.addRect(rect.left(), rect.top(), rect.width(), rect.height()) catch return buf;
                                path = pb.toOwnedPath() catch return buf;
                            },
                            .oval => |oval| {
                                var pb = Path.Builder.init(arena.allocator());
                                pb.addOval(oval.left(), oval.top(), oval.width(), oval.height()) catch return buf;
                                path = pb.toOwnedPath() catch return buf;
                            },
                            .path => |pp| path = pp,
                        }
                        p = if (path.isEmpty()) .{} else stroker.strokePath(dino.galloc, path, stroke_data.*);
                    }
                    stroke_path.value_ptr.* = .{
                        .path = .{ .path = p },
                        .mesh = null,
                    };
                }
                buf[0] = @intCast(@intFromPtr(stroke_path.value_ptr.*.path.?.path.commands.ptr));
                buf[1] = @intCast(stroke_path.value_ptr.*.path.?.path.commands.len);
                return buf;
            }
        }

        return buf;
    }

    export fn getStrokePathVtxEx(path_id: u32, stroke_id: u32) [*c]u32 {
        var buf = allocArenaU32(2);
        buf[0] = 0;
        buf[1] = 0;
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            if (state.tree.strokes.getDataPtr(stroke_id)) |stroke_data| {
                const stroke_path = state.stroke_path_cache.getOrPut(.{
                    .path = path_id,
                    .stroke = stroke_id,
                }) catch {
                    return buf;
                };
                if (!stroke_path.found_existing) {
                    var p: Path = .{};
                    if (!path_data.isEmpty()) {
                        // TODO: rect/oval fast path rendering
                        var path: Path = undefined;
                        switch (path_data.*) {
                            .rect => |rect| {
                                var pb = Path.Builder.init(arena.allocator());
                                pb.addRect(rect.left(), rect.top(), rect.width(), rect.height()) catch return buf;
                                path = pb.toOwnedPath() catch return buf;
                            },
                            .oval => |oval| {
                                var pb = Path.Builder.init(arena.allocator());
                                pb.addOval(oval.left(), oval.top(), oval.width(), oval.height()) catch return buf;
                                path = pb.toOwnedPath() catch return buf;
                            },
                            .path => |pp| path = pp,
                        }
                        p = if (path.isEmpty()) .{} else stroker.strokePath(dino.galloc, path, stroke_data.*);
                    }
                    stroke_path.value_ptr.* = .{
                        .path = .{ .path = p },
                        .mesh = null,
                    };
                }
                buf[0] = @intCast(@intFromPtr(stroke_path.value_ptr.*.path.?.path.vertices.ptr));
                buf[1] = @intCast(stroke_path.value_ptr.*.path.?.path.vertices.len);
                return buf;
            }
        }
        return buf;
    }

    export fn allocImage() u32 {
        const image_id = state.tree.images.alloc() catch {
            return 0;
        };
        state.tree.images.setState(image_id, .loading) catch {};
        return image_id;
    }
    fn makeImageWithURL(url: []const u8) u32 {
        // fetch
        const file_type = FileType.guess(url);
        switch (file_type) {
            .png, .jpg => {
                const image_id = state.tree.images.alloc() catch {
                    return 0;
                };
                setImageWithURL(image_id, url);
                return image_id;
            },
            else => {
                // TODO: show error prompt: "not supported image format"
            },
        }
        return 0;
    }
    export fn makeImageWithURLEx(url_ptr: [*c]const u8, url_len: u32) u32 {
        return makeImageWithURL(url_ptr[0..url_len]);
    }
    fn setImageWithURL(image_id: u32, url: []const u8) void {
        // fetch
        const file_type = FileType.guess(url);
        switch (file_type) {
            .png, .jpg => {
                const paint_id = image_id;
                state.tree.images.setState(paint_id, .loading) catch {};

                const ud: FetchUserData = .{
                    .file_type = @intFromEnum(file_type),
                    .res_idx = paint_id,
                };
                _ = fetch.send(.{
                    .path = url.ptr,
                    .callback = fileLoadedFromFetch,
                    .buffer = utils.asRange(fetch_buf.image),
                    .user_data = utils.asRange(&ud),
                    .channel = 2,
                });
            },
            else => {
                // TODO: show error prompt: "not supported image format"
            },
        }
    }
    export fn setImageWithURLEx(image_id: u32, url_ptr: [*c]const u8, url_len: u32) void {
        setImageWithURL(image_id, url_ptr[0..url_len]);
    }
    fn makeImageWithFileData(data: []const u8) u32 {
        var img = dino.image.Image.loadFromMemory(data, 4) catch {
            return 0;
        };
        defer img.deinit();

        const paint_id = state.tree.images.make(.{
            .tag = .data,
            .data = img.data,
            .size = math.vec2f(@floatFromInt(img.width), @floatFromInt(img.height)),
        }) catch {
            return 0;
        };
        state.tree.images.setState(paint_id, .valid) catch {};
        return paint_id;
    }
    export fn makeImageWithFileDataEx(data_ptr: [*c]const u8, data_len: u32) u32 {
        return makeImageWithFileData(data_ptr[0..data_len]);
    }
    fn setImageWithFileData(image_id: u32, data: []const u8) void {
        var img = dino.image.Image.loadFromMemory(data, 4) catch {
            return;
        };
        defer img.deinit();

        state.tree.images.clearData(image_id) catch {};
        state.tree.images.initWithData(image_id, .{
            .tag = .data,
            .data = img.data,
            .size = math.vec2f(@floatFromInt(img.width), @floatFromInt(img.height)),
        }) catch {};
    }
    export fn setImageWithFileDataEx(image_id: u32, data_ptr: [*c]const u8, data_len: u32) void {
        setImageWithFileData(image_id, data_ptr[0..data_len]);
    }
    fn makeImageWithPixels(pixels: []const u8, w: u32, h: u32) u32 {
        var img_data: sd.ImageData = .{};
        img_data.tag = .data;
        img_data.data = pixels;
        img_data.size = math.vec2f(@floatFromInt(w), @floatFromInt(h));
        const paint_id = state.tree.images.make(img_data) catch {
            return 0;
        };
        state.tree.images.setState(paint_id, .valid) catch {};
        return paint_id;
    }
    export fn makeImageWithPixelsEx(pixels_ptr: [*c]const u8, pixels_len: u32, w: u32, h: u32) u32 {
        return makeImageWithPixels(pixels_ptr[0..pixels_len], w, h);
    }
    fn setImageWithPixels(image_id: u32, pixels: []const u8, w: u32, h: u32) void {
        state.tree.images.clearData(image_id) catch {};
        state.tree.images.initWithData(image_id, .{
            .tag = .data,
            .data = pixels,
            .size = math.vec2f(@floatFromInt(w), @floatFromInt(h)),
        }) catch {};
        state.tree.images.setState(image_id, .valid) catch {};
    }
    export fn setImageWithPixelsEx(image_id: u32, pixels_ptr: [*c]const u8, pixels_len: u32, w: u32, h: u32) void {
        setImageWithPixels(image_id, pixels_ptr[0..pixels_len], w, h);
    }
    fn setImageFillMode(image_id: u32, mode: sd.ImageFillMode) void {
        if (state.tree.images.getDataPtr(image_id)) |image_data| {
            image_data.mode = mode;
        }
    }
    export fn setImageFillModeEx(image_id: u32, mode: u8) void {
        if (std.meta.intToEnum(sd.ImageFillMode, mode)) |m| {
            setImageFillMode(image_id, m);
        } else |_| {}
    }
    export fn destroyImage(image_id: u32) void {
        state.tree.images.destroy(image_id) catch {};
    }

    export fn makeCompose(t: sd.ComposeTag, root_id: u32) u32 {
        const tree = &state.tree;
        const mask_data: sd.MaskData = .{
            .root = root_id,
        };
        return switch (t) {
            .clip => tree.composes.make(.{ .clip = mask_data }) catch |err| {
                dino.log.err(.api, "makeCompose error: {}", .{err});
                return 0;
            },
            .alpha_mask => tree.composes.make(.{ .alpha_mask = mask_data }) catch |err| {
                dino.log.err(.api, "makeCompose error: {}", .{err});
                return 0;
            },
            .inv_alpha_mask => tree.composes.make(.{ .inv_alpha_mask = mask_data }) catch |err| {
                dino.log.err(.api, "makeCompose error: {}", .{err});
                return 0;
            },
            .luma_mask => tree.composes.make(.{ .luma_mask = mask_data }) catch |err| {
                dino.log.err(.api, "makeCompose error: {}", .{err});
                return 0;
            },
            .inv_luma_mask => tree.composes.make(.{ .inv_luma_mask = mask_data }) catch |err| {
                dino.log.err(.api, "makeCompose error: {}", .{err});
                return 0;
            },
            else => return 0,
        };
    }
    export fn setComposeRoot(comp_id: u32, root_id: u32) void {
        if (state.tree.composes.getComposePtr(comp_id)) |comp| {
            switch (comp.*) {
                .clip, .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |*cc| {
                    cc.root = root_id;
                },
                else => {},
            }
        }
    }
    export fn destroyCompose(comp_id: u32) void {
        state.tree.composes.destroy(comp_id) catch |err| {
            dino.log.err(.api, "destroyCompose error: comp_id={} {}", .{ comp_id, err });
        };
    }

    export fn markNodeChanged(node_id: u32) void {
        state.update_list.put(node_id, {}) catch {};
    }

    export fn purge() void {
        state.tree.purge();
        addDefaultRoot(&state.tree) catch {};
        state.tileset_default.freeAllTiles() catch {};
        state.tileset_xray.freeAllTiles() catch {};
        state.tileset_outline.freeAllTiles() catch {};
        state.selected = state.tree.root;
    }

    export fn isPointInPath(path_id: u32, x: f32, y: f32) bool {
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            const base_path = state.base_path_cache.getOrPut(path_id) catch {
                return false;
            };
            if (!base_path.found_existing) {
                base_path.value_ptr.* = .{
                    .path = path_data.*,
                    .mesh = null,
                };
            }

            if (base_path.value_ptr.fetchMesh()) |mesh| {
                if (mesh.contains(x, y)) return true;
            }
        }
        return false;
    }

    export fn isPointInStroke(path_id: u32, stroke_id: u32, x: f32, y: f32) bool {
        if (state.tree.paths.getDataPtr(path_id)) |path_data| {
            if (state.tree.strokes.getDataPtr(stroke_id)) |stroke_data| {
                const stroke_path = state.stroke_path_cache.getOrPut(.{
                    .path = path_id,
                    .stroke = stroke_id,
                }) catch {
                    return false;
                };
                if (!stroke_path.found_existing) {
                    // TODO: rect/oval fast path rendering
                    var path: Path = undefined;
                    switch (path_data.*) {
                        .rect => |rect| {
                            var pb = Path.Builder.init(arena.allocator());
                            pb.addRect(rect.left(), rect.top(), rect.width(), rect.height()) catch return false;
                            path = pb.toOwnedPath() catch return false;
                        },
                        .oval => |oval| {
                            var pb = Path.Builder.init(arena.allocator());
                            pb.addOval(oval.left(), oval.top(), oval.width(), oval.height()) catch return false;
                            path = pb.toOwnedPath() catch return false;
                        },
                        .path => |p| path = p,
                    }

                    const p = stroker.strokePath(dino.galloc, path, stroke_data.*);
                    stroke_path.value_ptr.* = .{
                        .path = .{ .path = p },
                        .mesh = null,
                    };
                }

                if (stroke_path.value_ptr.fetchMesh()) |mesh| {
                    if (mesh.contains(x, y)) return true;
                }
            }
        }
        return false;
    }

    export fn isPointInText(node_rid: u32, x: f32, y: f32) bool {
        const doc_id = api.getTextDocID(node_rid);
        if (state.tree.text_docs.getDataPtr(doc_id)) |doc| {
            var isInPath = false;
            if (state.tree.text_docs.getPathsPtr(doc_id)) |block_path_list| {
                var builder = Path.Builder.init(arena.allocator());
                for (block_path_list.items) |block| {
                    const p = block.path;
                    builder.append(p) catch |err| {
                        dino.log.err(.api, "getTextPathEx error: {}", .{err});
                    };
                }
                const path = Path.cast(builder);
                var path_data = PathData{ .path = path };
                isInPath = path_data.bbox().contains(math.vec2f(x, y));
            }
            return doc.isPointInBlock(x, y) or isInPath;
        }
        return false;
    }

    export fn getNodeStorageCount() u32 {
        return state.tree.nodes.pool.liveHandleCount();
    }
    export fn getColorStorageCount() u32 {
        return state.tree.colors.pool.liveHandleCount();
    }
    export fn getPathStorageCount() u32 {
        return state.tree.paths.pool.liveHandleCount();
    }
    export fn getStrokeStorageCount() u32 {
        return state.tree.strokes.pool.liveHandleCount();
    }
};

const ReferenceDAG = struct {
    pub const Node = struct {
        /// Resource ID.
        id: u32 = 0,
        /// Node ID
        dependencies: ?u32 = null,
        /// Is node dirty?
        dirty: bool = false,
    };

    const Pool = dino.Pool(16, 16, Node, struct {
        node: Node,
    });
    const Map = std.AutoHashMap(u32, u32);

    pool: Pool,
    map: Map,

    pub fn make(dag: *ReferenceDAG, node_rid: u32) !void {
        const h = try dag.pool.add(.{
            .node = Node{
                .id = node_rid,
            },
        });
        _ = h;
    }
};

const TreeUpdate = struct {
    pub fn fullUpdate(tree: *SceneTree, root: u32) !void {
        var ctx: Context = .{
            .tree = tree,
            .count = 0,
            .max_depth = 0,
        };
        const root_xform_changed = if (tree.nodes.getTransformPtr(root)) |transform|
            transform.world == null
        else
            false;

        try state.tree.prepare();

        // update tileset before nodes change
        try freeTilesForNodes();
        // update nodes
        try updateNode(&ctx, root, root_xform_changed, 0);

        // dino.log.info(.hi, "node count: {d}, max_depth: {d}", .{ ctx.count, ctx.max_depth });

        // update tileset after nodes change
        try freeTilesForNodes();

        state.update_list.clearRetainingCapacity();
    }
    pub fn partialUpdate(tree: *SceneTree, root: u32, compute_obb: bool) !void {
        var ctx: Context = .{
            .tree = tree,
            .compute_obb = compute_obb,
        };
        const root_xform_changed = if (tree.nodes.getTransformPtr(root)) |transform|
            transform.world == null
        else
            false;

        try state.tree.prepare();

        // update nodes
        try updateNode(&ctx, root, root_xform_changed, 0);
    }

    fn updateTransform(ctx: *Context, node: *sd.NodeData, node_id: u32, force_update: bool) bool {
        const tree = ctx.tree;
        if (tree.nodes.getTransformPtr(node_id)) |transform| {
            if (force_update) {
                transform.local = null;
                transform.world = null;
            }
            if (transform.local == null) {
                transform.local = node.getTransformAsMat2D();
                transform.world = null;
            }
            if (transform.world == null) {
                var parent_mat = math.mat2df_identity();
                if (tree.nodes.getParent(node_id)) |parent_id| {
                    if (tree.nodes.getTransformPtr(parent_id)) |parent_transform| {
                        if (parent_transform.world) |mat| {
                            parent_mat = mat;
                        }
                    }
                }
                transform.world = parent_mat.mul(transform.local.?);
                return true;
            }
        }
        return force_update;
    }

    fn updateNode(ctx: *Context, node_rid: u32, force_update_transform: bool, depth: u32) !void {
        const tree = ctx.tree;
        ctx.count += 1;
        if (depth > ctx.max_depth) ctx.max_depth = depth;

        const node_ptr_opt = tree.nodes.getNodePtr(node_rid);
        if (node_ptr_opt) |node_ptr| {
            switch (node_ptr.data) {
                .group => |*node| {
                    const xform_updated = updateTransform(ctx, node_ptr, node_rid, force_update_transform);

                    var first_aabb = true;
                    var first_obb = true;
                    var first_visual = true;
                    var iter = tree.nodes.children(node_rid);
                    var aabb = math.rect2f(0, 0, 0, 0);
                    var local_obb = math.Quadf.initEmpty();
                    var visual_aabb = math.rect2f(0, 0, 0, 0);
                    while (iter.next()) |child_id| {
                        try updateNode(ctx, child_id, xform_updated, depth + 1);

                        if (tree.nodes.getBBoxPtr(node_rid)) |bbox| {
                            if (tree.nodes.getBBoxPtr(child_id)) |child_bbox| {
                                if (child_bbox.aabb) |child_aabb| {
                                    if (!child_aabb.isEmpty()) {
                                        aabb = if (first_aabb) child_aabb else aabb.unite(child_aabb);
                                        first_aabb = false;
                                    }
                                }

                                if (child_bbox.visual_aabb) |child_visual_aabb| {
                                    if (!child_visual_aabb.isEmpty()) {
                                        visual_aabb = if (first_visual) child_visual_aabb else visual_aabb.unite(child_visual_aabb);
                                        first_visual = false;
                                    }
                                }
                            }
                            bbox.aabb = aabb;
                            bbox.visual_aabb = visual_aabb;
                        }
                        if (ctx.compute_obb) {
                            if (tree.nodes.getOBBoxPtr(child_id)) |child_obb| {
                                local_obb = if (first_obb) child_obb.local else local_obb.uniteAlignAxis(child_obb.local);
                                first_obb = false;
                            }
                        }
                    }

                    if (ctx.compute_obb) {
                        if (tree.nodes.getOBBoxPtr(node_rid)) |obb| {
                            obb.local = local_obb;
                            const mat = if (tree.nodes.getTransformPtr(node_rid)) |transform|
                                transform.world orelse math.mat2df_identity()
                            else
                                math.mat2df_identity();
                            obb.world = mat.transformQuad(obb.local);
                        }
                    }

                    // update compose subtrees
                    for (0..node.compose.len) |i| {
                        if (node.compose[i] > 0) {
                            if (tree.composes.getComposePtr(node.compose[i])) |comp| {
                                switch (comp.*) {
                                    .clip, .alpha_mask, .inv_alpha_mask, .luma_mask, .inv_luma_mask => |cc| {
                                        try updateNode(ctx, cc.root, true, depth + 1);
                                    },
                                    else => {},
                                }
                            }
                        }
                    }
                },
                .path => |*node| {
                    _ = updateTransform(ctx, node_ptr, node_rid, force_update_transform);

                    var local_bbox = math.rect2f(0, 0, 0, 0);
                    var visual_bbox = math.rect2f(0, 0, 0, 0);
                    if (tree.paths.getDataPtr(node.path_id)) |path_data| {
                        local_bbox = tree.paths.getBBox(node.path_id).?;

                        visual_bbox = local_bbox;
                        if (node.strokes[0]) |*path_stroke| {
                            if (tree.strokes.getDataPtr(path_stroke.data_id)) |stroke_data| {
                                const stroke_path = try state.stroke_path_cache.getOrPut(.{
                                    .path = node.path_id,
                                    .stroke = path_stroke.data_id,
                                });
                                if (!stroke_path.found_existing) {
                                    // TODO: rect/oval fast path rendering
                                    var path: Path = undefined;
                                    switch (path_data.*) {
                                        .rect => |rect| {
                                            var pb = Path.Builder.init(arena.allocator());
                                            try pb.addRect(rect.left(), rect.top(), rect.width(), rect.height());
                                            path = try pb.toOwnedPath();
                                        },
                                        .oval => |oval| {
                                            var pb = Path.Builder.init(arena.allocator());
                                            try pb.addOval(oval.left(), oval.top(), oval.width(), oval.height());
                                            path = try pb.toOwnedPath();
                                        },
                                        .path => |p| path = p,
                                    }

                                    const p = stroker.strokePath(dino.galloc, path, stroke_data.*);
                                    stroke_path.value_ptr.* = .{
                                        .path = .{ .path = p },
                                        .mesh = null,
                                    };
                                }

                                if (stroke_path.value_ptr.fetchMesh()) |mesh| {
                                    visual_bbox = visual_bbox.unite(mesh.bbox);
                                }
                            }
                        }

                        const bbox = tree.nodes.getBBoxPtr(node_rid).?;
                        bbox.local = local_bbox;
                        bbox.visual_local = visual_bbox;
                        const mat = if (tree.nodes.getTransformPtr(node_rid)) |transform|
                            transform.world orelse math.mat2df_identity()
                        else
                            math.mat2df_identity();
                        bbox.aabb = mat.transformRect(local_bbox);
                        bbox.visual_aabb = mat.transformRect(visual_bbox);

                        if (ctx.compute_obb) {
                            const obb = tree.nodes.getOBBoxPtr(node_rid).?;
                            obb.local = math.Quadf.initWithRect(local_bbox);
                            obb.world = mat.transformQuad(obb.local);
                        }
                    }
                },
                .text => |*node| {
                    _ = updateTransform(ctx, node_ptr, node_rid, force_update_transform);
                    const doc = state.tree.text_docs.getDataPtr(node.doc_id).?;
                    var local_bbox = math.rect2f(0, 0, 0, 0);
                    var visual_bbox = math.rect2f(0, 0, 0, 0);

                    if (doc.docBBox()) |layout_bbox| {
                        local_bbox = layout_bbox;
                        visual_bbox = local_bbox;

                        // if text has path data, use it as the visual_local for hit test

                        const text_path_id = ext.onGetTextPathId(node_rid);
                        const textPath = state.tree.paths.getDataPtr(text_path_id).?.*;
                        const textBbox = textPath.bbox();
                        visual_bbox = visual_bbox.unite(textBbox);
                        const path_stroke = node.stroke;
                        if (tree.strokes.getDataPtr(path_stroke.data_id)) |stroke_data| {
                            const stroke_path = try state.stroke_path_cache.getOrPut(.{
                                .path = node_rid,
                                .stroke = path_stroke.data_id,
                            });
                            if (!stroke_path.found_existing) {
                                // if (state.text_caches.getPath(node_rid)) |path| {
                                const p = stroker.strokePath(dino.galloc, textPath.path, stroke_data.*);
                                stroke_path.value_ptr.* = .{
                                    .path = .{ .path = p },
                                    .mesh = null,
                                };
                            }
                            if (stroke_path.value_ptr.fetchMesh()) |mesh| {
                                visual_bbox = visual_bbox.unite(mesh.bbox);
                            }
                        }

                        const bbox = tree.nodes.getBBoxPtr(node_rid).?;
                        bbox.local = local_bbox;
                        bbox.visual_local = visual_bbox;
                        const mat = if (tree.nodes.getTransformPtr(node_rid)) |transform|
                            transform.world orelse math.mat2df_identity()
                        else
                            math.mat2df_identity();
                        bbox.aabb = mat.transformRect(local_bbox);
                        bbox.visual_aabb = mat.transformRect(visual_bbox);

                        if (ctx.compute_obb) {
                            const obb = tree.nodes.getOBBoxPtr(node_rid).?;
                            obb.local = math.Quadf.initWithRect(local_bbox);
                            obb.world = mat.transformQuad(obb.local);
                        }
                    }
                },
            }
        }
    }

    fn freeTilesForNodes() !void {
        var it = state.update_list.keyIterator();
        while (it.next()) |node_id| {
            if (state.tree.nodes.getBBoxPtr(node_id.*)) |bbox| {
                if (bbox.visual_aabb) |aabb| {
                    try state.tileset_default.freeTilesInArea(aabb);
                    try state.tileset_xray.freeTilesInArea(aabb);
                    try state.tileset_outline.freeTilesInArea(aabb);
                }
            }
        }
    }

    const Context = struct {
        tree: *SceneTree,
        compute_obb: bool = false,
        count: u32 = 0,
        max_depth: u32 = 0,
    };
};

const updateTree = TreeUpdate.fullUpdate;
const updateSubTree = TreeUpdate.partialUpdate;

fn addDefaultRoot(tree: *SceneTree) !void {
    const id = try tree.nodes.make(.{});
    if (tree.nodes.getNodePtr(id)) |node| {
        node.id = id;
    }
    tree.root = id;
}

// --------------------------------------------------------
// API
// --------------------------------------------------------

const ext = struct {
    extern fn onReady() void;
    extern fn onFrameBegin() void;
    extern fn onOverlay() void;
    extern fn onUpdateSceneTree() void;
    extern fn onNodeUpdate(node_id: u32, flag: u32) void;
    extern fn onCopyText(text: [*]const u8, len: u32) void;
    extern fn onUndoComb() void;
    extern fn onRedoComb() void;
    extern fn onPasteText() void;
    extern fn onPrepareCamera() bool;
    extern fn onFrameEnd() void;
    extern fn onCapture(ptr: *u8, len: u32) void;
    extern fn onUpdateElementSize(node_id: u32, width: f32, height: f32) void;
    extern fn onGetTextPathId(node_id: u32) u32;
    extern fn onUpdatePageOffset(node_id: u32) void;
};

// memory

export fn allocPerm(size: u32) [*c]u8 {
    if (dino.galloc.alloc(u8, @intCast(size))) |ptr| {
        return @ptrCast(ptr);
    } else |_| {
        // dino.log.debug(@src(), .api, "allocPerm() failed", .{});
        return null;
    }
}
export fn allocPerm32(size: u32) [*c]f32 {
    if (dino.galloc.alloc(f32, @intCast(size))) |ptr| {
        return @ptrCast(ptr);
    } else |_| {
        // dino.log.debug(@src(), .api, "allocPerm32() failed", .{});
        return null;
    }
}
export fn allocArena(size: u32) [*c]u8 {
    if (arena.allocator().alloc(u8, @intCast(size))) |mem| {
        return @ptrCast(mem);
    } else |_| {
        // dino.log.debug(@src(), .api, "allocArena() failed", .{});
        return null;
    }
}
export fn allocArena32(size: u32) [*c]f32 {
    if (arena.allocator().alloc(f32, @intCast(size))) |ptr| {
        return @ptrCast(ptr);
    } else |_| {
        // dino.log.debug(@src(), .api, "allocArena32() failed", .{});
        return null;
    }
}
export fn allocArenaU32(size: u32) [*c]u32 {
    if (arena.allocator().alloc(u32, @intCast(size))) |ptr| {
        return @ptrCast(ptr);
    } else |_| {
        dino.log.debug(@src(), .api, "allocArenaU32() failed", .{});
        return null;
    }
}
export fn freePerm(ptr: [*c]u8, len: u32) void {
    dino.galloc.free(ptr[0..len]);
}
export fn freeArena(ptr: [*c]u8, len: u32) void {
    arena.allocator().free(ptr[0..len]);
}

// overlay

export fn setTransform(a: f32, b: f32, c: f32, d: f32, e: f32, f: f32) void {
    canvas.transform = math.mat2df(a, b, c, d, e, f);
}
export fn fillStyle(r: f32, g: f32, b: f32, a: f32) void {
    canvas.fill = col.Color.init(r, g, b, a);
}
export fn strokeStyle(w: f32, r: f32, g: f32, b: f32, a: f32) void {
    canvas.stroke.color = col.Color.init(r, g, b, a);
    canvas.stroke.width = w;
}
export fn drawPath(layer: u32, x: f32, y: f32, cmd_ptr: [*]const u8, cmd_len: u32, vtx_ptr: [*]const f32, vtx_len: u32) void {
    canvas.drawPath(layer, x, y, .{
        .commands = cmd_ptr[0..cmd_len],
        .vertices = vtx_ptr[0..vtx_len],
    });
}
export fn drawLine(layer: u32, x0: f32, y0: f32, x1: f32, y1: f32) void {
    canvas.drawLine(layer, x0, y0, x1, y1);
}
export fn drawLineShadow(layer: u32, x0: f32, y0: f32, x1: f32, y1: f32) void {
    canvas.drawLineShadow(layer, x0, y0, x1, y1);
}
export fn drawRect(layer: u32, x: f32, y: f32, w: f32, h: f32) void {
    canvas.drawRect(layer, x, y, w, h);
}
export fn drawRoundedRect(layer: u32, x: f32, y: f32, w: f32, h: f32, cr: f32) void {
    canvas.drawRoundedRect(layer, x, y, w, h, cr);
}
export fn drawSolidRect(layer: u32, x: f32, y: f32, w: f32, h: f32) void {
    canvas.drawSolidRect(layer, x, y, w, h);
}
export fn drawSolidRoundedRect(layer: u32, x: f32, y: f32, w: f32, h: f32, cr: f32) void {
    canvas.drawSolidRoundedRect(layer, x, y, w, h, cr);
}
export fn drawCircle(layer: u32, x: f32, y: f32, r: f32) void {
    canvas.drawCircle(layer, x, y, r);
}
export fn drawSolidCircle(layer: u32, x: f32, y: f32, r: f32) void {
    canvas.drawSolidCircle(layer, x, y, r);
}
export fn drawCircleShadow(layer: u32, x: f32, y: f32, r: f32) void {
    canvas.drawCircleShadow(layer, x, y, r, 0.5);
}
export fn drawEllipse(layer: u32, x: f32, y: f32, width: f32, height: f32, rot: f32) void {
    canvas.drawEllipse(layer, x, y, width, height, rot);
}
export fn drawSolidEllipse(layer: u32, x: f32, y: f32, width: f32, height: f32, rot: f32) void {
    canvas.drawSolidEllipse(layer, x, y, width, height, rot);
}
export fn drawEllipseShadow(layer: u32, x: f32, y: f32, width: f32, height: f32, rot: f32) void {
    canvas.drawEllipseShadow(layer, x, y, width, height, rot, 0.5);
}
export fn drawText(layer: u32, x: f32, y: f32, t_ptr: [*]const u8, t_len: u32, h_a: u32, v_a: u32, font: u32, rotation: f32) void {
    _ = font;
    const pos = canvas.transform.transform(math.vec2f(x, y));

    canvas.drawText(layer, pos.x(), pos.y(), t_ptr[0..t_len], .{
        .color = canvas.fill,
        .h_align = switch (h_a) {
            1 => .left,
            2 => .center,
            3 => .right,
            else => .left,
        },
        .v_align = switch (v_a) {
            1 => .top,
            2 => .center,
            3 => .bottom,
            else => .top,
        },
        // .pivot_h_align = .center,
        // .pivot_v_align = .center,
        .rotation = rotation,
    }) catch |err| {
        dino.log.err(.api, "drawText error: {}", .{err});
    };
}
export fn measureText(t_ptr: [*]const u8, t_len: u32, font: u32) [*c]f32 {
    _ = font;
    var buf = allocArena32(2);
    const size = canvas.measureText(t_ptr[0..t_len]);
    buf[0] = size.x();
    buf[1] = size.y();
    return buf;
}
export fn uploadImage(pixel_ptr: [*]const u8, pixel_len: u32, w: u32, h: u32) u32 {
    return canvas.loadImageFromPixels(w, h, pixel_ptr[0..pixel_len]);
}
export fn destroyCanvasImage(id: u32) void {
    canvas.destroyImage(id);
}
export fn drawImage(layer: u32, id: u32, x: f32, y: f32, w: f32, h: f32) void {
    canvas.drawImage(layer, id, x, y, w, h);
}

// tree

export fn setCamera(x: f32, y: f32, zoom: f32) void {
    state.camera.offset = math.vec2f(x, y);
    state.camera.zoom = zoom;
}

export fn getPathBBoxEx(id: u32) [*c]f32 {
    var buf = allocArena32(4);
    if (state.tree.paths.getBBox(id)) |bbox| {
        buf[0] = bbox.left();
        buf[1] = bbox.top();
        buf[2] = bbox.width();
        buf[3] = bbox.height();
    } else {
        buf[0] = 0.0;
        buf[1] = 0.0;
        buf[2] = 0.0;
        buf[3] = 0.0;
    }
    return buf;
}

export fn getTextPathBBoxEx(node_rid: u32) [*c]f32 {
    var buf = allocArena32(4);
    buf[0] = 0.0;
    buf[1] = 0.0;
    buf[2] = 0.0;
    buf[3] = 0.0;
    if (state.tree.text_docs.getDataPtr(node_rid)) |doc| {
        if (doc.docBBox()) |bbox| {
            buf[0] = bbox.left();
            buf[1] = bbox.top();
            buf[2] = bbox.width();
            buf[3] = bbox.height();
        }
    }
    return buf;
}

export fn getTextLineLength(node_rid: u32) u32 {
    const node_ptr = state.tree.nodes.getNodePtr(node_rid).?;
    if (state.tree.text_docs.getDataPtr(node_ptr.data.text.doc_id)) |doc| {
        var total_lines: u32 = 0;
        for (0..doc.blockCount()) |block_index| {
            total_lines += @intCast(doc.lineCount(block_index));
        }
        return total_lines;
    }
    return 0;
}

export fn getTextBaseline(node_rid: u32) [*c]f32 {
    const node_ptr = state.tree.nodes.getNodePtr(node_rid).?;
    const mat = if (state.tree.nodes.getTransformPtr(node_rid)) |transform| transform.world orelse math.mat2df_identity() else math.mat2df_identity();
    const line_count = getTextLineLength(node_rid);
    if (state.tree.text_docs.getDataPtr(node_ptr.data.text.doc_id)) |doc| {
        var buf = allocArena32(line_count * 4);
        var idx: usize = 0;
        for (0..doc.blockCount()) |block_index| {
            const blockOffset = doc.blockOffset(block_index);
            for (0..doc.lineCount(block_index)) |line_index| {
                const baseline = doc.baseline(block_index, line_index);
                const p1 = mat.transform(baseline.?.p1());
                const p2 = mat.transform(baseline.?.p2());
                buf[idx] = p1.x() + blockOffset.x();
                buf[idx + 1] = p1.y() + blockOffset.y();
                buf[idx + 2] = p2.x() + blockOffset.x();
                buf[idx + 3] = p2.y() + blockOffset.y();
                idx += 4;
            }
        }

        return buf;
    }

    var buf = allocArena32(4);
    buf[0] = 0;
    buf[1] = 0;
    buf[2] = 0;
    buf[3] = 0;

    return buf;
}

export fn getStrokeBBox(path_id: u32, stroke_id: u32) [*c]f32 {
    var buf = allocArena32(4);
    buf[0] = 0.0;
    buf[1] = 0.0;
    buf[2] = 0.0;
    buf[3] = 0.0;
    if (state.tree.paths.getDataPtr(path_id)) |path_data| {
        if (state.tree.strokes.getDataPtr(stroke_id)) |stroke_data| {
            const stroke_path = state.stroke_path_cache.getOrPut(.{
                .path = path_id,
                .stroke = stroke_id,
            }) catch {
                return buf;
            };
            if (!stroke_path.found_existing) {
                // TODO: rect/oval fast path rendering
                var path: Path = undefined;
                switch (path_data.*) {
                    .rect => |rect| {
                        var pb = Path.Builder.init(arena.allocator());
                        pb.addRect(rect.left(), rect.top(), rect.width(), rect.height()) catch return buf;
                        path = pb.toOwnedPath() catch return buf;
                    },
                    .oval => |oval| {
                        var pb = Path.Builder.init(arena.allocator());
                        pb.addOval(oval.left(), oval.top(), oval.width(), oval.height()) catch return buf;
                        path = pb.toOwnedPath() catch return buf;
                    },
                    .path => |p| path = p,
                }

                const p = stroker.strokePath(dino.galloc, path, stroke_data.*);
                stroke_path.value_ptr.* = .{
                    .path = .{ .path = p },
                    .mesh = null,
                };
            }

            if (stroke_path.value_ptr.fetchMesh()) |mesh| {
                const bbox = mesh.bbox;
                buf[0] = bbox.left();
                buf[1] = bbox.top();
                buf[2] = bbox.width();
                buf[3] = bbox.height();
            }
        }
    }
    return buf;
}

export fn setBackgroundColor(r: f32, g: f32, b: f32, a: f32) void {
    state.bg_color = col.Color.init(r, g, b, a);
}

export fn numNodesRender() u32 {
    return state.num_nodes_render;
}
export fn numTilesRender() u32 {
    return state.num_tiles_render;
}

export fn setWindowSize(w: u32, h: u32) void {
    app.setWindowSize(@intCast(w), @intCast(h));
}

export fn getCanvasWidth() i32 {
    return app.fbWidth();
}

export fn getCanvasHeight() i32 {
    return app.fbHeight();
}

export fn pauseApp() void {
    state.app_mode = .pausing;
}

export fn resumeApp() void {
    state.app_mode = .loaded;
}
