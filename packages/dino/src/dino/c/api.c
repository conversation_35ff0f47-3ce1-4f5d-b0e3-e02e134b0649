#include <emscripten/emscripten.h>

typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
typedef signed int i32;
typedef float f32;
typedef int b32;

// memory

extern u8* allocPerm(unsigned int size);
extern f32* allocPerm32(unsigned int size);
extern void freePerm(u8* ptr, unsigned int size);
extern u8* allocArena(unsigned int size);
extern f32* allocArena32(unsigned int size);
EMSCRIPTEN_KEEPALIVE
u8* ph_allocPerm(unsigned int size) {
    return allocPerm(size);
}
EMSCRIPTEN_KEEPALIVE
f32* ph_allocPerm32(unsigned int size) {
    return allocPerm32(size);
}
EMSCRIPTEN_KEEPALIVE
u8* ph_allocArena(unsigned int size) {
    return allocArena(size);
}
EMSCRIPTEN_KEEPALIVE
f32* ph_allocArena32(unsigned int size) {
    return allocArena32(size);
}
EMSCRIPTEN_KEEPALIVE
void ph_freePerm(u8* ptr, unsigned int size) {
    freePerm(ptr, size);
}

// overlay

extern void setTransform(float a, float b, float c, float d, float e, float f);
EMSCRIPTEN_KEEPALIVE
void ph_setTransform(float a, float b, float c, float d, float e, float f) {
    setTransform(a, b, c, d, e, f);
}
extern void fillStyle(float r, float g, float b, float a);
EMSCRIPTEN_KEEPALIVE
void ph_fillStyle(float r, float g, float b, float a) {
    fillStyle(r, g, b, a);
}
extern void strokeStyle(float w, float r, float g, float b, float a);
EMSCRIPTEN_KEEPALIVE
void ph_strokeStyle(float w, float r, float g, float b, float a) {
    strokeStyle(w, r, g, b, a);
}
extern void drawPath(int layer, float x, float y, u8* cmd, int cmd_len, f32* vtx, int vtx_len);
EMSCRIPTEN_KEEPALIVE
void ph_drawPathEx(int layer, float x, float y, u8* cmd, int cmd_len, f32* vtx, int vtx_len) {
    drawPath(layer, x, y, cmd, cmd_len, vtx, vtx_len);
}
extern void drawLine(int layer, float x1, float y1, float x2, float y2);
EMSCRIPTEN_KEEPALIVE
void ph_drawLine(int layer, float x1, float y1, float x2, float y2) {
    drawLine(layer, x1, y1, x2, y2);
}
extern void drawLineShadow(int layer, float x1, float y1, float x2, float y2);
EMSCRIPTEN_KEEPALIVE
void ph_drawLineShadow(int layer, float x1, float y1, float x2, float y2) {
    drawLineShadow(layer, x1, y1, x2, y2);
}
extern void drawRect(int layer, float x, float y, float w, float h);
EMSCRIPTEN_KEEPALIVE
void ph_drawRect(int layer, float x, float y, float w, float h) {
    drawRect(layer, x, y, w, h);
}
extern void drawRoundedRect(int layer, float x, float y, float w, float h, float cr);
EMSCRIPTEN_KEEPALIVE
void ph_drawRoundedRect(int layer, float x, float y, float w, float h, float cr) {
    drawRoundedRect(layer, x, y, w, h, cr);
}
extern void drawSolidRect(int layer, float x, float y, float w, float h);
EMSCRIPTEN_KEEPALIVE
void ph_drawSolidRect(int layer, float x, float y, float w, float h) {
    drawSolidRect(layer, x, y, w, h);
}
extern void drawSolidRoundedRect(int layer, float x, float y, float w, float h, float cr);
EMSCRIPTEN_KEEPALIVE
void ph_drawSolidRoundedRect(int layer, float x, float y, float w, float h, float cr) {
    drawSolidRoundedRect(layer, x, y, w, h, cr);
}
extern void drawCircle(int layer, float x, float y, float r);
EMSCRIPTEN_KEEPALIVE
void ph_drawCircle(int layer, float x, float y, float r) {
    drawCircle(layer, x, y, r);
}
extern void drawSolidCircle(int layer, float x, float y, float r);
EMSCRIPTEN_KEEPALIVE
void ph_drawSolidCircle(int layer, float x, float y, float r) {
    drawSolidCircle(layer, x, y, r);
}
extern void drawCircleShadow(int layer, float x, float y, float r);
EMSCRIPTEN_KEEPALIVE
void ph_drawCircleShadow(int layer, float x, float y, float r) {
    drawCircleShadow(layer, x, y, r);
}
extern void drawEllipse(int layer, float x, float y, float width, float height, float rot);
EMSCRIPTEN_KEEPALIVE
void ph_drawEllipse(int layer, float x, float y, float width, float height, float rot) {
    drawEllipse(layer, x, y, width, height, rot);
}
extern void drawSolidEllipse(int layer, float x, float y, float width, float height, float rot);
EMSCRIPTEN_KEEPALIVE
void ph_drawSolidEllipse(int layer, float x, float y, float width, float height, float rot) {
    drawSolidEllipse(layer, x, y, width, height, rot);
}
extern void drawEllipseShadow(int layer, float x, float y, float width, float height, float rot);
EMSCRIPTEN_KEEPALIVE
void ph_drawEllipseShadow(int layer, float x, float y, float width, float height, float rot) {
    drawEllipseShadow(layer, x, y, width, height, rot);
}
extern void drawText(int layer, float x, float y, u8* text, int text_len, int h_align, int v_align, u32 font, f32 rotation);
EMSCRIPTEN_KEEPALIVE
void ph_drawTextEx(int layer, float x, float y, u8* text, int text_len, int h_align, int v_align, u32 font, f32 rotation) {
    drawText(layer, x, y, text, text_len, h_align, v_align, font, rotation);
}
extern f32* measureText(u8* text, int text_len, u32 font);
EMSCRIPTEN_KEEPALIVE
f32* ph_measureTextEx(u8* text, int text_len, u32 font) {
    return measureText(text, text_len, font);
}
extern int uploadImage(u8* data, int data_len, int w, int h);
EMSCRIPTEN_KEEPALIVE
int ph_uploadImageEx(u8* data, int data_len, int w, int h) {
    return uploadImage(data, data_len, w, h);
}
extern void destroyCanvasImage(u32 id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyCanvasImage(u32 id) {
    destroyCanvasImage(id);
}
extern void drawImage(int layer, int id, float x, float y, float w, float h);
EMSCRIPTEN_KEEPALIVE
void ph_drawImage(int layer, int id, float x, float y, float w, float h) {
    drawImage(layer, id, x, y, w, h);
}

// tree

extern void setCamera(float x, float y, float zoom);
EMSCRIPTEN_KEEPALIVE
void ph_setCamera(float x, float y, float zoom) {
    setCamera(x, y, zoom);
}

extern f32* getPathBBoxEx(u32 id);
EMSCRIPTEN_KEEPALIVE
f32* ph_getPathBBoxEx(u32 id) {
    return getPathBBoxEx(id);
}

extern f32* getTextPathBBoxEx(u32 node_rid);
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextPathBBoxEx(u32 node_rid) {
    return getTextPathBBoxEx(node_rid);
}

extern u32 getTextLineLength(u32 id);
EMSCRIPTEN_KEEPALIVE
u32 ph_getTextLineLength(u32 id) {
    return getTextLineLength(id);
}

extern f32* getTextBaseline(u32 id);
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextBaseline(u32 id) {
    return getTextBaseline(id);
}

extern f32* getTextSelectionBlockEx(u32 doc_id);
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextSelectionBlockEx(u32 doc_id) {
    return getTextSelectionBlockEx(doc_id);
}

extern u32 getTextEditingNodeIdEx();
EMSCRIPTEN_KEEPALIVE
u32 ph_getTextEditingNodeIdEx() {
    return getTextEditingNodeIdEx();
}

extern f32* getTextCaretPosEx();
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextCaretPosEx() {
    return getTextCaretPosEx();
}

extern f32* getStrokeBBox(u32 path_id, u32 stroke_id);
EMSCRIPTEN_KEEPALIVE
f32* ph_getStrokeBBox(u32 path_id, u32 stroke_id) {
    return getStrokeBBox(path_id, stroke_id);
}

extern b32 isPointInPath(u32 path_id, f32 x, f32 y);
EMSCRIPTEN_KEEPALIVE
b32 ph_isPointInPath(u32 path_id, f32 x, f32 y) {
    return isPointInPath(path_id, x, y);
}

extern b32 isPointInStroke(u32 path_id, u32 stroke_id, f32 x, f32 y);
EMSCRIPTEN_KEEPALIVE
b32 ph_isPointInStroke(u32 path_id, u32 stroke_id, f32 x, f32 y) {
    return isPointInStroke(path_id, stroke_id, x, y);
}

extern b32 isPointInText(u32 node_id, f32 x, f32 y);
EMSCRIPTEN_KEEPALIVE
b32 ph_isPointInText(u32 node_id, f32 x, f32 y) {
    return isPointInText(node_id, x, y);
}

extern void markNodeChanged(u32 node_id);
EMSCRIPTEN_KEEPALIVE
void ph_markNodeChanged(u32 node_id) {
    return markNodeChanged(node_id);
}

extern u32 makeNodeEx(u8 type);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeNode(u8 type) {
    return makeNodeEx(type);
}
extern void destroyNode(u32 id, bool include_children);
EMSCRIPTEN_KEEPALIVE
void ph_destroyNode(u32 id, bool include_children) {
    return destroyNode(id, include_children);
}
extern void addNodeChild(u32 parent, u32 child);
EMSCRIPTEN_KEEPALIVE
void ph_addNodeChild(u32 parent, u32 child) {
    return addNodeChild(parent, child);
}
extern u32 getNodeChildCount(u32 parent);
EMSCRIPTEN_KEEPALIVE
u32 ph_getNodeChildCount(u32 parent) {
    return getNodeChildCount(parent);
}
extern void insertNodeChild(u32 parent, u32 child, u32 index);
EMSCRIPTEN_KEEPALIVE
void ph_insertNodeChild(u32 parent, u32 child, u32 index) {
    return insertNodeChild(parent, child, index);
}
extern void addNodeToRoot(u32 node_id);
EMSCRIPTEN_KEEPALIVE
void ph_addNodeToRoot(u32 node_id) {
    return addNodeToRoot(node_id);
}
extern void reorderNode(u32 node_id, u32 index);
EMSCRIPTEN_KEEPALIVE
void ph_reorderNode(u32 node_id, u32 index) {
    return reorderNode(node_id, index);
}
extern void removeNode(u32 node_id);
EMSCRIPTEN_KEEPALIVE
void ph_removeNode(u32 node_id) {
    return removeNode(node_id);
}

extern void setNodeTransform(u32 node_id, f32 x, f32 y, f32 r, f32 sx, f32 sy, f32 sk_x, f32 sk_y);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeTransform(u32 node_id, f32 x, f32 y, f32 r, f32 sx, f32 sy, f32 sk_x, f32 sk_y) {
    return setNodeTransform(node_id, x, y, r, sx, sy, sk_x, sk_y);
}
extern void setNodeVisible(u32 node_id, b32 visible);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeVisible(u32 node_id, b32 visible) {
    return setNodeVisible(node_id, visible);
}
extern void setNodeOpacity(u32 node_id, f32 opacity);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeOpacity(u32 node_id, f32 opacity) {
    return setNodeOpacity(node_id, opacity);
}
extern void setNodeBlendEx(u32 node_id, u8 blend);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeBlend(u32 node_id, u8 blend) {
    return setNodeBlendEx(node_id, blend);
}

extern void setNodeCompose(u32 node_id, u32 idx, u32 comp_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeCompose(u32 node_id, u32 idx, u32 comp_id) {
    return setNodeCompose(node_id, idx, comp_id);
}

extern void removeNodeCompose(u32 node_id, u32 idx);
EMSCRIPTEN_KEEPALIVE
void ph_removeNodeCompose(u32 node_id, u32 idx) {
    return removeNodeCompose(node_id, idx);
}

extern void destroyNodeCompose(u32 node_id, u32 idx);
EMSCRIPTEN_KEEPALIVE
void ph_destroyNodeCompose(u32 node_id, u32 idx) {
    return destroyNodeCompose(node_id, idx);
}

extern void setNodePath(u32 node_id, u32 path_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodePath(u32 node_id, u32 path_id) {
    return setNodePath(node_id, path_id);
}

extern void enterTextEditingMode(u32 node_id);
EMSCRIPTEN_KEEPALIVE
void ph_enterTextEditingMode(u32 node_id) {
    return enterTextEditingMode(node_id);
}

extern void changeTextEditingNode(u32 node_id, b32 tabbing);
EMSCRIPTEN_KEEPALIVE
void ph_changeTextEditingNode(u32 node_id, b32 tabbing) {
    return changeTextEditingNode(node_id, tabbing);
}

extern void exitTextEditingMode();
EMSCRIPTEN_KEEPALIVE
void ph_exitTextEditingMode() {
    return exitTextEditingMode();
}

extern void setForceDefaultCursor(b32 force);
EMSCRIPTEN_KEEPALIVE
void ph_setForceDefaultCursor(b32 force) {
    return setForceDefaultCursor(force);
}

extern void setTextSelection(u32 doc_id, u32 start_block, u32 start_char, u32 end_block, u32 end_char, u32 cursor_index, u32 active_block_index);
EMSCRIPTEN_KEEPALIVE
void ph_setTextSelection(u32 doc_id, u32 start_block, u32 start_char, u32 end_block, u32 end_char, u32 cursor_index, u32 active_block_index) {
    return setTextSelection(doc_id, start_block, start_char, end_block, end_char, cursor_index, active_block_index);
}

extern void updatePageOffset(f32 x, f32 y);
EMSCRIPTEN_KEEPALIVE
void ph_updatePageOffset(f32 x, f32 y) {
    return updatePageOffset(x, y);
}

extern void setNodeTextStyle(u32 node_id, u32 style_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeTextStyle(u32 node_id, u32 style_id) {
    return setNodeTextStyle(node_id, style_id);
}

extern void setNodeFillPaintEx(u32 node_id, u8 tag, u32 paint_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeFillPaint(u32 node_id, u8 tag, u32 paint_id) {
    return setNodeFillPaintEx(node_id, tag, paint_id);
}

extern void setNodeFillOpacity(u32 node_id, f32 opacity);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeFillOpacity(u32 node_id, f32 opacity) {
    return setNodeFillOpacity(node_id, opacity);
}

extern void setNodeStrokePaintEx(u32 node_id, u8 tag, u32 paint_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeStrokePaint(u32 node_id, u8 tag, u32 paint_id) {
    return setNodeStrokePaintEx(node_id, tag, paint_id);
}

extern void setNodeStrokeOpacity(u32 node_id, f32 opacity);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeStrokeOpacity(u32 node_id, f32 opacity) {
    return setNodeStrokeOpacity(node_id, opacity);
}

extern void setNodeStrokeData(u32 node_id, u32 stroke_id);
EMSCRIPTEN_KEEPALIVE
void ph_setNodeStrokeData(u32 node_id, u32 stroke_id) {
    return setNodeStrokeData(node_id, stroke_id);
}

extern void setAssetsPathEx(u8* ptr, u32 len);
EMSCRIPTEN_KEEPALIVE
void ph_setAssetsPathEx(u8* ptr, u32 len) {
    setAssetsPathEx(ptr, len);
}

extern u32 makePathEx(u8* cmd_ptr, u32 cmd_len, f32* vtx_ptr, u32 vtx_len);
EMSCRIPTEN_KEEPALIVE
u32 ph_makePathEx(u8* cmd_ptr, u32 cmd_len, f32* vtx_ptr, u32 vtx_len) {
    return makePathEx(cmd_ptr, cmd_len, vtx_ptr, vtx_len);
}
extern u32 makeEmptyPath();
EMSCRIPTEN_KEEPALIVE
u32 ph_makeEmptyPath() {
    return makeEmptyPath();
}
extern void setPathEx(u32 path_id, u8* cmd_ptr, u32 cmd_len, f32* vtx_ptr, u32 vtx_len);
EMSCRIPTEN_KEEPALIVE
void ph_setPathEx(u32 path_id, u8* cmd_ptr, u32 cmd_len, f32* vtx_ptr, u32 vtx_len) {
    setPathEx(path_id, cmd_ptr, cmd_len, vtx_ptr, vtx_len);
}
extern void setEmptyPath(u32 path_id);
EMSCRIPTEN_KEEPALIVE
void ph_setEmptyPath(u32 path_id) {
    setEmptyPath(path_id);
}
extern void destroyPath(u32 path_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyPath(u32 path_id) {
    destroyPath(path_id);
}

extern u32 getTextDocIDEx(u32 node_id);
EMSCRIPTEN_KEEPALIVE
u32 ph_getTextDocIDEx(u32 node_id) {
    return getTextDocIDEx(node_id);
}
extern void setTextDocEx(u32 doc_id, u8* content_ptr, u32 content_len, u32 cursor_index, u32 active_block_index);
EMSCRIPTEN_KEEPALIVE
void ph_setTextDocEx(u32 doc_id, u8* content_ptr, u32 content_len, u32 cursor_index, u32 active_block_index) {
    setTextDocEx(doc_id, content_ptr, content_len, cursor_index, active_block_index);
}
extern void destroyTextDoc(u32 doc_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyTextDoc(u32 doc_id) {
    destroyTextDoc(doc_id);
}

extern void destoryTextPath(u32 doc_id);
EMSCRIPTEN_KEEPALIVE
void ph_destoryTextPath(u32 doc_id) {
    destoryTextPath(doc_id);
}

extern void fetchFontEx(u8* font_family_ptr, u32 font_family_len, u8* url_ptr, u32 url_len, u8 font_style, u16 font_weight);
EMSCRIPTEN_KEEPALIVE
void ph_fetchFontEx(u8* font_family_ptr, u32 font_family_len, u8* url_ptr, u32 url_len, u8 font_style, u16 font_weight) {
    fetchFontEx(font_family_ptr, font_family_len, url_ptr, url_len, font_style, font_weight);
}

extern void setTextStyleEx(u32 doc_id, u8* font_family_ptr, u32 font_family_len, u8 font_style, u16 font_weight, f32 font_size, f32 letter_spacing, f32 line_spacing, f32 paragraph_spacing, u8 horizontal_alignment, u8 vertical_alignment);
EMSCRIPTEN_KEEPALIVE
void ph_setTextStyleEx(u32 doc_id, u8* font_family_ptr, u32 font_family_len, u8 font_style, u16 font_weight, f32 font_size, f32 letter_spacing, f32 line_spacing, f32 paragraph_spacing, u8 horizontal_alignment, u8 vertical_alignment) {
    setTextStyleEx(doc_id, font_family_ptr, font_family_len, font_style, font_weight, font_size, letter_spacing, line_spacing, paragraph_spacing, horizontal_alignment, vertical_alignment);
}

extern void buildLayoutAndPathEx(u32 node_id, u32 doc_id, f32 max_width, f32 max_height);
EMSCRIPTEN_KEEPALIVE
void ph_buildLayoutAndPathEx(u32 node_id, u32 doc_id, f32 max_width, f32 max_height) {
    buildLayoutAndPathEx(node_id, doc_id, max_width, max_height);
}

extern f32* getTextOffsetEx(u32 node_rid);
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextOffsetEx(u32 node_rid) {
    return getTextOffsetEx(node_rid);
}
extern f32* getTextLayoutBoxEx(u32 node_rid);
EMSCRIPTEN_KEEPALIVE
f32* ph_getTextLayoutBoxEx(u32 node_rid) {
    return getTextLayoutBoxEx(node_rid);
}

extern u32 makeColorEx(f32 r, f32 g, f32 b, f32 a);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeColor(f32 r, f32 g, f32 b, f32 a) {
    return makeColorEx(r, g, b, a);
}
extern void setColorEx(u32 id, f32 r, f32 g, f32 b, f32 a);
EMSCRIPTEN_KEEPALIVE
void ph_setColor(u32 id, f32 r, f32 g, f32 b, f32 a) {
    return setColorEx(id, r, g, b, a);
}
extern void destroyColor(u32 id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyColor(u32 id) {
    return destroyColor(id);
}

extern u32 makeGradientEx(u8 type, f32 a, f32 b, f32 c, f32 d, f32 e, f32 f);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeGradientEx(u8 type, f32 a, f32 b, f32 c, f32 d, f32 e, f32 f) {
    return makeGradientEx(type, a, b, c, d, e, f);
}

extern void setGradientMatrixEx(u32 gradient_id, f32 a, f32 b, f32 c, f32 d, f32 e, f32 f);
EMSCRIPTEN_KEEPALIVE
void ph_setGradientMatrixEx(u32 gradient_id, f32 a, f32 b, f32 c, f32 d, f32 e, f32 f) {
    return setGradientMatrixEx(gradient_id, a, b, c, d, e, f);
}

extern void setGradientTag(u32 gradient_id, u8 tag);
EMSCRIPTEN_KEEPALIVE
void ph_setGradientTag(u32 gradient_id, u8 tag) {
    return setGradientTag(gradient_id, tag);
}

extern void setGradientStopLen(u32 gradient_id, u32 len);
EMSCRIPTEN_KEEPALIVE
void ph_setGradientStopLen(u32 gradient_id, u32 len) {
    return setGradientStopLen(gradient_id, len);
}

extern void setGradientStopEx(u32 gradient_id, u32 idx, f32 pos, f32 r, f32 g, f32 b, f32 a);
EMSCRIPTEN_KEEPALIVE
void ph_setGradientStop(u32 gradient_id, u32 idx, f32 pos, f32 r, f32 g, f32 b, f32 a) {
    return setGradientStopEx(gradient_id, idx, pos, r, g, b, a);
}

extern void updateGradientPixels(u32 gradient_id);
EMSCRIPTEN_KEEPALIVE
void ph_updateGradientPixels(u32 gradient_id) {
    return updateGradientPixels(gradient_id);
}

extern void destroyGradient(u32 gradient_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyGradient(u32 gradient_id) {
    return destroyGradient(gradient_id);
}

extern u32 makeStroke(f32 width, u8 cap, u8 join, f32 miter_limit, f32 dash, f32 gap);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeStroke(f32 width, u8 cap, u8 join, f32 miter_limit, f32 dash, f32 gap) {
    return makeStroke(width, cap, join, miter_limit, dash, gap);
}

extern void destroyStrokePathCacheEx(u32 node_id, u32 path_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyStrokePathCacheEx(u32 node_id, u32 path_id) {
    return destroyStrokePathCacheEx(node_id, path_id);
}

extern void setStroke(u32 stroke_id, f32 width, u8 cap, u8 join, f32 miter_limit, f32 dash, f32 gap);
EMSCRIPTEN_KEEPALIVE
void ph_setStroke(u32 stroke_id, f32 width, u8 cap, u8 join, f32 miter_limit, f32 dash, f32 gap) {
    return setStroke(stroke_id, width, cap, join, miter_limit, dash, gap);
}

extern void destroyStroke(u32 stroke_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyStroke(u32 stroke_id) {
    return destroyStroke(stroke_id);
}

extern u32* getTextPathEx(u32 text_doc_id);
EMSCRIPTEN_KEEPALIVE
u32* ph_getTextPathEx(u32 text_doc_id) {
    return getTextPathEx(text_doc_id);
}

extern u32* getPathCmdEx(u32 path_id);
EMSCRIPTEN_KEEPALIVE
u32* ph_getPathCmdEx(u32 path_id) {
    return getPathCmdEx(path_id);
}

extern u32* getPathVtxEx(u32 path_id);
EMSCRIPTEN_KEEPALIVE
u32* ph_getPathVtxEx(u32 path_id) {
    return getPathVtxEx(path_id);
}

extern u32* getStrokePathCmdEx(u32 path_id, u32 stroke_id);
EMSCRIPTEN_KEEPALIVE
u32* ph_getStrokePathCmdEx(u32 path_id, u32 stroke_id) {
    return getStrokePathCmdEx(path_id, stroke_id);
}

extern u32* getStrokePathVtxEx(u32 path_id, u32 stroke_id);
EMSCRIPTEN_KEEPALIVE
u32* ph_getStrokePathVtxEx(u32 path_id, u32 stroke_id) {
    return getStrokePathVtxEx(path_id, stroke_id);
}

extern u32 allocImage();
EMSCRIPTEN_KEEPALIVE
u32 ph_allocImage() {
    return allocImage();
}

extern u32 makeImageWithURLEx(u8* url, u32 url_len);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeImageWithURLEx(u8* url, u32 url_len) {
    return makeImageWithURLEx(url, url_len);
}

extern u32 makeImageWithFileDataEx(u8* data, u32 data_len);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeImageWithFileDataEx(u8* data, u32 data_len) {
    return makeImageWithFileDataEx(data, data_len);
}

extern u32 makeImageWithPixelsEx(u8* data, u32 data_len, u32 w, u32 h);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeImageWithPixelsEx(u8* data, u32 data_len, u32 w, u32 h) {
    return makeImageWithPixelsEx(data, data_len, w, h);
}

extern void setImageWithURLEx(u32 img_id, u8* url, u32 url_len);
EMSCRIPTEN_KEEPALIVE
void ph_setImageWithURLEx(u32 img_id, u8* url, u32 url_len) {
    setImageWithURLEx(img_id, url, url_len);
}

extern void setImageWithFileDataEx(u32 img_id, u8* data, u32 data_len);
EMSCRIPTEN_KEEPALIVE
void ph_setImageWithFileDataEx(u32 img_id, u8* data, u32 data_len) {
    setImageWithFileDataEx(img_id, data, data_len);
}

extern void setImageWithPixelsEx(u32 img_id, u8* data, u32 data_len, u32 w, u32 h);
EMSCRIPTEN_KEEPALIVE
void ph_setImageWithPixelsEx(u32 img_id, u8* data, u32 data_len, u32 w, u32 h) {
    setImageWithPixelsEx(img_id, data, data_len, w, h);
}

extern void setImageFillModeEx(u32 img_id, u8 mode);
EMSCRIPTEN_KEEPALIVE
void ph_setImageFillMode(u32 img_id, u8 mode) {
    setImageFillModeEx(img_id, mode);
}

extern void destroyImage(u32 img_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyImage(u32 img_id) {
    destroyImage(img_id);
}

extern u32 makeCompose(u8 tag, u32 root_id);
EMSCRIPTEN_KEEPALIVE
u32 ph_makeCompose(u8 tag, u32 root_id) {
    return makeCompose(tag, root_id);
}

extern void setComposeRoot(u32 comp_id, u32 root_id);
EMSCRIPTEN_KEEPALIVE
void ph_setComposeRoot(u32 comp_id, u32 root_id) {
    setComposeRoot(comp_id, root_id);
}

extern void destroyCompose(u32 comp_id);
EMSCRIPTEN_KEEPALIVE
void ph_destroyCompose(u32 comp_id) {
    destroyCompose(comp_id);
}

extern void setBackgroundColor(f32 r, f32 g, f32 b, f32 a);
EMSCRIPTEN_KEEPALIVE
void ph_setBackgroundColor(f32 r, f32 g, f32 b, f32 a) {
    setBackgroundColor(r, g, b, a);
}

extern u32 numNodesRender();
EMSCRIPTEN_KEEPALIVE
u32 ph_numNodesRender() {
    return numNodesRender();
}
extern u32 numTilesRender();
EMSCRIPTEN_KEEPALIVE
u32 ph_numTilesRender() {
    return numTilesRender();
}

extern void purge();
EMSCRIPTEN_KEEPALIVE
void ph_purge() {
    purge();
}

extern void setWindowSize(u32 w, u32 h);
EMSCRIPTEN_KEEPALIVE
void ph_setWindowSize(u32 w, u32 h) {
    setWindowSize(w, h);
}

extern i32 getCanvasWidth();
EMSCRIPTEN_KEEPALIVE
i32 ph_getCanvasWidth() {
    return getCanvasWidth();
}

extern i32 getCanvasHeight();
EMSCRIPTEN_KEEPALIVE
i32 ph_getCanvasHeight() {
    return getCanvasHeight();
}

extern void setCaptureBackgroundColor(f32 r, f32 g, f32 b, f32 a);
EMSCRIPTEN_KEEPALIVE
void ph_setCaptureBackgroundColor(f32 r, f32 g, f32 b, f32 a) {
    setCaptureBackgroundColor(r, g, b, a);
}
extern void capture(u32 node_id, f32 clip_x, f32 clip_y, f32 clip_w, f32 clip_h, u32 w, u32 h);
EMSCRIPTEN_KEEPALIVE
void ph_capture(u32 node_id, f32 clip_x, f32 clip_y, f32 clip_w, f32 clip_h, u32 w, u32 h) {
    capture(node_id, clip_x, clip_y, clip_w, clip_h, w, h);
}

extern void pauseApp();
EMSCRIPTEN_KEEPALIVE
void ph_pauseApp() {
    pauseApp();
}

extern void resumeApp();
EMSCRIPTEN_KEEPALIVE
void ph_resumeApp() {
    resumeApp();
}

extern u32 getNodeStorageCount();
EMSCRIPTEN_KEEPALIVE
u32 ph_getNodeStorageCount() {
    return getNodeStorageCount();
}

extern u32 getColorStorageCount();
EMSCRIPTEN_KEEPALIVE
u32 ph_getColorStorageCount() {
    return getColorStorageCount();
}

extern u32 getPathStorageCount();
EMSCRIPTEN_KEEPALIVE
u32 ph_getPathStorageCount() {
    return getPathStorageCount();
}

extern u32 getStrokeStorageCount();
EMSCRIPTEN_KEEPALIVE
u32 ph_getStrokeStorageCount() {
    return getStrokeStorageCount();
}


