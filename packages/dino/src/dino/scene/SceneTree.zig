const SceneTree = @This();

const std = @import("std");
const dino = @import("../dino.zig");
const Allocator = std.mem.Allocator;

const gfx = @import("../gfx.zig");
const utils = @import("../utils.zig");
const math = @import("../math.zig");
const col = @import("../color.zig");
const image = @import("../image/image.zig");
const Pool = @import("../pool/pool.zig").Pool;
const Path = @import("../Path.zig");
const RIMesh = @import("../RIMesh.zig");
const stroker = @import("../stroker.zig");
const FontDB = @import("../text/FontDB.zig");
const TextDoc = @import("../text/TextDoc.zig");
const NoteDoc = @import("../text/NoteDoc.zig");
const BlockPathList = NoteDoc.BlockPathList;

const TextStyle = TextDoc.Style;

const sd = @import("data.zig");

/// Resource ID type.
pub const ResID = u32;
/// Resource state.
pub const ResState = enum {
    /// default
    initial,
    /// allocated, can get by id
    alloc,
    /// is load in progress
    loading,
    /// ready to use
    valid,
    /// failed to construct
    failed,
};

const ColorStorage = struct {
    const ColorPool = Pool(16, 16, sd.ColorData, struct {
        data: sd.ColorData,
    });

    allocator: Allocator,
    pool: ColorPool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try ColorPool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: col.Color) !ResID {
        const rid = try self.pool.add(.{
            .data = .{
                .rgba = .{ data.r, data.g, data.b, data.a },
            },
        });
        return @bitCast(rid);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: ColorPool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }

    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.ColorData {
        const hdl: ColorPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }

    /// Get color value as RGBA32. `null` if RID not found.
    pub fn getRGBA32(self: @This(), rid: ResID) ?col.Color {
        const hdl: ColorPool.Handle = @bitCast(rid);
        const data: ?*sd.ColorData = self.pool.getColumnPtr(hdl, .data) catch null;
        return if (data) |c| c.toRGBA32() else null;
    }
};

const GradientStorage = struct {
    const rows: u32 = 1024;
    const table_width: u32 = 1024;
    const GradientPool = Pool(16, 16, sd.GradientData, struct {
        data: sd.GradientData,

        idx: u32 = 0,
        pixels: [table_width]u32 = [_]u32{0} ** table_width,
    });

    allocator: Allocator,
    pool: GradientPool,

    img: gfx.Image,
    dirty: bool = false,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try GradientPool.initCapacity(allocator, 1024),
            .img = gfx.makeImage(blk: {
                break :blk gfx.ImageDesc{
                    .usage = .STREAM,
                    .width = table_width,
                    .height = rows,
                };
            }),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: sd.GradientData) !u32 {
        self.dirty = true;
        var pixels: [table_width]u32 = [_]u32{0} ** table_width;
        if (data.stop_len > 0) {
            buildPixels(&pixels, data.stops[0..data.stop_len]);
        }
        return @bitCast(try self.pool.add(.{
            .data = data,
            .pixels = pixels,
        }));
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        self.dirty = true;
        const hdl: GradientPool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }
    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.GradientData {
        const hdl: GradientPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
    pub fn getPos(self: @This(), rid: ResID) !f32 {
        const hdl: GradientPool.Handle = @bitCast(rid);
        const idx_f: f32 = @floatFromInt(try self.pool.getColumn(hdl, .idx));
        return idx_f / rows;
    }
    pub fn getPixels(self: @This(), rid: ResID) ?*[table_width]u32 {
        const hdl: GradientPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .pixels) catch null;
    }
    pub fn updatePixels(self: @This(), rid: ResID) void {
        const hdl: GradientPool.Handle = @bitCast(rid);
        const data = self.pool.getColumnPtr(hdl, .data) catch return;

        var stops = self.allocator.dupe(sd.GradientColorStop, data.stops[0..data.stop_len]) catch return;
        defer self.allocator.free(stops);
        std.sort.insertion(sd.GradientColorStop, stops, {}, comparePos);
        var len: usize = 0;
        for (0..stops.len) |i| {
            if (i == 0) {
                stops[len] = stops[i];
                len += 1;
            } else if (stops[i].pos != stops[len - 1].pos) {
                stops[len] = stops[i];
                len += 1;
            }
        }

        var pixels: [table_width]u32 = [_]u32{0} ** table_width;
        buildPixels(&pixels, stops[0..len]);

        self.pool.setColumn(hdl, .pixels, pixels) catch return;
    }
    fn comparePos(_: void, a: sd.GradientColorStop, b: sd.GradientColorStop) bool {
        return a.pos < b.pos;
    }
    pub fn updateImage(self: *@This()) !void {
        var pixels = [_]u32{0} ** (table_width * rows);
        var iter = self.pool.liveHandles();
        var idx: u32 = 0;
        while (iter.next()) |hdl| : (idx += 1) {
            if (self.getPixels(@bitCast(hdl))) |ps| {
                @memcpy(pixels[(idx * table_width)..(idx * table_width + table_width)], ps[0..table_width]);
                try self.pool.setColumn(hdl, .idx, idx);
            }
        }
        var data: gfx.ImageData = .{};
        data.subimage[0][0] = utils.asRange(pixels[0..]);
        gfx.updateImage(self.img, data);
    }
    /// Color stops need to be sorted by position and have no duplicates!
    fn buildPixels(pixels: *[table_width]u32, stops: []const sd.GradientColorStop) void {
        const widthf: f32 = @floatFromInt(table_width);
        const first = stops[0];
        var curr = first;
        var c = curr.color;

        var index: u32 = 0;
        for (stops) |next| {
            const stop_index: u32 = @intFromFloat(std.math.clamp(next.pos, 0.0, 1.0) * widthf);
            const length: f32 = @floatFromInt(stop_index - index);

            if (length > 0.0) {
                const inc = col.Color.init(
                    (next.color.r - curr.color.r) / length,
                    (next.color.g - curr.color.g) / length,
                    (next.color.b - curr.color.b) / length,
                    (next.color.a - curr.color.a) / length,
                );

                while (index < stop_index) : (index += 1) {
                    pixels[index] = c.toU32();
                    c = c.add(inc);
                }
            }

            // Set last color to final stop color, avoids rounding error
            if (index == table_width) {
                pixels[table_width - 1] = next.color.toU32();
                break;
            }

            curr = next;
        }

        // fill remaining pixels with last color
        const last = stops[stops.len - 1].color.toU32();
        while (index < table_width) : (index += 1) {
            pixels[index] = last;
        }
    }
};

const ImageStorage = struct {
    const ImagePool = Pool(16, 16, sd.ImageData, struct {
        state: ResState = .alloc,
        data: sd.ImageData,
        gfx_image: ?gfx.Image = null,
    });

    allocator: Allocator,
    pool: ImagePool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try ImagePool.initCapacity(allocator, 2048),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        var images = self.pool.liveHandles();
        while (images.next()) |hdl| {
            if (self.pool.getColumnPtr(hdl, .data)) |data_ptr| {
                self.allocator.free(data_ptr.*.data);
            } else |_| {}

            if (self.pool.getColumnPtr(hdl, .gfx_image)) |gfx_ptr| {
                if (gfx_ptr.*) |gfx_img| {
                    gfx.destroyImage(gfx_img);
                    gfx_ptr.* = null;
                }
            } else |_| {}
        }
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: sd.ImageData) !u32 {
        const id = try self.pool.add(.{
            .data = blk: {
                var d_data = data;
                d_data.data = try self.allocator.dupe(u8, data.data);
                break :blk d_data;
            },
        });
        return @bitCast(id);
    }
    pub fn alloc(self: *@This()) !u32 {
        const id = try self.pool.add(.{
            .data = .{},
        });
        return @bitCast(id);
    }
    pub fn initWithData(self: *@This(), rid: ResID, data: sd.ImageData) !void {
        const hdl: ImagePool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .data)) |data_ptr| {
            data_ptr.*.data = try self.allocator.dupe(u8, data.data);
            data_ptr.*.size = data.size;
            data_ptr.*.tag = data.tag;
        } else |_| {}
    }
    pub fn clearData(self: *@This(), rid: ResID) !void {
        const hdl: ImagePool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .data)) |data_ptr| {
            self.allocator.free(data_ptr.*.data);
            data_ptr.*.size = math.vec2f(0.0, 0.0);
            data_ptr.*.tag = .data;
        } else |_| {}
        if (self.pool.getColumnPtr(hdl, .gfx_image)) |gfx_ptr| {
            if (gfx_ptr.*) |gfx_img| {
                gfx.destroyImage(gfx_img);
                gfx_ptr.* = null;
            }
        } else |_| {}
    }
    pub fn setState(self: *@This(), rid: ResID, state: ResState) !void {
        const hdl: ImagePool.Handle = @bitCast(rid);
        try self.pool.setColumn(hdl, .state, state);
    }
    pub fn getState(self: *@This(), rid: ResID) !ResState {
        const hdl: ImagePool.Handle = @bitCast(rid);
        return try self.pool.getColumn(hdl, .state);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: ImagePool.Handle = @bitCast(rid);
        try self.clearData(rid);
        try self.pool.remove(hdl);
    }
    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.ImageData {
        const hdl: ImagePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
    pub fn getGfxImage(self: @This(), rid: ResID) ?gfx.Image {
        const hdl: ImagePool.Handle = @bitCast(rid);
        const ptr = self.pool.getColumnPtr(hdl, .gfx_image) catch {
            return null;
        };
        if (ptr.*) |gfx_img| return gfx_img;

        ptr.* = gfx.makeImage(blk: {
            const data_ptr = self.pool.getColumnPtr(hdl, .data) catch {
                return null;
            };
            var desc: gfx.ImageDesc = .{
                .width = @intFromFloat(data_ptr.*.size.x()),
                .height = @intFromFloat(data_ptr.*.size.y()),
            };
            desc.data.subimage[0][0] = utils.asRange(data_ptr.*.data[0..]);
            break :blk desc;
        });
        return ptr.*;
    }
};

const StrokeStorage = struct {
    const StrokePool = Pool(16, 16, sd.StrokeData, struct {
        data: sd.StrokeData,
    });

    allocator: Allocator,
    pool: StrokePool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try StrokePool.initCapacity(allocator, 2048),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: sd.StrokeData) !u32 {
        const id = try self.pool.add(.{
            .data = data,
        });
        return @bitCast(id);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: StrokePool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }
    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.StrokeData {
        const hdl: StrokePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
};

pub const PathStorage = struct {
    const PathPool = Pool(16, 16, Path, struct {
        data: sd.PathData,
        /// Cached bbox, updated each time `update()` is called.
        bbox: ?math.Rect2f = null,
    });

    allocator: Allocator,
    pool: PathPool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try PathPool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        var paths = self.pool.liveHandles();
        while (paths.next()) |hdl| {
            if (self.pool.getColumnPtr(hdl, .data)) |data_ptr| {
                switch (data_ptr.*) {
                    .path => |path| {
                        self.allocator.free(path.commands);
                        self.allocator.free(path.vertices);
                    },
                    else => {},
                }
            } else |_| {}
        }
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: Path) !u32 {
        const id = try self.pool.add(.{
            .data = .{
                .path = try data.cloneWithAllocator(self.allocator),
            },
        });
        return @bitCast(id);
    }
    pub fn make2(self: *@This(), data: sd.PathData) !u32 {
        const id = if (data == .path)
            try self.pool.add(.{
                .data = .{
                    .path = try data.path.cloneWithAllocator(self.allocator),
                },
            })
        else
            try self.pool.add(.{
                .data = data,
            });
        return @bitCast(id);
    }
    pub fn update(self: *@This(), rid: ResID, data: Path) !void {
        const hdl: PathPool.Handle = @bitCast(rid);
        const data_ptr = try self.pool.getColumnPtr(hdl, .data);
        switch (data_ptr.*) {
            .path => |path| {
                self.allocator.free(path.commands);
                self.allocator.free(path.vertices);
            },
            else => {},
        }
        data_ptr.* = .{
            .path = try data.cloneWithAllocator(self.allocator),
        };
        try self.pool.setColumn(hdl, .bbox, null);
    }
    /// Set the path as a Rectangle.
    pub fn setRect(self: *@This(), rid: ResID, data: math.Rect2f) !void {
        const hdl: PathPool.Handle = @bitCast(rid);
        const data_ptr = try self.pool.getColumnPtr(hdl, .data);
        switch (data_ptr.*) {
            .path => |path| {
                self.allocator.free(path.commands);
                self.allocator.free(path.vertices);
            },
            else => {},
        }
        data_ptr.* = .{
            .rect = data,
        };
        try self.pool.setColumn(hdl, .bbox, data);
    }
    /// Set the path as an Oval.
    pub fn setOval(self: *@This(), rid: ResID, data: math.Rect2f) !void {
        const hdl: PathPool.Handle = @bitCast(rid);
        const data_ptr = try self.pool.getColumnPtr(hdl, .data);
        switch (data_ptr.*) {
            .path => |path| {
                self.allocator.free(path.commands);
                self.allocator.free(path.vertices);
            },
            else => {},
        }
        data_ptr.* = .{
            .oval = data,
        };
        try self.pool.setColumn(hdl, .bbox, data);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: PathPool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .data)) |path_data_ptr| {
            switch (path_data_ptr.*) {
                .path => |path| {
                    self.allocator.free(path.commands);
                    self.allocator.free(path.vertices);
                },
                else => {},
            }
        } else |_| {}
        try self.pool.remove(hdl);
    }

    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.PathData {
        const hdl: PathPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch {
            return null;
        };
    }

    pub fn getBBox(self: @This(), rid: ResID) ?math.Rect2f {
        const hdl: PathPool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .bbox)) |bbox_opt| {
            if (bbox_opt.* == null) {
                const path_ptr = self.pool.getColumnPtr(hdl, .data) catch return null;
                bbox_opt.* = path_ptr.bbox();
            }
            return bbox_opt.*;
        } else |_| return null;
    }
};

pub const TextDocStorage = struct {
    const TextDocPool = Pool(16, 16, NoteDoc, struct {
        data: NoteDoc,
        paths: BlockPathList,
    });

    allocator: Allocator,
    font_db: *FontDB,
    pool: TextDocPool,

    pub fn init(allocator: Allocator, font_db: *FontDB) !@This() {
        return .{
            .allocator = allocator,
            .font_db = font_db,
            .pool = try TextDocPool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }

    pub fn makeParagraphs(self: *@This()) !u32 {
        var note_doc = NoteDoc.init(self.allocator);
        defer note_doc.deinit();

        // try note_doc.importFromString(content);
        const id = try self.pool.add(.{
            .data = note_doc,
            .paths = BlockPathList.init(self.allocator),
        });

        return @bitCast(id);
    }

    pub fn resetParagraphsWithText(self: *@This(), rid: ResID, text: []const u8, is_composition: bool) void {
        if (self.getDataPtr(rid)) |doc| {
            self.destroyPath(rid);

            if (is_composition) {
                doc.insertCompositionText(text.ptr[0..text.len]) catch return;
            } else {
                const end_block_index = doc.blocks.items.len - 1;
                const end_char_index = doc.characterCount(end_block_index);
                doc.deleteRange(0, 0, end_block_index, end_char_index) catch return;
                doc.insertText(text) catch return;
            }
        }
    }

    pub fn destroyPath(self: *@This(), rid: ResID) void {
        const hdl: TextDocPool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .paths)) |paths| {
            // for (paths.items) |*path| {
            //     path.path.deinit();
            // }
            paths.clearRetainingCapacity();
            // paths.deinit(); // may not need this
        } else |_| {}
    }

    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: TextDocPool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .paths)) |paths| {
            paths.clearRetainingCapacity();
        } else |_| {}
        // pool.remove will handle the deinit
        try self.pool.remove(hdl);
    }

    pub fn getDataPtr(self: @This(), rid: ResID) ?*NoteDoc {
        const hdl: TextDocPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch {
            return null;
        };
    }

    pub fn getPathsPtr(self: @This(), rid: ResID) ?*BlockPathList {
        const hdl: TextDocPool.Handle = @bitCast(rid);
        if (self.pool.getColumnPtr(hdl, .paths)) |ptr| {
            if (ptr.items.len == 0) {
                if (self.getDataPtr(rid)) |note_doc| {
                    note_doc.buildPaths(ptr, self.font_db, self.allocator) catch return null;
                }
            }
            return ptr;
        } else |_| {
            return null;
        }
    }
};

const TextStyleStorage = struct {
    const TextStylePool = Pool(16, 16, TextStyle, struct {
        data: TextStyle,
    });

    allocator: Allocator,
    pool: TextStylePool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try TextStylePool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }

    pub fn make(self: *@This(), style: TextStyle) !u32 {
        const id = try self.pool.add(.{
            .data = style,
        });
        return @bitCast(id);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: TextStylePool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }

    pub fn getDataPtr(self: @This(), rid: ResID) ?*TextStyle {
        const hdl: TextStylePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch {
            return null;
        };
    }
};

const NodeStorage = struct {
    const Hierarchy = struct {
        prev: u32 = 0,
        next: u32 = 0,
        parent: u32 = 0,
        child: u32 = 0,
    };
    const Matrix = struct {
        /// Size scale value.
        size: math.Vec2f = math.vec2fs(1.0),
        /// Local 2D matrix computed from `Transform` and `size`.
        local: ?math.Mat2Df = null,
        /// Matrix in world space.
        world: ?math.Mat2Df = null,
    };
    const BBox = struct {
        local: ?math.Rect2f = null,
        visual_local: ?math.Rect2f = null,
        aabb: ?math.Rect2f = null,
        visual_aabb: ?math.Rect2f = null,
        world: ?math.Quadf = null,
    };
    const OBBox = struct {
        local: math.Quadf = math.Quadf.initEmpty(),
        world: math.Quadf = math.Quadf.initEmpty(),
    };

    /// Keep some informations of the SceneData that this one is created from.
    const Owner = struct {
        /// path of the SceneData owner
        path: []const u8 = &.{},
        /// ID in the SceneData
        id: u32 = 0,
    };

    const NodePool = Pool(16, 16, sd.NodeData, struct {
        data: sd.NodeData,

        owner: Owner = .{},

        hierarchy: Hierarchy = .{},
        transform: Matrix = .{},
        bbox: BBox = .{},
        obbox: OBBox = .{},
    });

    allocator: Allocator,
    pool: NodePool,

    pub fn init(allocator: Allocator) !NodeStorage {
        return NodeStorage{
            .allocator = allocator,
            .pool = try NodePool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *NodeStorage) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *NodeStorage) void {
        self.pool.clear();
    }
    pub inline fn count(self: *NodeStorage) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *NodeStorage, data: sd.NodeData) !u32 {
        const hdl = try self.pool.add(.{
            .data = data,
        });
        return @bitCast(hdl);
    }
    /// Remove from all relationships and destroy the node.
    /// If the node has children, and including_children is
    /// - true: all children will be destroyed
    /// - false: all children will be detached from the node but not destroyed
    ///   (User has to take care of the children themselves.)
    pub fn destroy(self: *NodeStorage, rid: ResID, include_children: bool) !void {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier: *Hierarchy = try self.pool.getColumnPtr(hdl, .hierarchy);
        // remove from old relationship first
        try self.removeNode(rid);
        // remove from all children
        if (hier.child > 0) {
            var child_rid = hier.child;
            if (include_children) {
                try self.destroy(child_rid, true);
            } else {
                try self.setParent(child_rid, 0);
            }
            while (self.getNext(child_rid)) |next_rid| {
                child_rid = next_rid;
                if (include_children) {
                    try self.destroy(child_rid, true);
                } else {
                    try self.setParent(child_rid, 0);
                }
            }
        }
        try self.pool.remove(hdl);
    }
    pub fn getNode(self: NodeStorage, rid: ResID) ?sd.NodeData {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumn(hdl, .data) catch null;
    }
    pub fn getNodePtr(self: NodeStorage, rid: ResID) ?*sd.NodeData {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
    pub fn getHirarchy(self: NodeStorage, rid: ResID) ?Hierarchy {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumn(hdl, .hierarchy) catch null;
    }

    pub fn getTransformPtr(self: NodeStorage, rid: ResID) ?*Matrix {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .transform) catch null;
    }
    pub fn getBBoxPtr(self: NodeStorage, rid: ResID) ?*BBox {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .bbox) catch null;
    }
    pub fn getOBBoxPtr(self: NodeStorage, rid: ResID) ?*OBBox {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .obbox) catch null;
    }
    pub fn getParent(self: NodeStorage, rid: ResID) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        return if (hier.parent > 0) hier.parent else null;
    }
    pub fn setParent(self: NodeStorage, rid: ResID, parent_rid: u32) !void {
        if (rid == 0) return;
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier: *Hierarchy = try self.pool.getColumnPtr(hdl, .hierarchy);
        hier.parent = parent_rid;
    }
    pub fn getPrev(self: NodeStorage, rid: ResID) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        return if (hier.prev > 0) hier.prev else null;
    }
    pub fn setPrev(self: NodeStorage, rid: ResID, prev: u32) !void {
        if (rid == 0) return;
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = try self.pool.getColumnPtr(hdl, .hierarchy);
        hier.prev = prev;
    }
    pub fn getNext(self: NodeStorage, rid: ResID) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        return if (hier.next > 0) hier.next else null;
    }
    pub fn setNext(self: NodeStorage, rid: ResID, next: u32) !void {
        if (rid == 0) return;
        const self_hdl: NodePool.Handle = @bitCast(rid);
        const self_hier = try self.pool.getColumnPtr(self_hdl, .hierarchy);
        self_hier.next = next;
    }
    pub fn getFirstChild(self: NodeStorage, rid: ResID) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        return if (hier.child > 0) hier.child else null;
    }
    pub fn setFirstChild(self: NodeStorage, rid: ResID, child: u32) !void {
        if (rid == 0) return;
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = try self.pool.getColumnPtr(hdl, .hierarchy);
        hier.child = child;
    }
    pub fn getLastChild(self: NodeStorage, rid: ResID) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        if (hier.child > 0) {
            var last_child_id = hier.child;
            while (self.getNext(last_child_id)) |next_id| {
                last_child_id = next_id;
            }
            return last_child_id;
        }
        return null;
    }
    /// Get child node at index, if index is out of range, return null
    pub fn getChild(self: NodeStorage, rid: ResID, index: usize) ?u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return null;
        };
        if (index == 0) {
            return if (hier.child > 0) hier.child else null;
        } else {
            var i: usize = 1;
            var curr_id = hier.child;
            while (self.getNext(curr_id)) |next_id| {
                curr_id = next_id;
                if (i == index) {
                    return next_id;
                }
                i += 1;
            }
        }
        return null;
    }
    /// Add a child node to parent, if parent already has children, the node will be added to the end
    pub fn addChild(self: NodeStorage, parent: u32, child: u32) !void {
        // remove from old relationship first
        try self.removeNode(child);

        // add to new relationship
        if (self.getLastChild(parent)) |last_child_id| {
            try self.setNext(last_child_id, child);
            try self.setPrev(child, last_child_id);
        } else {
            try self.setFirstChild(parent, child);
        }
        try self.setParent(child, parent);
    }
    /// Insert a child node to parent at index, if index is out of range, the node will be added to the end
    /// If new index is out of range, the node will be added to the end
    /// Old relationship will be removed first (only keep children)
    pub fn insertChild(self: NodeStorage, parent: u32, child: u32, index: u32) !void {
        // remove from old relationship first
        try self.removeNode(child);

        // add to new relationship
        if (index == 0) {
            if (self.getFirstChild(parent)) |first_child_id| {
                try self.setPrev(first_child_id, child);
                try self.setNext(child, first_child_id);
            }
            try self.setFirstChild(parent, child);
        } else {
            if (self.getChild(parent, index - 1)) |prev_child_id| {
                if (self.getNext(prev_child_id)) |next_child_id| {
                    try self.setNext(prev_child_id, child);
                    try self.setPrev(child, prev_child_id);
                    try self.setNext(child, next_child_id);
                    try self.setPrev(next_child_id, child);
                } else {
                    try self.setNext(prev_child_id, child);
                    try self.setPrev(child, prev_child_id);
                }
            } else {
                // index out of range, add to the end
                // (not sure if this is a good behavior, can remove if needed)
                if (self.getLastChild(parent)) |last_child_id| {
                    try self.setNext(last_child_id, child);
                    try self.setPrev(child, last_child_id);
                }
            }
        }
        try self.setParent(child, parent);
    }
    /// Reorder a node to a new index in parent's children
    pub fn reorderNode(self: NodeStorage, rid: ResID, index: u32) !void {
        if (self.getParent(rid)) |parent_id| {
            if (self.getChild(parent_id, index)) |_| {
                try self.insertChild(parent_id, rid, index);
            } else {
                try self.addChild(parent_id, rid);
            }
        }
    }
    /// Remove node relationship with its old parent and siblings,
    /// siblings will be reconnected
    /// children will be kept
    pub fn removeNode(self: NodeStorage, rid: ResID) !void {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = try self.pool.getColumnPtr(hdl, .hierarchy);

        // Remove from parent
        if (hier.parent > 0) {
            if (self.getFirstChild(hier.parent)) |first_child_id| {
                if (first_child_id == rid) {
                    try self.setFirstChild(hier.parent, hier.next);
                }
            }
        }
        // Reconnect old prev and old next
        if (hier.prev > 0) {
            try self.setNext(hier.prev, hier.next);
        }
        if (hier.next > 0) {
            try self.setPrev(hier.next, hier.prev);
        }

        // Cleanup relationship but only keep children
        try self.setParent(rid, 0);
        try self.setNext(rid, 0);
        try self.setPrev(rid, 0);
    }
    pub fn getNodeChildCount(self: NodeStorage, rid: ResID) u32 {
        const hdl: NodePool.Handle = @bitCast(rid);
        const hier = self.pool.getColumnPtr(hdl, .hierarchy) catch {
            return 0;
        };
        if (hier.child > 0) {
            var child_count: u32 = 1;
            var curr_id = hier.child;
            while (self.getNext(curr_id)) |next_id| {
                curr_id = next_id;
                child_count += 1;
            }
            return child_count;
        }
        return 0;
    }
    pub fn children(self: *NodeStorage, rid: ResID) ChildrenIterator {
        return ChildrenIterator{
            .nodes = self,
            .rid = rid,
        };
    }
    pub fn childrenReversed(self: *NodeStorage, rid: ResID) ChildrenIterator {
        return ChildrenIterator{
            .nodes = self,
            .rid = rid,
            .reverse = true,
        };
    }

    pub fn getOwnerPtr(self: *NodeStorage, rid: ResID) ?*Owner {
        const hdl: NodePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .owner) catch null;
    }

    const ChildrenIterator = struct {
        nodes: *NodeStorage,
        rid: ResID = 0,
        reverse: bool = false,
        _started: bool = false,
        _curr_id: ?u32 = null,

        pub fn next(self: *ChildrenIterator) ?u32 {
            if (self.rid == 0) return null;

            if (!self._started) {
                self._started = true;
                self._curr_id = if (self.reverse)
                    self.nodes.getLastChild(self.rid)
                else
                    self.nodes.getFirstChild(self.rid);
                return self._curr_id;
            }

            if (self._curr_id) |id| {
                self._curr_id = if (self.reverse)
                    self.nodes.getPrev(id)
                else
                    self.nodes.getNext(id);
                return self._curr_id;
            }

            return null;
        }
    };

    test "NodeStorage addChild hierarchy and navigation" {
        const allocator = std.testing.allocator;
        var storage = try NodeStorage.init(allocator);
        defer storage.deinit();

        // Create nodes
        const parentId = try storage.make(.{ .name = "p", .data = .{ .group = .{} } });
        const child1Id = try storage.make(.{ .name = "c1", .data = .{ .group = .{} } });
        const child2Id = try storage.make(.{ .name = "c2", .data = .{ .path = .{} } });
        const child3Id = try storage.make(.{ .name = "c3", .data = .{ .text = .{} } });

        // Add children to parent
        try storage.addChild(parentId, child1Id);
        try storage.addChild(parentId, child2Id);
        try storage.addChild(parentId, child3Id);

        // Verify hierarchy
        const firstChildId = storage.getFirstChild(parentId);
        try std.testing.expect(firstChildId != null);
        try std.testing.expectEqual(child1Id, firstChildId.?);

        const secondChildId = storage.getNext(child1Id);
        try std.testing.expect(secondChildId != null);
        try std.testing.expectEqual(child2Id, secondChildId.?);

        const thirdChildId = storage.getNext(child2Id);
        try std.testing.expect(thirdChildId != null);
        try std.testing.expectEqual(child3Id, thirdChildId.?);

        const noNextChildId = storage.getNext(child3Id);
        try std.testing.expect(noNextChildId == null);

        // Verify parent relationships
        const child1ParentId = storage.getParent(child1Id);
        try std.testing.expect(child1ParentId != null);
        try std.testing.expectEqual(parentId, child1ParentId.?);

        const child2ParentId = storage.getParent(child2Id);
        try std.testing.expect(child2ParentId != null);
        try std.testing.expectEqual(parentId, child2ParentId.?);

        const child3ParentId = storage.getParent(child3Id);
        try std.testing.expect(child3ParentId != null);
        try std.testing.expectEqual(parentId, child3ParentId.?);
    }

    test "NodeStorage getChild with index" {
        const allocator = std.testing.allocator;
        var storage = try NodeStorage.init(allocator);
        defer storage.deinit();

        const parentData: sd.NodeData = .{ .name = "p", .data = .{ .group = .{} } };
        const firstChildData: sd.NodeData = .{ .name = "c1", .data = .{ .group = .{} } };
        const secondChildData: sd.NodeData = .{ .name = "c2", .data = .{ .group = .{} } };
        const thirdChildData: sd.NodeData = .{ .name = "c3", .data = .{ .group = .{} } };

        // Create nodes
        const parentId = try storage.make(parentData);
        const firstChildId = try storage.make(firstChildData);
        const secondChildId = try storage.make(secondChildData);
        const thirdChildId = try storage.make(thirdChildData);

        // Add children to parent
        try storage.addChild(parentId, firstChildId);
        try storage.addChild(parentId, secondChildId);
        try storage.addChild(parentId, thirdChildId);

        // Verify getChild function
        const retrievedFirstChildId = storage.getChild(parentId, 0);
        try std.testing.expect(retrievedFirstChildId != null);
        try std.testing.expectEqual(firstChildId, retrievedFirstChildId.?);

        const retrievedSecondChildId = storage.getChild(parentId, 1);
        try std.testing.expect(retrievedSecondChildId != null);
        try std.testing.expectEqual(secondChildId, retrievedSecondChildId.?);

        const retrievedThirdChildId = storage.getChild(parentId, 2);
        try std.testing.expect(retrievedThirdChildId != null);
        try std.testing.expectEqual(thirdChildId, retrievedThirdChildId.?);

        const nonExistentChildId = storage.getChild(parentId, 3);
        try std.testing.expect(nonExistentChildId == null);
    }

    test "NodeStorage insertChild and verify hierarchy" {
        const allocator = std.testing.allocator;
        var storage = try NodeStorage.init(allocator);
        defer storage.deinit();

        const parentData: sd.NodeData = .{ .name = "p", .data = .{ .group = .{} } };
        const firstChildData: sd.NodeData = .{ .name = "c1", .data = .{ .group = .{} } };
        const secondChildData: sd.NodeData = .{ .name = "c2", .data = .{ .group = .{} } };
        const thirdChildData: sd.NodeData = .{ .name = "c3", .data = .{ .group = .{} } };

        // Create nodes
        const parentId = try storage.make(parentData);
        const firstChildId = try storage.make(firstChildData);
        const secondChildId = try storage.make(secondChildData);
        const thirdChildId = try storage.make(thirdChildData);

        // Add first child
        try storage.addChild(parentId, firstChildId);
        // Insert second child at position 0
        try storage.insertChild(parentId, secondChildId, 0);
        // Insert third child at position 1
        try storage.insertChild(parentId, thirdChildId, 1);

        // Children order should be: second > third > first

        // Verify hierarchy
        const firstChildIdAfterInsert = storage.getFirstChild(parentId);
        try std.testing.expect(firstChildIdAfterInsert != null);
        try std.testing.expectEqual(secondChildId, firstChildIdAfterInsert.?);

        const secondChildIdAfterInsert = storage.getNext(secondChildId);
        try std.testing.expect(secondChildIdAfterInsert != null);
        try std.testing.expectEqual(thirdChildId, secondChildIdAfterInsert.?);

        const thirdChildIdAfterInsert = storage.getNext(thirdChildId);
        try std.testing.expect(thirdChildIdAfterInsert != null);
        try std.testing.expectEqual(firstChildId, thirdChildIdAfterInsert.?);

        const noNextChildId = storage.getNext(firstChildId);
        try std.testing.expect(noNextChildId == null);

        // Verify parent relationships
        try std.testing.expectEqual(parentId, storage.getParent(secondChildId).?);
        try std.testing.expectEqual(parentId, storage.getParent(thirdChildId).?);
        try std.testing.expectEqual(parentId, storage.getParent(firstChildId).?);
    }
};

const ComposeStorage = struct {
    const ComposePool = Pool(16, 16, sd.ComposeData, struct {
        data: sd.ComposeData,
    });

    allocator: Allocator,
    pool: ComposePool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try ComposePool.initCapacity(allocator, 1024),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: sd.ComposeData) !u32 {
        const id = try self.pool.add(.{
            .data = data,
        });
        return @bitCast(id);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: ComposePool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }
    pub fn getComposePtr(self: @This(), rid: ResID) ?*sd.ComposeData {
        const hdl: ComposePool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
};

const AnimationStorage = struct {
    const AnimationPool = Pool(16, 16, sd.AnimationData, struct {
        data: sd.AnimationData,
    });

    allocator: Allocator,
    pool: AnimationPool,

    pub fn init(allocator: Allocator) !@This() {
        return .{
            .allocator = allocator,
            .pool = try AnimationPool.initCapacity(allocator, 4096),
        };
    }
    pub fn deinit(self: *@This()) void {
        self.purge();
        self.pool.deinit();
    }
    pub fn purge(self: *@This()) void {
        self.pool.clear();
    }
    pub inline fn count(self: *@This()) usize {
        return self.pool.liveHandleCount();
    }
    pub fn make(self: *@This(), data: sd.AnimationData) !u32 {
        const id = try self.pool.add(.{
            .data = data,
        });
        return @bitCast(id);
    }
    pub fn destroy(self: *@This(), rid: ResID) !void {
        const hdl: AnimationPool.Handle = @bitCast(rid);
        try self.pool.remove(hdl);
    }
    pub fn getDataPtr(self: @This(), rid: ResID) ?*sd.AnimationData {
        const hdl: AnimationPool.Handle = @bitCast(rid);
        return self.pool.getColumnPtr(hdl, .data) catch null;
    }
};

const DataDB = struct {
    const DataMap = std.StringHashMap(sd.SceneData);

    allocator: std.mem.Allocator,
    data_map: DataMap,

    pub fn init(allocator: std.mem.Allocator) DataDB {
        return DataDB{
            .allocator = allocator,
            .data_map = DataMap.init(allocator),
        };
    }
    pub fn deinit(db: *DataDB) void {
        var it = db.data_map.valueIterator();
        while (it.next()) |data_ptr| {
            data_ptr.deinit();
        }
        db.data_map.deinit();
    }

    /// Put a SceneData with path (target path).
    pub fn put(db: *DataDB, path: []const u8, data: sd.SceneData) !void {
        const copy = try data.cloneWithAllocator(db.allocator);
        try db.data_map.put(path, copy);
    }
    /// Remove a scene data with given path, and DESTROY it!
    pub fn remove(db: *DataDB, path: []const u8) void {
        if (db.data_map.fetchRemove(path)) |pair| {
            pair.value.deinit();
        }
    }
    pub fn getPtr(db: *DataDB, path: []const u8) ?*sd.SceneData {
        return db.data_map.getPtr(path);
    }
};

allocator: std.mem.Allocator,
sd_arena: std.heap.ArenaAllocator,

font_db: *FontDB,
data_db: DataDB,

colors: ColorStorage,
gradients: GradientStorage,
images: ImageStorage,
strokes: StrokeStorage,
paths: PathStorage,
nodes: NodeStorage,
composes: ComposeStorage,
text_docs: TextDocStorage,
animations: AnimationStorage,
root: u32 = 0,

pub fn init(allocator: std.mem.Allocator) !SceneTree {
    const font_db = try allocator.create(FontDB);
    font_db.* = FontDB.init(allocator);
    return SceneTree{
        .allocator = allocator,
        .sd_arena = std.heap.ArenaAllocator.init(allocator),

        .font_db = font_db,
        .data_db = DataDB.init(allocator),

        .colors = try ColorStorage.init(allocator),
        .gradients = try GradientStorage.init(allocator),
        .images = try ImageStorage.init(allocator),
        .strokes = try StrokeStorage.init(allocator),
        .paths = try PathStorage.init(allocator),
        .nodes = try NodeStorage.init(allocator),
        .composes = try ComposeStorage.init(allocator),
        .text_docs = try TextDocStorage.init(allocator, font_db),
        .animations = try AnimationStorage.init(allocator),
    };
}
pub fn deinit(tree: *SceneTree) void {
    tree.font_db.deinit();
    tree.allocator.destroy(tree.font_db);
    tree.data_db.deinit();

    tree.colors.deinit();
    tree.gradients.deinit();
    tree.images.deinit();
    tree.strokes.deinit();
    tree.paths.deinit();
    tree.nodes.deinit();
    tree.composes.deinit();
    tree.text_docs.deinit();
    tree.animations.deinit();
    tree.root = 0;

    tree.sd_arena.deinit();
}
pub fn prepare(tree: *SceneTree) !void {
    if (tree.gradients.dirty) {
        try tree.gradients.updateImage();
        tree.gradients.dirty = false;
    }
}
pub fn purge(tree: *SceneTree) void {
    tree.colors.purge();
    tree.gradients.purge();
    tree.images.purge();
    tree.strokes.purge();
    tree.paths.purge();
    tree.nodes.purge();
    tree.composes.purge();
    tree.text_docs.purge();
    tree.animations.purge();
    tree.root = 0;
}

pub fn traversePreOrder(st: *SceneTree, rid: ResID) !NodeIteratorPreOrder {
    return try NodeIteratorPreOrder.init(st.allocator, st, rid);
}

const NodeIteratorPreOrder = struct {
    stack: std.ArrayList(u32),
    allocator: std.mem.Allocator,
    tree: *SceneTree,
    pub fn init(allocator: std.mem.Allocator, st: *SceneTree, root: u32) !NodeIteratorPreOrder {
        var stack = std.ArrayList(u32).init(allocator);
        try stack.append(root);
        return .{
            .stack = stack,
            .allocator = allocator,
            .tree = st,
        };
    }
    pub fn deinit(self: *NodeIteratorPreOrder) void {
        self.stack.deinit();
    }
    pub fn next(self: *NodeIteratorPreOrder) ?u32 {
        if (self.stack.popOrNull()) |node_id| {
            if (self.tree.nodes.getNode(node_id)) |node| {
                switch (node.data) {
                    .group => {
                        var iter = self.tree.nodes.children(node_id);
                        while (iter.next()) |child_id| {
                            self.stack.append(child_id) catch return null;
                        }
                    },
                    else => {},
                }
            }
            return node_id;
        }
        return null;
    }
};

pub fn traversePostOrder(st: *SceneTree, rid: ResID) !NodeIteratorPostOrder {
    return try NodeIteratorPostOrder.init(st.allocator, st, rid);
}

const NodeIteratorPostOrder = struct {
    stack: std.ArrayList(u32),
    visited: std.AutoHashMap(u32, bool),
    allocator: std.mem.Allocator,
    tree: *SceneTree,

    pub fn init(allocator: std.mem.Allocator, st: *SceneTree, root: u32) !NodeIteratorPostOrder {
        var stack = std.ArrayList(u32).init(allocator);
        try stack.append(root);
        const visited = std.AutoHashMap(u32, bool).init(allocator);
        return .{
            .stack = stack,
            .visited = visited,
            .allocator = allocator,
            .tree = st,
        };
    }

    pub fn deinit(self: *NodeIteratorPostOrder) void {
        self.stack.deinit();
        self.visited.deinit();
    }

    pub fn next(self: *NodeIteratorPostOrder) ?u32 {
        while (self.stack.items.len > 0) {
            const node_id = self.stack.getLastOrNull() orelse 0;
            if (self.visited.get(node_id) == null) {
                self.visited.put(node_id, true) catch return null;
                if (self.tree.nodes.getNode(node_id)) |node| {
                    if (node.data == .group) {
                        var iter = self.tree.nodes.children(node_id);
                        while (iter.next()) |child_id| {
                            self.stack.append(child_id) catch return null;
                        }
                    }
                }
            } else {
                _ = self.stack.pop();
                return node_id;
            }
        }
        return null;
    }
};

/// Make a instance out of the given scene data pack, returns the main scene root.
pub fn instance(tree: *SceneTree, data_path: []const u8) !ResID {
    _ = tree.sd_arena.reset(.retain_capacity);
    if (tree.data_db.getPtr(data_path)) |data| {
        var spawner = Spawner.init(tree.sd_arena.allocator(), tree, data);
        return try spawner.spawn();
    }
    return 0;
}

/// TODO: duplicate internal memory (name/Path etc.), maybe add a different API for that
pub fn pack(tree: *SceneTree, allocator: std.mem.Allocator, root_rid: ResID) !sd.SceneData {
    _ = tree.sd_arena.reset(.retain_capacity);
    var packer = Packer.init(allocator, tree);
    return try packer.pack(root_rid);
}

const Packer = struct {
    const HashMap = std.AutoArrayHashMap;

    allocator: std.mem.Allocator,

    tree: *SceneTree,
    data: sd.SceneData,
    root_rid: ResID,

    pub fn init(allocator: std.mem.Allocator, tree: *SceneTree) Packer {
        return Packer{
            .allocator = allocator,
            .tree = tree,
            .data = sd.SceneData.init(allocator),
            .root_rid = 0,
        };
    }
    pub fn deinit(packer: *Packer) void {
        packer.data.deinit();
    }

    pub fn pack(packer: *Packer, root_rid: ResID) !sd.SceneData {
        packer.root_rid = root_rid;
        // 1. Pack nodes belongs to the subtree of given root.
        const main_scene = try packer.packNode(root_rid);
        // 2. Replace resource ids with their order index, make them easier to work with locally.
        try replaceResMap(&packer.data.colors);
        try replaceResMap(&packer.data.gradients);
        try replaceResMap(&packer.data.images);
        try replaceResMap(&packer.data.strokes);
        try replaceResMap(&packer.data.paths);
        try replaceResMap(&packer.data.nodes);
        try replaceResMap(&packer.data.composes);
        try replaceResMap(&packer.data.text_docs);
        try replaceResMap(&packer.data.scenes);
        try replaceResMap(&packer.data.animations);
        packer.data.main_scene = main_scene;
        return packer.data;
    }

    /// Replace keys of a resource map with `index + 1`.
    fn replaceResMap(res_map: anytype) !void {
        var it = res_map.iterator();
        while (it.next()) |pair| {
            pair.key_ptr.* = indexToID(res_map.getIndex(pair.key_ptr.*).?);
        }
        try res_map.reIndex();
    }
    /// `fill` should be something similar to `*FillPaintData`.
    fn packFill(packer: *Packer, fill: anytype) !void {
        const tree = packer.tree;
        const data = &packer.data;
        switch (fill.paint_tag) {
            .color => {
                const entry = try data.colors.getOrPut(fill.paint_id);
                if (!entry.found_existing) {
                    if (tree.colors.getDataPtr(fill.paint_id)) |data_ptr| {
                        entry.value_ptr.* = data_ptr.*;
                        fill.paint_id = indexToID(data.colors.getIndex(fill.paint_id).?);
                    } else {
                        _ = data.colors.orderedRemove(fill.paint_id);
                    }
                }
            },
            .gradient => {
                const entry = try data.gradients.getOrPut(fill.paint_id);
                if (!entry.found_existing) {
                    if (tree.gradients.getDataPtr(fill.paint_id)) |data_ptr| {
                        entry.value_ptr.* = data_ptr.*;
                        fill.paint_id = indexToID(data.gradients.getIndex(fill.paint_id).?);
                    } else {
                        _ = data.gradients.orderedRemove(fill.paint_id);
                    }
                }
            },
            .image => {
                const entry = try data.images.getOrPut(fill.paint_id);
                if (!entry.found_existing) {
                    if (tree.images.getDataPtr(fill.paint_id)) |data_ptr| {
                        entry.value_ptr.* = data_ptr.*;
                        fill.paint_id = indexToID(data.images.getIndex(fill.paint_id).?);
                    } else {
                        _ = data.images.orderedRemove(fill.paint_id);
                    }
                }
            },
            else => {},
        }
    }
    /// `stroke` should be something similar to `*StrokePaintData`.
    fn packStroke(packer: *Packer, stroke: anytype) !void {
        const tree = packer.tree;
        const data = &packer.data;
        try packer.packFill(stroke);
        {
            const entry = try data.strokes.getOrPut(stroke.data_id);
            if (!entry.found_existing) {
                if (tree.strokes.getDataPtr(stroke.data_id)) |data_ptr| {
                    entry.value_ptr.* = data_ptr.*;
                    stroke.data_id = indexToID(data.strokes.getIndex(stroke.data_id).?);
                } else {
                    _ = data.strokes.orderedRemove(stroke.data_id);
                }
            }
        }
    }

    fn packNode(packer: *Packer, node_rid: ResID) !u32 {
        const tree = packer.tree;
        var data = &packer.data;
        if (tree.nodes.getNodePtr(node_rid)) |node_ptr| {
            try data.nodes.put(node_rid, node_ptr.*);
            if (node_ptr.name.len > 0) {
                node_ptr.name = try packer.allocator.dupe(u8, node_ptr.name);
            }
            switch (node_ptr.data) {
                .group => |*n| {
                    var it = tree.nodes.children(node_rid);
                    while (it.next()) |child_rid| {
                        _ = try packer.packNode(child_rid);
                    }
                    if (n.compose[0] > 0) n.compose[0] = try packer.packNode(n.compose[0]);
                    if (n.compose[1] > 0) n.compose[1] = try packer.packNode(n.compose[1]);
                },
                .path => |*n| {
                    // path data
                    {
                        const entry = try data.paths.getOrPut(n.path_id);
                        if (!entry.found_existing) {
                            if (tree.paths.getDataPtr(n.path_id)) |data_ptr| {
                                entry.value_ptr.* = data_ptr.*;
                                switch (data_ptr.*) {
                                    .path => |path| entry.value_ptr.path = try path.cloneWithAllocator(packer.allocator),
                                    else => {},
                                }
                                n.path_id = indexToID(data.paths.getIndex(n.path_id).?);
                            } else {
                                _ = data.paths.orderedRemove(n.path_id);
                            }
                        }
                    }
                    // fill
                    try packer.packFill(&n.fill);
                    // stroke
                    try packer.packStroke(&n.stroke);
                },
                .text => {},
            }
            node_ptr.id = indexToID(data.nodes.getIndex(node_rid).?);
            if (node_ptr.parent_id == packer.root_rid) {
                node_ptr.parent_id = 0;
            } else if (node_ptr.parent_id > 0) {
                if (data.nodes.getIndex(node_ptr.parent_id)) |parent_index| {
                    node_ptr.parent_id = indexToID(parent_index);
                }
            }
            return node_ptr.id;
        }
        return 0;
    }

    /// What ID should be use for resource with given index.
    ///
    /// We use `0` as the invalid ID, and `index + 1` as the final ID for resources in a SceneData.
    inline fn indexToID(index: usize) u32 {
        return @intCast(index + 1);
    }
};

const Spawner = struct {
    tree: *SceneTree,
    data: *sd.SceneData,

    colors: std.AutoHashMap(u32, u32),
    gradients: std.AutoHashMap(u32, u32),
    images: std.AutoHashMap(u32, u32),
    strokes: std.AutoHashMap(u32, u32),
    paths: std.AutoHashMap(u32, u32),
    nodes: std.AutoHashMap(u32, u32),
    composes: std.AutoHashMap(u32, u32),
    text_docs: std.AutoHashMap(u32, u32),
    text_styles: std.AutoHashMap(u32, u32),
    main_scene: ResID,

    pub fn init(allocator: std.mem.Allocator, tree: *SceneTree, data: *sd.SceneData) Spawner {
        return Spawner{
            .tree = tree,
            .data = data,

            .colors = std.AutoHashMap(u32, u32).init(allocator),
            .gradients = std.AutoHashMap(u32, u32).init(allocator),
            .images = std.AutoHashMap(u32, u32).init(allocator),
            .strokes = std.AutoHashMap(u32, u32).init(allocator),
            .paths = std.AutoHashMap(u32, u32).init(allocator),
            .nodes = std.AutoHashMap(u32, u32).init(allocator),
            .composes = std.AutoHashMap(u32, u32).init(allocator),
            .text_docs = std.AutoHashMap(u32, u32).init(allocator),
            .text_styles = std.AutoHashMap(u32, u32).init(allocator),

            .main_scene = 0,
        };
    }
    pub fn deinit(spawner: *Spawner) void {
        spawner.colors.deinit();
        spawner.gradients.deinit();
        spawner.images.deinit();
        spawner.strokes.deinit();
        spawner.paths.deinit();
        spawner.nodes.deinit();
        spawner.composes.deinit();
        spawner.text_docs.deinit();
        spawner.text_styles.deinit();
        spawner.main_scene = 0;
    }

    pub fn spawn(spawner: *Spawner) !ResID {
        // 1. Make instances from resources.
        try spawner.instanciateRes();
        // 2. Remap relationships between resources.
        try spawner.remapRelationships();
        return spawner.main_scene;
    }

    fn instanciateRes(spawner: *Spawner) !void {
        const tree = spawner.tree;
        const data = spawner.data;
        {
            var it = data.colors.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.colors.make(pair.value_ptr.toRGBA32());
                try spawner.colors.put(id, rid);
            }
        }
        {
            var it = data.gradients.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.gradients.make(pair.value_ptr.*);
                try spawner.gradients.put(id, rid);
            }
        }
        {
            var it = data.images.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.images.make(pair.value_ptr.*);
                try spawner.images.put(id, rid);
            }
        }
        {
            var it = data.strokes.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.strokes.make(pair.value_ptr.*);
                try spawner.strokes.put(id, rid);
            }
        }
        {
            var it = data.paths.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.paths.make2(pair.value_ptr.*);
                try spawner.paths.put(id, rid);
            }
        }
        {
            var it = data.nodes.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.nodes.make(pair.value_ptr.*);
                try spawner.nodes.put(id, rid);
            }
        }
        {
            var it = data.composes.iterator();
            while (it.next()) |pair| {
                const id = pair.key_ptr.*;
                const rid = try tree.composes.make(pair.value_ptr.*);
                try spawner.composes.put(id, rid);
            }
        }
        {
            // var it = data.text_docs.iterator();
            // while (it.next()) |pair| {
            //     const id = pair.key_ptr.*;
            //     const rid = try tree.text_docs.make(pair.value_ptr.*);
            //     try spawner.text_docs.put(id, rid);
            // }
        }
        {
            // var it = data.text_styles.iterator();
            // while (it.next()) |pair| {
            //     const id = pair.key_ptr.*;
            //     const rid = try tree.text_styles.make(pair.value_ptr.*);
            //     try spawner.text_styles.put(id, rid);
            // }
        }
    }
    fn remapRelationships(spawner: *Spawner) !void {
        const tree = spawner.tree;
        const data = spawner.data;
        for (data.nodes.keys()) |node_id| {
            if (spawner.nodes.get(node_id)) |node_rid| {
                if (tree.nodes.getNodePtr(node_rid)) |node_ptr| {
                    node_ptr.id = node_rid;
                    // hierarchy
                    if (node_ptr.parent_id > 0) {
                        if (spawner.nodes.get(node_ptr.parent_id)) |parent_rid| {
                            // const parent_ptr = tree.nodes.getNode(parent_rid).?;
                            // std.log.debug("[{s}].addChild({s})", .{
                            //     parent_ptr.name,
                            //     node_ptr.name,
                            // });
                            try tree.nodes.addChild(parent_rid, node_rid);
                        }
                    }
                    // node
                    switch (node_ptr.data) {
                        .group => |*n| {
                            if (n.compose[0] > 0) {
                                n.compose[0] = spawner.nodes.get(n.compose[0]) orelse 0;
                            }
                            if (n.compose[1] > 0) {
                                n.compose[1] = spawner.nodes.get(n.compose[1]) orelse 0;
                            }
                        },
                        .path => |*n| {
                            if (n.path_id > 0) {
                                n.path_id = spawner.paths.get(n.path_id) orelse 0;
                            }
                            if (n.fill.paint_id > 0) {
                                switch (n.fill.paint_tag) {
                                    .color => n.fill.paint_id = spawner.colors.get(n.fill.paint_id) orelse 0,
                                    .gradient => n.fill.paint_id = spawner.gradients.get(n.fill.paint_id) orelse 0,
                                    .image => n.fill.paint_id = spawner.images.get(n.fill.paint_id) orelse 0,
                                    else => {},
                                }
                            }
                            if (n.stroke.paint_id > 0) {
                                switch (n.stroke.paint_tag) {
                                    .color => n.stroke.paint_id = spawner.colors.get(n.stroke.paint_id) orelse 0,
                                    .gradient => n.stroke.paint_id = spawner.gradients.get(n.stroke.paint_id) orelse 0,
                                    .image => n.stroke.paint_id = spawner.images.get(n.stroke.paint_id) orelse 0,
                                    else => {},
                                }
                                if (n.stroke.data_id > 0) n.stroke.data_id = spawner.strokes.get(n.stroke.data_id) orelse 0;
                            }
                        },
                        .text => |*n| {
                            _ = n;
                        },
                    }
                    // Keep original id in the owner cache
                    const owner_ptr = tree.nodes.getOwnerPtr(node_rid).?;
                    owner_ptr.id = node_id;
                    // Save main scene rid
                    if (node_id == spawner.data.main_scene) {
                        spawner.main_scene = node_rid;
                    }
                }
            } else {
                // FIXME: what should we do while the rid is missing? or maybe this is just unreachable!
            }
        }
    }
};
