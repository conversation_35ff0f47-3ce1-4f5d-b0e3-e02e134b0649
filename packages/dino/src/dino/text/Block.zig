const Block = @This();

const std = @import("std");
const assert = std.debug.assert;
const math = @import("../math.zig");
const Path = @import("../Path.zig");
const FontDB = @import("FontDB.zig");
const Range = @import("Range.zig");
const DynamicString = @import("DynamicString.zig");
const style = @import("style.zig");
const shaping = @import("shaping.zig");

allocator: std.mem.Allocator = undefined,

// data
string: DynamicString = undefined,
composition_text: ?struct {
    text: []const u8,
    comp_text: []const u8,
    cursor_idx: usize,
} = null,
spans: std.ArrayList(style.Span),

// computation
layout_xform: math.Mat2Df = math.mat2df_identity(),
layout: ?shaping.Layout = null,

pub fn init(allocator: std.mem.Allocator) Block {
    return Block{
        .allocator = allocator,
        .string = DynamicString.init(allocator),
        .spans = std.ArrayList(style.Span).init(allocator),
    };
}
pub fn deinit(block: *Block) void {
    block.clearCache();
    block.spans.deinit();
    block.string.deinit();
}

/// Re-shape text layout.
pub fn buildLayout(block: *Block, font_db: *FontDB, base_style: style.CascadeStyle.Flattened, max_width: f32) !void {
    block.clearCache();
    // Flatten spans first.
    const flat_spans = try style.flattenSpans(block.allocator, block.spans.items, base_style);
    defer block.allocator.free(flat_spans);
    // Layout text and calculate xform to move top-left to local (0, 0).
    const layout = try shaping.layoutText(block.allocator, font_db, block.string.asSlice(), flat_spans, max_width);
    const block_bbox = layout.calcBlockBBox();
    block.layout = layout;
    block.layout_xform = math.mat2df_translate(-block_bbox.left(), -block_bbox.top());
}

pub fn horizontalAlignBlock(block: *Block, base_style: style.CascadeStyle.Flattened, max_width: f32) !void {
    const flat_spans = try style.flattenSpans(block.allocator, block.spans.items, base_style);
    defer block.allocator.free(flat_spans);
    const horizontal_alignment = flat_spans[0].style.horizontal_alignment;
    if (horizontal_alignment == .left) {
        return;
    }
    var offestX: f32 = 0.0;

    if (horizontal_alignment == .right) {
        const width = block.blockBBox().?.width();
        offestX = max_width - width;
    } else if (horizontal_alignment == .center) {
        const width = block.blockBBox().?.width();
        offestX = (max_width - width) * 0.5;
    }
    block.layout_xform = block.layout_xform.mul(math.mat2df_translate(offestX, 0.0));
}

pub fn verticalAlignBlock(block: *Block, base_style: style.CascadeStyle.Flattened, doc_height: f32, max_height: f32) !void {
    const flat_spans = try style.flattenSpans(block.allocator, block.spans.items, base_style);
    defer block.allocator.free(flat_spans);
    const vertical_alignment = flat_spans[0].style.vertical_alignment;
    if (vertical_alignment == .top) {
        return;
    }
    var offestY: f32 = 0.0;
    if (vertical_alignment == .bottom) {
        offestY = max_height - doc_height;
    } else if (vertical_alignment == .middle) {
        offestY = (max_height - doc_height) * 0.5;
    }
    block.layout_xform = block.layout_xform.mul(math.mat2df_translate(0.0, offestY));
}

/// Build paths per span.
pub fn buildPaths(block: Block, font_db: *FontDB, allocator: std.mem.Allocator, xform: math.Mat2Df) !std.ArrayList(Path.Builder) {
    var paths = std.ArrayList(Path.Builder).init(allocator);
    if (block.layout) |layout| {
        const block_xform = xform.mul(block.layout_xform);
        for (layout.visual_spans.items) |*span| {
            var path = Path.Builder.init(allocator);
            for (span.positioned_glyphs.items) |*glyph| {
                if (font_db.fetchGlyphPath(glyph.font, glyph.id)) |glyph_path| {
                    var glyph_xform = glyph.outlineXform();
                    glyph_xform = block_xform.mul(glyph_xform); // TODO: add alignment offset to glyph_xform
                    try path.appendTransformed(glyph_path.path, glyph_xform);
                }
            }
            try paths.append(path);
        }
    }
    return paths;
}

/// Returns the total number of characters in this block.
pub inline fn characterCount(block: Block) usize {
    return block.string.length();
}
/// How many lines in the block.
pub fn lineCount(block: Block) usize {
    if (block.layout) |layout| {
        return layout.lines.items.len;
    }
    return 0;
}

/// Find line index at given character index.
/// Returns the index of the line that contains the character.
/// If the character is a newline, returns the index of the next line.
/// If there is no layout, returns 0.
pub fn lineIndexAt(block: Block, char_index: usize) usize {
    if (block.layout) |layout| {
        for (layout.lines.items, 0..) |*line, i| {
            if (line.range.contains(char_index)) {
                // If this is not the last line and we're at the end of this line,
                // return the next line's index
                if (i < layout.lines.items.len - 1 and char_index == line.range.location + line.range.length) {
                    return i + 1;
                }
                return i;
            }
        }
        // If we get here, we're beyond the last line, so return the last line index
        if (layout.lines.items.len > 0) {
            return layout.lines.items.len - 1;
        }
    }
    return 0;
}

pub fn lineIndexAtCursor(block: Block, char_index: usize) usize {
    if (block.layout) |layout| {
        for (layout.lines.items, 0..) |*line, i| {
            if (line.range.contains(char_index)) {
                return i;
            }
        }
    }
    return 0;
}
/// Get the range of characters in the given line.
pub fn characterRangeAtLine(block: Block, line_index: usize) Range {
    if (block.layout) |layout| {
        assert(line_index < layout.lines.items.len);
        return layout.lines.items[line_index].range;
    }
    return .{};
}

/// Get the bounding box of character at specific index.
pub fn characterBBox(block: Block, char_index: usize) ?math.Rect2f {
    if (block.layout) |layout| {
        for (layout.visual_spans.items) |*v_span| {
            if (!v_span.range.contains(char_index)) continue;

            var total_text_count: usize = 0;
            for (v_span.positioned_glyphs.items) |*g| {
                var sub_text_count: usize = 0;
                if (g.text.len > 0) {
                    const g_factor = std.unicode.utf8ByteSequenceLength(g.text[0]) catch 1;

                    for (0..g.text.len / g_factor) |i| {
                        if (total_text_count + i == char_index) {
                            const factor: f32 = @as(f32, @floatFromInt(sub_text_count)) / @as(f32, @floatFromInt(g.text.len / g_factor));

                            // Use cluster's local bbox and apply transforms properly
                            const cluster_local_bbox = g.cluster.localBBox();
                            const cluster_transform = g.cluster.transform();

                            // Calculate character position within cluster
                            const char_width = cluster_local_bbox.width() / @as(f32, @floatFromInt(g.text.len / g_factor));
                            const char_offset = char_width * factor;

                            // Create character bbox in cluster local space
                            const char_local_bbox = math.rect2f(cluster_local_bbox.left() + char_offset, cluster_local_bbox.top(), char_width, cluster_local_bbox.height());

                            // Apply cluster transform, then block transform
                            const cluster_bbox = cluster_transform.transformRect(char_local_bbox);
                            return block.layout_xform.transformRect(cluster_bbox);
                        }
                        sub_text_count += 1;
                    }
                    total_text_count += g.text.len / g_factor;
                }
            }
        }
    }
    return null;
}
/// Get the bounding box of specific span.
pub fn spanBBox(block: Block, span_index: usize) ?math.Rect2f {
    if (block.layout) |layout| {
        const visual_spans = layout.visual_spans;
        if (span_index < visual_spans.items.len) {
            const span = visual_spans.items[span_index];
            return block.layout_xform.transformRect(span.bbox);
        }
    }
    return null;
}
/// Get the bounding box of specific line.
pub fn lineBBox(block: Block, line_index: usize) ?math.Rect2f {
    if (block.layout) |layout| {
        if (line_index < layout.lines.items.len) {
            const line = layout.lines.items[line_index];
            const p1 = block.layout_xform.transform(math.vec2f(line.x_offset, line.y_offset + @ceil((line.ascent - line.descent - line.height) * 0.5) - line.ascent));
            return math.rect2f(p1.x(), p1.y(), line.width, line.height);
        }
    }
    return null;
}
/// Get the bounding box of entire block.
pub fn blockBBox(block: Block) ?math.Rect2f {
    if (block.layout) |layout| {
        const block_bbox = layout.calcBlockBBox();
        return block.layout_xform.transformRect(block_bbox);
    }
    return null;
}
/// Get baseline of specific line.
pub fn baseline(block: Block, line_index: usize) ?math.Linef {
    if (block.layout) |layout| {
        if (line_index < layout.lines.items.len) {
            const line = layout.lines.items[line_index];
            const p1 = block.layout_xform.transform(math.vec2f(line.x_offset, line.y_offset));
            const p2 = block.layout_xform.transform(math.vec2f(line.x_offset + line.width, line.y_offset));
            return math.linef(p1.x(), p1.y(), p2.x(), p2.y());
        }
    }
    return null;
}

pub fn clearCache(block: *Block) void {
    if (block.layout) |layout| {
        layout.deinit();
    }
    block.layout = null;
}

test "lineCount" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();
    const font_id = try font_db.loadFromMemory(.{
        .family = "Sans",
    }, @embedFile("../OpenSans-subset.ttf"));
    const base_style = style.CascadeStyle.Flattened{
        .font = .{
            .family = "Sans",
        },
    };
    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var block = Block.init(allocator);
    defer block.deinit();
    try block.string.append("Hello\nWorld\n");
    try block.spans.append(.{
        .range = .{ .length = 11 },
        .style = .{},
    });
    try block.buildLayout(&font_db, base_style, 100.0);

    try std.testing.expectEqual(2, block.lineCount());
}

test "lineIndexAt" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();
    const font_id = try font_db.loadFromMemory(.{
        .family = "Sans",
    }, @embedFile("../OpenSans-subset.ttf"));
    const base_style = style.CascadeStyle.Flattened{
        .font = .{
            .family = "Sans",
        },
    };
    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var block = Block.init(std.testing.allocator);
    defer block.deinit();
    try block.string.append("Hello\nWorld\n");
    try block.spans.append(.{
        .range = .{ .length = 11 },
        .style = .{},
    });
    try block.buildLayout(&font_db, base_style, 100.0);

    try std.testing.expectEqual(1, block.lineIndexAt(5));
}

test "characterRangeAtLine" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();
    const font_id = try font_db.loadFromMemory(.{
        .family = "Sans",
    }, @embedFile("../OpenSans-subset.ttf"));
    const base_style = style.CascadeStyle.Flattened{
        .font = .{
            .family = "Sans",
        },
    };
    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var block = Block.init(allocator);
    defer block.deinit();
    try block.string.append("HelloWorld");
    try block.spans.append(.{
        .range = .{ .length = 11 },
        .style = .{},
    });
    try block.buildLayout(&font_db, base_style, 90.0);

    try std.testing.expectEqual(Range.init(0, 5), block.characterRangeAtLine(0));
    try std.testing.expectEqual(Range.init(5, 5), block.characterRangeAtLine(1));
}

test "blockBBox" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();
    const font_id = try font_db.loadFromMemory(.{
        .family = "Sans",
    }, @embedFile("../OpenSans-subset.ttf"));
    const base_style = style.CascadeStyle.Flattened{
        .font = .{
            .family = "Sans",
        },
    };
    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var block = Block.init(allocator);
    defer block.deinit();
    try block.string.append("HelloWorld");
    try block.spans.append(.{
        .range = .{ .length = 11 },
        .style = .{},
    });
    try block.buildLayout(&font_db, base_style, 90.0);

    const bbox = block.blockBBox().?;
    try std.testing.expectApproxEqAbs(0.0, bbox.left(), 0.001);
    try std.testing.expectApproxEqAbs(0.0, bbox.top(), 0.001);
    try std.testing.expectApproxEqAbs(89.07813, bbox.width(), 0.001);
    try std.testing.expectApproxEqAbs(87.15625, bbox.height(), 0.001);
}
