const NoteDoc = @This();

const std = @import("std");
const assert = std.debug.assert;
const math = @import("../math.zig");
const FontDB = @import("FontDB.zig");
const Range = @import("Range.zig");
const character = @import("character.zig");
const isWhitespaceAt = character.isWhitespaceAt;
const DynamicString = @import("DynamicString.zig");
const Block = @import("Block.zig");
const shaping = @import("shaping.zig");
const style = @import("style.zig");
const CascadeStyle = style.CascadeStyle;
const Span = style.Span;
const Path = @import("../Path.zig");

pub const NoteBlockKind = enum(u8) {
    paragraph,
    list,
    code,
};

// Quote, Head: Sohne
// Paragraph: Source Serif Pro
pub const ParagraphAttributes = struct {
    pub const Kind = enum(u8) {
        text,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        quote,
    };

    kind: Kind,

    pub fn baseStyle(attr: ParagraphAttributes) CascadeStyle.Flattened {
        return switch (attr.kind) {
            else => CascadeStyle.Flattened{
                .font = .{
                    .family = "Source Serif Pro",
                },
                .font_size = 20.0,
            },
            .h1 => CascadeStyle.Flattened{
                .font = .{
                    .family = "Neue Haas Unica",
                    .weight = .bold,
                },
                .font_size = 24.0,
            },
            .h2 => CascadeStyle.Flattened{
                .font = .{
                    .family = "Neue Haas Unica",
                    .weight = .bold,
                },
                .font_size = 20.0,
            },
            .h3 => CascadeStyle.Flattened{
                .font = .{
                    .family = "Neue Haas Unica",
                    .weight = .bold,
                },
                .font_size = 16.0,
            },
            .h4, .h5, .h6 => CascadeStyle.Flattened{
                .font = .{
                    .family = "Neue Haas Unica",
                    .weight = .bold,
                },
                .font_size = 12.0,
            },
        };
    }
};
pub const ListAttributes = struct {
    pub const Kind = enum(u8) {
        bulleted,
        numbered,
        todo,
    };

    kind: Kind,
    done: bool = false,

    pub fn baseStyle(_: ListAttributes) CascadeStyle.Flattened {
        return CascadeStyle.Flattened{
            .font = .{
                .family = "Source Serif Pro",
            },
            .font_size = 20.0,
        };
    }
};
pub const CodeAttributes = struct {
    lang: []const u8,

    pub fn baseStyle(_: CodeAttributes) CascadeStyle.Flattened {
        return CascadeStyle.Flattened{
            .font = .{
                .family = "Menlo",
            },
            .font_size = 20.0,
        };
    }
};

pub const NoteBlock = struct {
    kind: NoteBlockKind,
    indent: u8 = 0,
    attributes: union(NoteBlockKind) {
        paragraph: ParagraphAttributes,
        list: ListAttributes,
        code: CodeAttributes,
        pub fn baseStyle(attr: @This()) CascadeStyle.Flattened {
            return switch (attr) {
                .paragraph => |a| a.baseStyle(),
                .list => |a| a.baseStyle(),
                .code => |a| a.baseStyle(),
            };
        }
    },
    data: Block,
};
const NoteBlockList = std.ArrayList(NoteBlock);
const OffsetList = std.ArrayList(math.Vec2f);

const SpanList = std.ArrayList(Span);

const Selection = struct {
    start_block: usize,
    start_char: usize,
    end_block: usize,
    end_char: usize,
};

allocator: std.mem.Allocator,
blocks: NoteBlockList,
block_offsets: OffsetList,
spans: SpanList,

/// Current editing block
active_block_index: usize = 0,

/// Cursor inside current block's text
cursor_char_index: usize = 0,
selection: ?Selection = null,

_cached_doc_bbox: ?math.Rect2f = null,

pub fn init(allocator: std.mem.Allocator) NoteDoc {
    return NoteDoc{
        .allocator = allocator,
        .blocks = NoteBlockList.init(allocator),
        .block_offsets = OffsetList.init(allocator),
        .spans = SpanList.init(allocator),
    };
}
pub fn deinit(self: *NoteDoc) void {
    self.spans.deinit();
    self.block_offsets.deinit();
    for (self.blocks.items) |*b| {
        self.freeBlock(b);
    }
    self.blocks.deinit();
}

/// Returns the total number of characters in the given block.
pub fn characterCount(self: NoteDoc, block_index: usize) usize {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.characterCount();
}
/// How many lines in the given block.
pub fn lineCount(self: NoteDoc, block_index: usize) usize {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.lineCount();
}
/// How many spans in the given block.
pub fn spanCount(self: NoteDoc, block_index: usize) usize {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.spans.items.len;
}
/// Return the number of blocks.
pub fn blockCount(self: NoteDoc) usize {
    return self.blocks.items.len;
}

/// Line index at cursor
pub inline fn getLineIndexAtCursor(self: NoteDoc) usize {
    assert(self.active_block_index < self.blocks.items.len);
    return self.blocks.items[self.active_block_index].data.lineIndexAt(self.cursor_char_index);
}
/// Return range of characters in the given line.
pub inline fn characterRangeAtLine(self: NoteDoc, block_index: usize, line_index: usize) Range {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.characterRangeAtLine(line_index);
}
pub const GetTextInRangeOptions = struct {
    start_block: usize,
    start_char: usize,
    end_block: usize,
    end_char: usize,
    with_sentinel: bool = false,
};
/// Get text between two positions, potentially spanning multiple blocks.
///
/// - A line break is added between blocks.
/// - Sentinels can also be added with `with_sentinel` option.
/// - Caller owns returned memory.
pub fn getTextInRange(self: *NoteDoc, allocator: std.mem.Allocator, options: GetTextInRangeOptions) ![]const u8 {
    if (options.start_block > options.end_block or (options.start_block == options.end_block and options.start_char > options.end_char)) {
        return error.InvalidRange;
    }
    if (options.start_block >= self.blocks.items.len or options.end_block >= self.blocks.items.len) {
        return error.BlockOutOfRange;
    }
    var list = std.ArrayList(u8).init(allocator);
    errdefer list.deinit();
    // Single block case
    if (options.start_block == options.end_block) {
        const block = &self.blocks.items[options.start_block];
        const text = block.data.string.asSlice();
        const start_byte = try block.data.string.charIndexToByteOffset(options.start_char);
        const end_byte = try block.data.string.charIndexToByteOffset(options.end_char);
        try list.appendSlice(text[start_byte..end_byte]);
        if (options.with_sentinel) {
            try list.append(0);
        }
        return list.toOwnedSlice();
    }
    // Multiple blocks case
    // First block
    {
        const block = &self.blocks.items[options.start_block];
        const text = block.data.string.asSlice();
        const start_byte = try block.data.string.charIndexToByteOffset(options.start_char);
        try list.appendSlice(text[start_byte..]);
        try list.append('\n');
    }
    // Middle blocks
    for (options.start_block + 1..options.end_block) |i| {
        const block = &self.blocks.items[i];
        try list.appendSlice(block.data.string.asSlice());
        try list.append('\n');
    }
    // Last block
    {
        const block = &self.blocks.items[options.end_block];
        const text = block.data.string.asSlice();
        const end_byte = try block.data.string.charIndexToByteOffset(options.end_char);
        try list.appendSlice(text[0..end_byte]);
    }
    if (options.with_sentinel) {
        try list.append(0);
    }
    return list.toOwnedSlice();
}

test "getTextInRange - single block" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    const text = try doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 0,
        .end_block = 0,
        .end_char = 5,
        .with_sentinel = false,
    });
    defer std.testing.allocator.free(text);
    try std.testing.expectEqualStrings("First", text);
}

test "getTextInRange - multiple blocks" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    const text = try doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 6,
        .end_block = 1,
        .end_char = 6,
        .with_sentinel = false,
    });
    defer std.testing.allocator.free(text);
    try std.testing.expectEqualStrings("block\nSecond", text);
}

test "getTextInRange - full range" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    const text = try doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 0,
        .end_block = 2,
        .end_char = 11,
        .with_sentinel = false,
    });
    defer std.testing.allocator.free(text);
    try std.testing.expectEqualStrings("First block\nSecond block\nThird block", text);
}

test "getTextInRange - with sentinel" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    const text = try doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 0,
        .end_block = 2,
        .end_char = 11,
        .with_sentinel = true,
    });
    defer std.testing.allocator.free(text);
    try std.testing.expectEqualStrings("First block\nSecond block\nThird block" ++ &[_]u8{0}, text);
}

test "getTextInRange - range at end of block" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    const text = try doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 0,
        .end_block = 2,
        .end_char = 11,
        .with_sentinel = false,
    });
    defer std.testing.allocator.free(text);
    try std.testing.expectEqualStrings("First block\nSecond block\nThird block", text);
}

test "getTextInRange - error cases" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("First block", .text);
    try doc.appendParagraph("Second block", .text);
    try doc.appendParagraph("Third block", .text);

    try std.testing.expectError(error.InvalidRange, doc.getTextInRange(std.testing.allocator, .{
        .start_block = 1,
        .start_char = 0,
        .end_block = 0,
        .end_char = 0,
        .with_sentinel = false,
    }));
    try std.testing.expectError(error.BlockOutOfRange, doc.getTextInRange(std.testing.allocator, .{
        .start_block = 0,
        .start_char = 0,
        .end_block = 3,
        .end_char = 0,
        .with_sentinel = false,
    }));
}

/// Get the bounding box of character at given block and index.
pub fn characterBBox(self: NoteDoc, block_index: usize, char_index: usize) ?math.Rect2f {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.characterBBox(char_index);
}
/// Get the bounding box of span at given block and index.
pub fn spanBBox(self: NoteDoc, block_index: usize, span_index: usize) ?math.Rect2f {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.spanBBox(span_index);
}
/// Get the bounding box of line at given block and index.
pub fn lineBBox(self: NoteDoc, block_index: usize, line_index: usize) ?math.Rect2f {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.lineBBox(line_index);
}
/// Get the bounding box of block at given index.
pub fn blockBBox(self: NoteDoc, block_index: usize) ?math.Rect2f {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.blockBBox();
}
/// Get the bounding box of entire doc.
pub fn docBBox(self: *NoteDoc) ?math.Rect2f {
    if (self._cached_doc_bbox) |bbox| {
        return bbox;
    }

    var bbox: ?math.Rect2f = null;
    for (self.blocks.items, 0..) |*block, block_index| {
        const offset = self.blockOffset(block_index);
        if (block.data.blockBBox()) |b| {
            bbox = math.uniteBBox(bbox, math.rect2f(
                b.left() + offset.x(),
                b.top() + offset.y(),
                b.width(),
                b.height(),
            ));
        }
    }
    self._cached_doc_bbox = bbox;
    return bbox;
}

pub fn isPointInBlock(self: NoteDoc, x: f32, y: f32) bool {
    for (self.blocks.items, 0..) |*block, block_index| {
        const offset = self.blockOffset(block_index);
        if (block.data.blockBBox()) |b| {
            const bbox = math.rect2f(
                b.left() + offset.x(),
                b.top() + offset.y(),
                b.width(),
                b.height(),
            );
            if (bbox.contains(math.vec2f(x, y))) {
                return true;
            }
        }
    }
    return false;
}

/// Get the baseline of line at given block and index.
pub fn baseline(self: NoteDoc, block_index: usize, line_index: usize) ?math.Linef {
    assert(block_index < self.blocks.items.len);
    return self.blocks.items[block_index].data.baseline(line_index);
}

/// Get the offset of block at given index.
pub fn blockOffset(self: NoteDoc, block_index: usize) math.Vec2f {
    assert(block_index < self.block_offsets.items.len);
    return self.block_offsets.items[block_index];
}

/// Append a paragraph block.
pub fn appendParagraph(self: *NoteDoc, text: []const u8, kind: ParagraphAttributes.Kind) !void {
    var data = Block.init(self.allocator);
    try data.string.append(text);
    // Add a span to cover the entire range
    try data.spans.append(.{
        .range = .{ .length = text.len },
        .style = .{},
    });
    try self.blocks.append(NoteBlock{
        .kind = .paragraph,
        .attributes = .{ .paragraph = .{
            .kind = kind,
        } },
        .data = data,
    });
    // Add default doc level span if not already present
    try self.checkAndAddDefaultSpan();
}
/// Append a list block.
pub fn appendList(self: *NoteDoc, text: []const u8, kind: ListAttributes.Kind) !void {
    var data = Block.init(self.allocator);
    try data.string.append(text);
    // Add a span to cover the entire range
    try data.spans.append(.{
        .range = .{ .length = text.len },
        .style = .{},
    });
    try self.blocks.append(NoteBlock{
        .kind = .list,
        .attributes = .{ .list = .{
            .kind = kind,
        } },
        .data = data,
    });
    // Add default doc level span if not already present
    try self.checkAndAddDefaultSpan();
}
/// Append a code block.
pub fn appendCode(self: *NoteDoc, text: []const u8, lang: []const u8) !void {
    var data = Block.init(self.allocator);
    try data.string.append(text);
    // Add a span to cover the entire range
    try data.spans.append(.{
        .range = .{ .length = text.len },
        .style = .{},
    });
    try self.blocks.append(NoteBlock{
        .kind = .code,
        .attributes = .{ .code = .{
            .lang = try self.allocator.dupe(u8, lang),
        } },
        .data = data,
    });
    // Add default doc level span if not already present
    try self.checkAndAddDefaultSpan();
}
/// Remove and free the block at given index.
pub fn removeBlockAt(self: *NoteDoc, block_index: usize) void {
    assert(block_index < self.blocks.items.len);
    self.freeBlock(&self.blocks.items[block_index]);
    _ = self.blocks.orderedRemove(block_index);
}
inline fn freeBlock(self: *NoteDoc, block: *NoteBlock) void {
    switch (block.attributes) {
        .code => |code| {
            self.allocator.free(code.lang);
        },
        else => {},
    }
    block.data.deinit();
}

test "append paragraph/list/code blocks" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Paragraph.text", .text);
    try note_data.appendParagraph("Paragraph.h1", .h1);
    try note_data.appendParagraph("Paragraph.quote", .quote);
    try note_data.appendList("List.bulleted", .bulleted);
    try note_data.appendList("List.bulleted.indent", .bulleted);
    note_data.setIndentOf(note_data.blockCount() - 1, 1);
    try note_data.appendCode("defer note_data.deinit();", "zig");

    try std.testing.expectEqualStrings("Paragraph.text", note_data.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqualStrings("Paragraph.h1", note_data.blocks.items[1].data.string.asSlice());
    try std.testing.expectEqual(.paragraph, note_data.blocks.items[1].kind);
    try std.testing.expectEqual(.h1, note_data.blocks.items[1].attributes.paragraph.kind);
    try std.testing.expectEqualStrings("List.bulleted", note_data.blocks.items[3].data.string.asSlice());
    try std.testing.expectEqualStrings("zig", note_data.blocks.items[5].attributes.code.lang);
}

test "appended blocks always come with a default span" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Paragraph.text", .text);
    try note_data.appendParagraph("Paragraph.h1", .h1);
    try note_data.appendParagraph("Paragraph.quote", .quote);
    try note_data.appendList("List.bulleted", .bulleted);
    try note_data.appendList("List.bulleted.indent", .bulleted);
    note_data.setIndentOf(note_data.blockCount() - 1, 1);
    try note_data.appendCode("defer note_data.deinit();", "zig");

    try std.testing.expectEqual(1, note_data.blocks.items[0].data.spans.items.len);
    try std.testing.expectEqual(1, note_data.blocks.items[1].data.spans.items.len);
    try std.testing.expectEqual(1, note_data.blocks.items[2].data.spans.items.len);
    try std.testing.expectEqual(1, note_data.blocks.items[3].data.spans.items.len);
    try std.testing.expectEqual(1, note_data.blocks.items[4].data.spans.items.len);
    try std.testing.expectEqual(1, note_data.blocks.items[5].data.spans.items.len);
}

/// Add a default span if not already present.
fn checkAndAddDefaultSpan(self: *NoteDoc) !void {
    if (self.spans.items.len == 0) {
        try self.spans.append(.{
            .range = .{},
            .style = style.CascadeStyle{
                .font = .{
                    .family = "Source Serif Pro",
                    .style = .normal,
                    .weight = .regular,
                    .stretch = .normal,
                },
                .font_size = 20.0,
                .letter_spacing = 0.0,
                .word_spacing = 0.0,
            },
        });
    }
}

test "there are always default spans" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Paragraph.text", .text);
    try std.testing.expectEqual(1, note_data.spans.items.len);
}

/// Set the indent level of a specific block.
pub fn setIndentOf(self: *NoteDoc, block_index: usize, new_indent: u8) void {
    assert(block_index < self.blocks.items.len);
    self.blocks.items[block_index].indent = new_indent;
}

/// Move a block from `from_index` to before `target_index` maintaining order.
///
/// If `from_index < target_index`, after removal target block shifts left by one,
/// so we insert at `target_index - 1` to place before it.
/// If `from_index > target_index`, we insert at `target_index`.
pub fn moveBlockBefore(self: *NoteDoc, from_index: usize, target_index: usize) !void {
    if (from_index >= self.blocks.items.len or target_index >= self.blocks.items.len) {
        return error.OutOfRange;
    }
    if (from_index == target_index) return;
    const block = self.blocks.orderedRemove(from_index);
    const new_insert_index = if (from_index < target_index) (target_index - 1) else target_index;
    try self.blocks.insert(new_insert_index, block);
}

/// Move a block from `from_index` to after `target_index` maintaining order.
///
/// If `from_index < target_index`, after removal target block at (target_index - 1),
/// to place after target block, we insert at `target_index` (because target block now at target_index-1).
/// If `from_index > target_index`, we insert at `target_index + 1`.
pub fn moveBlockAfter(self: *NoteDoc, from_index: usize, target_index: usize) !void {
    if (from_index >= self.blocks.items.len or target_index >= self.blocks.items.len) {
        return error.OutOfRange;
    }
    if (from_index == target_index) return;
    const block = self.blocks.orderedRemove(from_index);
    const new_insert_index = if (from_index < target_index) target_index else (target_index + 1);
    try self.blocks.insert(new_insert_index, block);
}

/// Shape a single block at `block_index`.
///
/// Note that`block_offsets` won't be created/updated.
pub fn buildBlockLayout(self: *NoteDoc, font_db: *FontDB, block_index: usize, max_width: f32) !void {
    if (block_index >= self.blocks.items.len) return error.OutOfRange;
    var block = &self.blocks.items[block_index];
    // Make sure default span covers the entire block.
    block.data.spans.items[0].range.length = block.data.characterCount();
    try block.data.buildLayout(font_db, block.attributes.baseStyle(), max_width);
}
/// Shape all blocks, also calculate their offsets.
pub fn buildDocLayout(self: *NoteDoc, font_db: *FontDB, max_width: f32, max_height: f32) !void {
    self.block_offsets.clearRetainingCapacity();
    self._cached_doc_bbox = null; // Invalidate cached doc bbox
    var y_offset: f32 = 0.0;
    for (0..self.blocks.items.len) |block_index| {
        // Shape each block
        try self.buildBlockLayout(font_db, block_index, max_width);
        // Calculate offsets for each block since they are computed locally
        const offset = math.vec2f(0.0, y_offset);
        try self.block_offsets.append(offset);
        if (self.blocks.items[block_index].data.blockBBox()) |bbox| {
            y_offset += bbox.height();
        }
    }
    // alignment
    var align_width = max_width;
    if (align_width < 0) {
        align_width = self.docBBox().?.width();
    }
    const doc_height = self.docBBox().?.height();
    for (self.blocks.items) |*block| {
        try block.data.horizontalAlignBlock(block.attributes.baseStyle(), align_width);
        if (!(max_height < 0)) {
            try block.data.verticalAlignBlock(block.attributes.baseStyle(), doc_height, max_height);
        }
    }
}

test "moveBlockBefore" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Block0", .text);
    try note_data.appendParagraph("Block1", .text);
    try note_data.appendParagraph("Block2", .text);
    try note_data.appendParagraph("Block3", .text);

    // Move block at index 0 to before index 2
    try note_data.moveBlockBefore(0, 2);
    // Original: [Block0, Block1, Block2, Block3]
    // After move before(0,2): from_index=0 < target_index=2 => insert at target_index-1=1
    // Steps:
    // removeAt(0) stable: [Block1, Block2, Block3]
    // insertAt(1, Block0): [Block1, Block0, Block2, Block3]
    try std.testing.expectEqualStrings("Block1", note_data.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqualStrings("Block0", note_data.blocks.items[1].data.string.asSlice());
    try std.testing.expectEqualStrings("Block2", note_data.blocks.items[2].data.string.asSlice());
    try std.testing.expectEqualStrings("Block3", note_data.blocks.items[3].data.string.asSlice());
}

test "moveBlockAfter" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Block1", .text);
    try note_data.appendParagraph("Block0", .text);
    try note_data.appendParagraph("Block2", .text);
    try note_data.appendParagraph("Block3", .text);

    // Move block at index 3 (Block3) after index 1 (Block0)
    try note_data.moveBlockAfter(3, 1);
    // Current: [Block1, Block0, Block2, Block3]
    // from_index=3 > target_index=1 => new_insert_index = target_index+1=2
    // removeAt(3): remove Block3 stable: [Block1, Block0, Block2]
    // insertAt(2, Block3): [Block1, Block0, Block3, Block2]
    try std.testing.expectEqualStrings("Block1", note_data.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqualStrings("Block0", note_data.blocks.items[1].data.string.asSlice());
    try std.testing.expectEqualStrings("Block3", note_data.blocks.items[2].data.string.asSlice());
    try std.testing.expectEqualStrings("Block2", note_data.blocks.items[3].data.string.asSlice());
}

test "shapeBlock basic" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();

    const font_setting = FontDB.FontSetting{
        .family = "Sans",
    };
    const font_id = try font_db.loadFromMemory(font_setting, @embedFile("../OpenSans-subset.ttf"));

    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello", .text);
    // Override default font to "Sans".
    note_data.blocks.items[0].data.spans.items[0].style.font = font_setting;
    // Initially spans is one span covering entire text
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.spans.items.len);
    try std.testing.expectEqual(5, note_data.blocks.items[0].data.spans.items[0].range.length);
    try std.testing.expectEqual(null, note_data.blocks.items[0].data.layout);
    try note_data.buildBlockLayout(&font_db, 0, -1.0);
    try std.testing.expect(note_data.blocks.items[0].data.layout != null);
}

test "shapeBlock should work after blocks changed" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();

    const font_setting = FontDB.FontSetting{
        .family = "Sans",
    };
    const font_id = try font_db.loadFromMemory(font_setting, @embedFile("../OpenSans-subset.ttf"));

    try shaping.init(allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello", .text);
    // Override default font to "Sans".
    note_data.blocks.items[0].data.spans.items[0].style.font = font_setting;
    // Initially spans is one span covering entire text
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.spans.items.len);
    try std.testing.expectEqual(5, note_data.blocks.items[0].data.spans.items[0].range.length);
    try std.testing.expectEqual(null, note_data.blocks.items[0].data.layout);
    try note_data.buildBlockLayout(&font_db, 0, -1.0);
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.layout.?.visual_spans.items.len);

    // Modify text and shape again
    try note_data.blocks.items[0].data.string.append(" World");
    // Now length is 11 chars
    // shapeBlock should adjust the first span to cover entire text
    try note_data.buildBlockLayout(&font_db, 0, -1.0);
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.spans.items.len);
    try std.testing.expectEqual(11, note_data.blocks.items[0].data.spans.items[0].range.length);
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.layout.?.visual_spans.items.len);

    // Empty the text and shape again
    note_data.blocks.items[0].data.string.clear();
    try note_data.buildBlockLayout(&font_db, 0, -1.0);
    try std.testing.expect(note_data.blocks.items[0].data.layout != null);
    try std.testing.expectEqual(1, note_data.blocks.items[0].data.layout.?.visual_spans.items.len);
}

/// Move left by one character. If at start of block, move to end of previous block if exists.
pub fn moveCharLeft(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const current_block = &self.blocks.items[self.active_block_index];
    var count = current_block.data.characterCount();

    if (current_block.data.layout) |layout| {
        // If there are multiple lines, handle line wrapping
        if (layout.lines.items.len > 1) {
            if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
                const start = layout.lines.items[line_index].range.location;
                count = layout.lines.items[line_index].range.length;

                if (self.cursor_char_index >= start and self.cursor_char_index != 0) {
                    // Move left within the same line
                    self.cursor_char_index -= 1;
                } else if (line_index > 0) {
                    // Move to end of previous line
                    const prev_line = layout.lines.items[line_index - 1];
                    self.cursor_char_index = prev_line.range.location + prev_line.range.length;
                } else if (self.active_block_index > 0) {
                    // Move to end of previous block
                    self.active_block_index -= 1;
                    const prev_block = &self.blocks.items[self.active_block_index];
                    self.cursor_char_index = prev_block.data.characterCount();
                }
                return;
            }
        }
    }

    // Handle single line or no layout case
    if (self.cursor_char_index > 0) {
        self.cursor_char_index -= 1;
    } else if (self.active_block_index > 0) {
        self.active_block_index -= 1;
        const prev_block = &self.blocks.items[self.active_block_index];
        self.cursor_char_index = prev_block.data.characterCount();
    }
}
/// Move right by one character. If at end of block, move to start of next block if exists.
pub fn moveCharRight(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const current_block = &self.blocks.items[self.active_block_index];
    var count = current_block.data.characterCount();

    if (current_block.data.layout) |layout| {
        // If there are multiple lines, handle line wrapping
        if (layout.lines.items.len > 1) {
            if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
                const start = layout.lines.items[line_index].range.location;
                count = layout.lines.items[line_index].range.length;

                if (self.cursor_char_index < start + count) {
                    // Move right within the same line
                    self.cursor_char_index += 1;
                } else if (line_index + 1 < layout.lines.items.len) {
                    // Move to start of next line
                    const next_line = layout.lines.items[line_index + 1];
                    self.cursor_char_index = next_line.range.location;
                } else if (self.active_block_index + 1 < self.blocks.items.len) {
                    // Move to start of next block
                    self.active_block_index += 1;
                    self.cursor_char_index = 0;
                }
                return;
            }
        }
    }

    // Handle single line or no layout case
    if (self.cursor_char_index < count) {
        self.cursor_char_index += 1;
    } else if (self.active_block_index + 1 < self.blocks.items.len) {
        self.active_block_index += 1;
        self.cursor_char_index = 0;
    }
}
/// Move up visually by one line. If the line is wrapped, moves up within the same block.
/// Otherwise moves to the previous block. Maintains the visual column position.
pub fn moveVisualLineUp(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    // If we have layout info and are not on first line of block
    if (block.data.layout) |layout| {
        if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
            if (line_index > 0) {
                // Move up within same block
                const prev_line = layout.lines.items[line_index - 1];
                // Get current character's bbox to get target x position
                const end_idx = layout.lines.items[line_index].range.location + layout.lines.items[line_index].range.length;
                // if the cursor is at the end of the line, move to the previous character to get the last character's bbox
                const char_idx = if (end_idx == self.cursor_char_index) self.cursor_char_index - 1 else self.cursor_char_index;
                if (block.data.characterBBox(char_idx)) |bbox| {
                    const target_x = bbox.center().x();
                    const cursor_x = if (self.cursor_char_index != 0) bbox.right() else bbox.left();
                    // Find closest character in previous line by checking bboxes
                    var closest_char = prev_line.range.start();
                    var min_dist: f32 = std.math.inf(f32);
                    for (prev_line.range.start()..prev_line.range.end()) |ci| {
                        if (block.data.characterBBox(ci)) |char_bbox| {
                            const dist = @abs(char_bbox.center().x() - target_x);
                            if (dist < min_dist) {
                                if (cursor_x > char_bbox.center().x()) {
                                    closest_char = ci + 1;
                                } else {
                                    closest_char = ci;
                                }
                                min_dist = dist;
                            }
                        }
                    }
                    self.cursor_char_index = closest_char;
                }
                return;
            } else if ((self.active_block_index == 0) and (line_index == 0)) {
                // If we're at the first block and first line, move to beginning of the block
                self.cursor_char_index = 0;
                return;
            }
        }
    }
    // Otherwise try moving to previous block
    if (self.active_block_index > 0) {
        self.active_block_index -= 1;
        const prev_block = &self.blocks.items[self.active_block_index];
        // if the previous block has multiple lines, we need to get the start and end of the last line
        const line_count = prev_block.data.lineCount();
        var start: usize = 0;
        var end = prev_block.data.characterCount();
        if (line_count > 1) {
            if (prev_block.data.layout) |layout| {
                start = layout.lines.items[layout.lines.items.len - 1].range.location;
                end = layout.lines.items[layout.lines.items.len - 1].range.location + layout.lines.items[layout.lines.items.len - 1].range.length;
            }
        }

        // if the cursor is at the start of the block, use the first character's bbox
        // otherwise use the previous character's bbox for the target x position
        const char_idx = if (self.cursor_char_index == 0) self.cursor_char_index else self.cursor_char_index - 1;

        // Get current character's bbox to get target x position
        if (block.data.characterBBox(char_idx)) |bbox| {
            // if the cursor is at the start of the block, use the left edge of the bbox
            // otherwise use the center of the bbox
            const target_x = if (self.cursor_char_index != 0) bbox.center().x() else bbox.left();
            const cursor_x = if (self.cursor_char_index != 0) bbox.right() else bbox.left();
            // Find closest character in previous block by checking bboxes
            var closest_char: usize = end; //prev_block.data.characterCount();
            var min_dist: f32 = std.math.inf(f32);
            const char_count = end; //prev_block.data.characterCount();
            for (start..end) |ci| {
                if (prev_block.data.characterBBox(ci)) |char_bbox| {
                    const dist = @abs(char_bbox.center().x() - target_x);
                    if (dist < min_dist) {
                        if (cursor_x > char_bbox.center().x()) {
                            closest_char = ci + 1;
                        } else {
                            closest_char = ci;
                        }
                        min_dist = dist;
                    }
                    // If we're at the last character, check if it's more close to right edge of bbox
                    if (ci == char_count - 1) {
                        const last_char_dist = @abs(char_bbox.right() - target_x);
                        if (last_char_dist < min_dist) {
                            if (cursor_x > char_bbox.center().x()) {
                                closest_char = ci + 1;
                            } else {
                                closest_char = ci;
                            }
                            min_dist = last_char_dist;
                        }
                    }
                }
            }
            self.cursor_char_index = closest_char;
        }
    }
}
/// Move down visually by one line. If the line is wrapped, moves down within the same block.
/// Otherwise moves to the next block. Maintains the visual column position.
pub fn moveVisualLineDown(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    // If we have layout info and are not on last line of block
    if (block.data.layout) |layout| {
        if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
            if (line_index < layout.lines.items.len - 1) {
                // Move down within same block
                const next_line = layout.lines.items[line_index + 1];
                // Get current character's bbox to get target x position
                if (block.data.characterBBox(self.cursor_char_index)) |bbox| {
                    const target_x = bbox.center().x();
                    const cursor_x = if (self.cursor_char_index != 0) bbox.right() else bbox.left();
                    // Find closest character in next line by checking bboxes
                    var closest_char = next_line.range.start();
                    var min_dist: f32 = std.math.inf(f32);
                    for (next_line.range.start()..next_line.range.end()) |ci| {
                        if (block.data.characterBBox(ci)) |char_bbox| {
                            const dist = @abs(char_bbox.center().x() - target_x);
                            if (dist < min_dist) {
                                if (cursor_x > char_bbox.center().x()) {
                                    closest_char = ci + 1;
                                } else {
                                    closest_char = ci;
                                }
                                min_dist = dist;
                            }
                        }
                    }
                    self.cursor_char_index = closest_char;
                }
                return;
            } else if ((self.active_block_index == self.blocks.items.len - 1) and (line_index == layout.lines.items.len - 1)) {
                // If we're at the last block and last line, move to end of the block
                self.cursor_char_index = block.data.characterCount();
                return;
            }
        }
    }
    // Otherwise try moving to next block
    if (self.active_block_index + 1 < self.blocks.items.len) {
        self.active_block_index += 1;
        const next_block = &self.blocks.items[self.active_block_index];
        // Get current character's bbox to get target x position
        const prev_block = &self.blocks.items[self.active_block_index - 1];
        const char_idx = if (self.cursor_char_index == 0) self.cursor_char_index else self.cursor_char_index - 1;
        if (prev_block.data.characterBBox(char_idx)) |bbox| {
            const target_x = if (self.cursor_char_index != 0) bbox.center().x() else bbox.left();
            const cursor_x = if (self.cursor_char_index != 0) bbox.right() else bbox.left();
            // Find closest character in next block by checking bboxes
            var closest_char: usize = next_block.data.characterCount();
            var min_dist: f32 = std.math.inf(f32);
            const char_count = next_block.data.characterCount();
            for (0..char_count) |ci| {
                if (next_block.data.characterBBox(ci)) |char_bbox| {
                    const dist = @abs(char_bbox.center().x() - target_x);
                    if (dist < min_dist) {
                        if (cursor_x > char_bbox.center().x()) {
                            closest_char = ci + 1;
                        } else {
                            closest_char = ci;
                        }
                        min_dist = dist;
                    }
                    // If we're at the last character, check if it's more close to right edge of bbox
                    if (ci == char_count - 1) {
                        const last_char_dist = @abs(char_bbox.right() - target_x);
                        if (last_char_dist < min_dist) {
                            if (cursor_x > char_bbox.center().x()) {
                                closest_char = char_count;
                            } else {
                                closest_char = ci;
                            }
                            min_dist = last_char_dist;
                        }
                    }
                }
            }
            self.cursor_char_index = closest_char;
        }
    }
}

test "moveVisualLineUp" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello", .text);
    try note_data.appendParagraph("World", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 2; // "Wo|rld"
    note_data.moveVisualLineUp();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(2, note_data.cursor_char_index); // "He|llo"
}

test "moveVisualLineDown" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello", .text);
    try note_data.appendParagraph("World", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 2; // "He|llo"
    note_data.moveVisualLineDown();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(2, note_data.cursor_char_index); // "Wo|rld"
}

/// Move to the start of the previous word.
pub fn movePrevWordStart(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    var cursor = self.cursor_char_index;
    const text = block.data.string.asSlice();

    // If at start of block or at start of first word, try previous block
    if ((cursor == 0 or (cursor < text.len and cursor == firstNonWhitespaceInBlock(text))) and self.active_block_index > 0) {
        self.active_block_index -= 1;
        block = &self.blocks.items[self.active_block_index];
        const prev_text = block.data.string.asSlice();
        cursor = block.data.characterCount();
        // Skip trailing whitespace in previous block
        while (cursor > 0 and isWhitespaceAt(prev_text, cursor - 1)) : (cursor -= 1) {}
        // Skip word to its start
        while (cursor > 0 and !isWhitespaceAt(prev_text, cursor - 1)) : (cursor -= 1) {}
    } else if (cursor > 0) {
        // If in middle/end of word, first try to move to start of current word
        const in_word = cursor < text.len and !isWhitespaceAt(text, cursor - 1);
        if (in_word) {
            // Move backward until start of current word
            while (cursor > 0 and !isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
            // If already at start of word, continue to previous word
            if (cursor == self.cursor_char_index) {
                // Skip whitespace backward
                while (cursor > 0 and isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
                // Move to start of previous word
                while (cursor > 0 and !isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
            }
        } else {
            // In whitespace, skip it backward
            while (cursor > 0 and isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
            // Move to start of previous word
            while (cursor > 0 and !isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
        }
    }
    self.cursor_char_index = cursor;
}

fn firstNonWhitespaceInBlock(text: []const u8) usize {
    var i: usize = 0;
    while (i < text.len and isWhitespaceAt(text, i)) : (i += 1) {}
    return i;
}

test "movePrevWordStart behavior" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("abc def   ghi", .text);
    note_data.active_block_index = 0;

    // From middle of word: moves to start of current word
    note_data.cursor_char_index = 5; // "abc d|ef   ghi"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(4, note_data.cursor_char_index); // "abc |def   ghi"

    // From start of word: moves to start of previous word
    note_data.cursor_char_index = 4; // "abc |def   ghi"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(0, note_data.cursor_char_index); // "|abc def   ghi"

    // From whitespace: moves to start of previous word
    note_data.cursor_char_index = 8; // "abc def | ghi"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(4, note_data.cursor_char_index); // "abc |def   ghi"

    // Test across blocks
    try note_data.appendParagraph("jkl mno", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 0; // at start of second block
    note_data.movePrevWordStart();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(10, note_data.cursor_char_index); // "abc def  |ghi"
}

/// Move to end of previous word.
pub fn movePrevWordEnd(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    var cursor = self.cursor_char_index;
    const text = block.data.string.asSlice();

    // If at start of block or in leading whitespace, try previous block
    if ((cursor == 0 or (cursor < text.len and isWhitespaceAt(text, cursor - 1))) and self.active_block_index > 0) {
        self.active_block_index -= 1;
        block = &self.blocks.items[self.active_block_index];
        const prev_text = block.data.string.asSlice();
        cursor = block.data.characterCount();
        // Skip trailing whitespace
        while (cursor > 0 and isWhitespaceAt(prev_text, cursor - 1)) : (cursor -= 1) {}
    } else if (cursor > 0) {
        cursor -= 1;
        // Skip whitespace backward
        while (cursor > 0 and isWhitespaceAt(text, cursor)) : (cursor -= 1) {}
        // If we're in a word, move to its start
        while (cursor > 0 and !isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
        // Then move to previous word end if we're not at the first word
        if (cursor > 0) {
            // Skip whitespace backward
            while (cursor > 0 and isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
            // Find start of word
            while (cursor > 0 and !isWhitespaceAt(text, cursor - 1)) : (cursor -= 1) {}
            // Move to end of word
            while (cursor < text.len and !isWhitespaceAt(text, cursor)) : (cursor += 1) {}
        }
    }
    self.cursor_char_index = cursor;
}

test "word movement - comprehensive" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("  first   second  third", .text);
    note_data.active_block_index = 0;

    // moveNextWordStart
    note_data.cursor_char_index = 0; // "  |first   second  third"
    note_data.moveNextWordStart();
    try std.testing.expectEqual(2, note_data.cursor_char_index); // "|first   second  third"

    note_data.cursor_char_index = 3; // "  f|irst   second  third"
    note_data.moveNextWordStart();
    try std.testing.expectEqual(10, note_data.cursor_char_index); // "  first   |second  third"

    // moveNextWordEnd
    note_data.cursor_char_index = 2; // "  |first   second  third"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(7, note_data.cursor_char_index); // "  first|   second  third"

    note_data.cursor_char_index = 10; // "  first   |second  third"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(16, note_data.cursor_char_index); // "  first   second|  third"

    // movePrevWordStart
    note_data.cursor_char_index = 16; // "  first   second|  third"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(10, note_data.cursor_char_index); // "  first   |second  third"

    note_data.cursor_char_index = 12; // "  first   se|cond  third"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(10, note_data.cursor_char_index); // "  first   |second  third"

    // movePrevWordEnd
    note_data.cursor_char_index = 16; // "  first   second|  third"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(7, note_data.cursor_char_index); // "  first|   second  third"

    note_data.cursor_char_index = 12; // "  first   se|cond  third"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(7, note_data.cursor_char_index); // "  first|   second  third"
}

test "word movement - cross block boundaries" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("first end", .text);
    try note_data.appendParagraph("  second line", .text);

    // moveNextWordStart across blocks
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 8; // "first en|d"
    note_data.moveNextWordStart();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(2, note_data.cursor_char_index); // "  |second line"

    // moveNextWordEnd across blocks
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 8; // "first en|d"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(9, note_data.cursor_char_index); // "first end|"

    // movePrevWordStart across blocks
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 2; // "  |second line"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(6, note_data.cursor_char_index); // "first |end"

    // movePrevWordEnd across blocks
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 2; // "  |second line"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(9, note_data.cursor_char_index); // "first end|"
}

/// Move to start of next word.
pub fn moveNextWordStart(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    var cursor = self.cursor_char_index;
    const text = block.data.string.asSlice();
    const length = text.len;

    // Skip current word if in one
    while (cursor < length and !isWhitespaceAt(text, cursor)) : (cursor += 1) {}
    // Skip whitespace
    while (cursor < length and isWhitespaceAt(text, cursor)) : (cursor += 1) {}

    // If we hit end of block, try next block
    if (cursor >= length and self.active_block_index + 1 < self.blocks.items.len) {
        self.active_block_index += 1;
        block = &self.blocks.items[self.active_block_index];
        const next_text = block.data.string.asSlice();
        cursor = 0;
        // Skip whitespace at start of next block
        while (cursor < next_text.len and isWhitespaceAt(next_text, cursor)) : (cursor += 1) {}
    }
    self.cursor_char_index = cursor;
}
/// Move to the end of the next word.
pub fn moveNextWordEnd(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    var cursor = self.cursor_char_index;
    const text = block.data.string.asSlice();
    const length = text.len;

    // If at end of block, try next block
    if (cursor >= length and self.active_block_index + 1 < self.blocks.items.len) {
        self.active_block_index += 1;
        block = &self.blocks.items[self.active_block_index];
        const next_text = block.data.string.asSlice();
        cursor = 0;
        // Skip whitespace at start of next block
        while (cursor < next_text.len and isWhitespaceAt(next_text, cursor)) : (cursor += 1) {}
        // Move to end of first word
        while (cursor < next_text.len and !isWhitespaceAt(next_text, cursor)) : (cursor += 1) {}
    } else if (cursor < length) {
        // If in whitespace, skip to next word
        if (isWhitespaceAt(text, cursor)) {
            while (cursor < length and isWhitespaceAt(text, cursor)) : (cursor += 1) {}
        }
        // Move to end of current/next word
        while (cursor < length and !isWhitespaceAt(text, cursor)) : (cursor += 1) {}
    }
    self.cursor_char_index = cursor;
}
/// Move cursor to the start of the current block.
pub fn moveCursorToBlockStart(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    self.cursor_char_index = 0;
}
/// Move cursor to the end of the current block.
pub fn moveCursorToBlockEnd(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const cur_block = &self.blocks.items[self.active_block_index];
    self.cursor_char_index = cur_block.data.characterCount();
}
/// Go to line start
pub fn gotoLineStart(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    self.cursor_char_index = 0;
}
/// Go to line end
pub fn gotoVisualLineEnd(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    if (block.data.layout) |layout| {
        const line_index = block.data.lineIndexAtCursor(self.cursor_char_index);
        self.cursor_char_index = layout.lines.items[line_index].range.end();
    }
}
/// Go to line end
pub fn gotoLineEnd(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    if (block.data.layout) |layout| {
        if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
            if (line_index > 0) {
                self.cursor_char_index = layout.lines.items[line_index].range.location + layout.lines.items[line_index].range.length;
            } else {
                self.cursor_char_index = block.data.characterCount();
            }
        }
    }
}
/// Goto first non whitespace in line
pub fn gotoFirstNonWhitespace(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    const text = block.data.string.asSlice();
    var cursor: usize = 0;
    while (cursor < text.len and isWhitespaceAt(text, cursor)) : (cursor += 1) {}
    self.cursor_char_index = cursor;
}

test "moveCharLeft and moveCharRight" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 0;

    // moveCharLeft at start does nothing (no previous block)
    note_data.moveCharLeft();
    try std.testing.expectEqual(0, note_data.cursor_char_index);

    // move cursor to end
    note_data.cursor_char_index = 5;
    note_data.moveCharRight(); // at end
    try std.testing.expectEqual(5, note_data.cursor_char_index);

    // move left one char
    note_data.moveCharLeft();
    try std.testing.expectEqual(4, note_data.cursor_char_index);
}

test "movePrevWordStart" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Test basic word movement
    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 11; // at end of "world"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(6, note_data.cursor_char_index); // start of "world"

    // Test with multiple spaces
    try note_data.appendParagraph("Hello   world", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 11; // at end of "world"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(8, note_data.cursor_char_index); // start of "world" after spaces

    // Test with punctuation
    try note_data.appendParagraph("Hello, world!", .text);
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 12; // at end
    note_data.movePrevWordStart();
    try std.testing.expectEqual(7, note_data.cursor_char_index); // start of "world"

    // Test at start of block
    note_data.cursor_char_index = 0;
    note_data.movePrevWordStart();
    try std.testing.expectEqual(8, note_data.cursor_char_index); // start of "world" of previous block
}

test "movePrevWordEnd" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Test basic word movement
    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 7; // at 'o' in "world"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"

    // Test with multiple spaces
    try note_data.appendParagraph("Hello   world", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 9; // in "world"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"

    // FIXME: Test with punctuation
    // try note_data.appendParagraph("Hello, world!", .text);
    // note_data.active_block_index = 2;
    // note_data.cursor_char_index = 8; // in "world"
    // note_data.movePrevWordEnd();
    // try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"
}

test "moveNextWordStart" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Test basic word movement
    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 0; // at 'H'
    note_data.moveNextWordStart();
    try std.testing.expectEqual(6, note_data.cursor_char_index); // start of "world"

    // Test with multiple spaces
    try note_data.appendParagraph("Hello   world", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordStart();
    try std.testing.expectEqual(8, note_data.cursor_char_index); // start of "world" after spaces

    // Test with punctuation
    try note_data.appendParagraph("Hello, world!", .text);
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordStart();
    try std.testing.expectEqual(7, note_data.cursor_char_index); // start of "world"

    // Test at end of block
    note_data.cursor_char_index = 12; // at end
    note_data.moveNextWordStart();
    // FIXME: This is wrong, should stay at end (punctuation not handled)
    try std.testing.expectEqual(13, note_data.cursor_char_index);
    // try std.testing.expectEqual(12, note_data.cursor_char_index); // stays at end
}

test "moveNextWordEnd" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Test basic word movement
    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(11, note_data.cursor_char_index); // end of "world"

    // Test with multiple spaces
    try note_data.appendParagraph("Hello   world", .text);
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(13, note_data.cursor_char_index); // end of "world"

    // Test with punctuation
    try note_data.appendParagraph("Hello, world!", .text);
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordEnd();
    // FIXME: This is wrong, should be 5 (no punctuation handled)
    try std.testing.expectEqual(6, note_data.cursor_char_index); // end of "Hello"
    // try std.testing.expectEqual(5, note_data.cursor_char_index); // end of "Hello"
    note_data.moveNextWordEnd();
    // FIXME: This is wrong, should be 12 (no punctuation handled)
    try std.testing.expectEqual(13, note_data.cursor_char_index); // end of "world!"
    // try std.testing.expectEqual(12, note_data.cursor_char_index); // end of "world!"
}

test "word movement - single block" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;

    // Test moveNextWordStart
    note_data.cursor_char_index = 0; // "H|ello world"
    note_data.moveNextWordStart();
    try std.testing.expectEqual(6, note_data.cursor_char_index); // "Hello |world"

    // Test moveNextWordEnd
    note_data.cursor_char_index = 0; // "H|ello world"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // "Hello| world"

    // Test movePrevWordStart
    note_data.cursor_char_index = 11; // "Hello world|"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(6, note_data.cursor_char_index); // "Hello |world"

    // Test movePrevWordEnd
    note_data.cursor_char_index = 11; // "Hello world|"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(5, note_data.cursor_char_index); // "Hello| world"
}

test "word movement - multiple blocks" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("First block", .text);
    try note_data.appendParagraph("Second block", .text);
    try note_data.appendParagraph("Third block", .text);

    // Test moveNextWordStart across blocks
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 10; // "First block|"
    note_data.moveNextWordStart();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(0, note_data.cursor_char_index); // "|Second block"

    // Test moveNextWordEnd across blocks
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 11; // "First block|"
    note_data.moveNextWordEnd();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(6, note_data.cursor_char_index); // "Second| block"

    // Test movePrevWordStart across blocks
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 0; // "|Second block"
    note_data.movePrevWordStart();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(6, note_data.cursor_char_index); // "First |block"

    // Test movePrevWordEnd across blocks
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 0; // "|Second block"
    note_data.movePrevWordEnd();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(11, note_data.cursor_char_index); // "First block|"
}

test "word movement - edge cases" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Test with empty blocks
    try note_data.appendParagraph("", .text);
    try note_data.appendParagraph("Next", .text);

    note_data.active_block_index = 0;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordStart();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(0, note_data.cursor_char_index);

    // Test with multiple spaces
    try note_data.appendParagraph("Word1    Word2", .text);
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 0;
    note_data.moveNextWordStart();
    try std.testing.expectEqual(9, note_data.cursor_char_index); // Skip multiple spaces

    // Test at document boundaries
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 0;
    note_data.movePrevWordStart(); // Should stay at start
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(0, note_data.cursor_char_index);

    note_data.active_block_index = 2;
    note_data.cursor_char_index = 15; // End of "Word1    Word2"
    note_data.moveNextWordStart(); // Should stay at end
    try std.testing.expectEqual(2, note_data.active_block_index);
    try std.testing.expectEqual(15, note_data.cursor_char_index);
}

test "gotoLineStart and gotoLineEnd" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("ABC", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 2; // "AB|C"

    note_data.gotoLineStart();
    try std.testing.expectEqual(0, note_data.cursor_char_index);

    note_data.gotoLineEnd();
    try std.testing.expectEqual(3, note_data.cursor_char_index);
}

test "moveVisualLineUp and moveVisualLineDown complex case" {
    var font_db = FontDB.init(std.testing.allocator);
    defer font_db.deinit();
    const attr = ParagraphAttributes{ .kind = .text };
    const font_id = try font_db.loadFromMemory(attr.baseStyle().font, @embedFile("../OpenSans-subset.ttf"));
    try shaping.init(std.testing.allocator, font_db.getPtrByID(font_id).?);
    defer shaping.deinit();

    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    // Create a document with varying line lengths and multiple paragraphs
    try note_data.appendParagraph("Short", .text);
    try note_data.appendParagraph("This is a much longer line that spans more characters", .text);
    try note_data.appendParagraph("Medium length line", .text);
    try note_data.appendParagraph("Tiny", .text);
    try note_data.appendParagraph("Another very long line to test cursor position preservation", .text);

    try note_data.buildDocLayout(&font_db, -1.0);
    try std.testing.expectEqual(5, note_data.blocks.items.len);

    // Test Case 1: Moving from long line to short line
    note_data.active_block_index = 1;
    note_data.cursor_char_index = 30; // "...line tha|t spans..."
    note_data.moveVisualLineUp();
    try std.testing.expectEqual(0, note_data.active_block_index);
    try std.testing.expectEqual(5, note_data.cursor_char_index); // Should clamp to "Short" length ("Short|")

    // Test Case 2: Moving from short line to long line
    note_data.moveVisualLineDown();
    try std.testing.expectEqual(1, note_data.active_block_index);
    // FIXME: This should restore original position since it was at the end of the line before moving down
    // try std.testing.expectEqual(30, note_data.cursor_char_index); // Should restore original position
    try std.testing.expectEqual(5, note_data.cursor_char_index); // "This |is a much..."

    // Test Case 3: Multiple moves up from bottom
    note_data.active_block_index = 4;
    note_data.cursor_char_index = 20;
    note_data.moveVisualLineUp(); // To "Tiny"
    try std.testing.expectEqual(3, note_data.active_block_index);
    try std.testing.expectEqual(4, note_data.cursor_char_index); // Clamped to "Tiny" length

    note_data.moveVisualLineUp(); // To "Medium length line"
    try std.testing.expectEqual(2, note_data.active_block_index);
    // FIXME: This should restore original position since it was at the end of the line before moving down
    // try std.testing.expectEqual(17, note_data.cursor_char_index); // Should use full length
    try std.testing.expectEqual(4, note_data.cursor_char_index);

    // Test Case 4: Moving up at start of line
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 0;
    note_data.moveVisualLineUp();
    try std.testing.expectEqual(1, note_data.active_block_index);
    try std.testing.expectEqual(0, note_data.cursor_char_index); // Should stay at start

    // Test Case 5: Moving down at end of line
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 17; // End of "Medium length line"
    note_data.moveVisualLineDown();
    try std.testing.expectEqual(3, note_data.active_block_index);
    try std.testing.expectEqual(4, note_data.cursor_char_index); // Should clamp to "Tiny" length

    // Test Case 6: Moving up at first line and down at last line
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 2;
    note_data.moveVisualLineUp();
    try std.testing.expectEqual(0, note_data.active_block_index); // Should stay at first line
    try std.testing.expectEqual(0, note_data.cursor_char_index); // Should move to start of block

    note_data.active_block_index = 4;
    note_data.cursor_char_index = 25;
    note_data.moveVisualLineDown();
    try std.testing.expectEqual(4, note_data.active_block_index); // Should stay at last line
    try std.testing.expectEqual(59, note_data.cursor_char_index); // Should stay at end of block
}

/// Get selection range in specific block (if any)
pub fn getSelectionRange(doc: *NoteDoc, block_index: usize) ?Range {
    // No active selection
    if (doc.selection == null) {
        return null;
    }
    const selection = doc.selection.?;
    var start_block = selection.start_block;
    var start_char = selection.start_char;
    var end_block = selection.end_block;
    var end_char = selection.end_char;
    // Ensure (start_block, start_char) <= (end_block, end_char)
    if (start_block > end_block or (start_block == end_block and start_char > end_char)) {
        // swap
        const tmp_block = start_block;
        const tmp_char = start_char;
        start_block = end_block;
        start_char = end_char;
        end_block = tmp_block;
        end_char = tmp_char;
    }
    // If block_index is outside [start_block..end_block], no selection in this block
    if (block_index < start_block or block_index > end_block) {
        return null;
    }
    // Now compute partial sub-range in this block
    const block_char_count = doc.characterCount(block_index);
    var block_sel_start: usize = 0;
    var block_sel_end: usize = block_char_count;
    if (block_index == start_block) {
        block_sel_start = start_char;
    }
    if (block_index == end_block) {
        block_sel_end = end_char;
    }
    if (block_sel_start > block_char_count) {
        block_sel_start = block_char_count;
    }
    if (block_sel_end > block_char_count) {
        block_sel_end = block_char_count;
    }
    if (block_sel_end <= block_sel_start) {
        return null; // No real selection in this block
    }
    return Range{
        .location = block_sel_start,
        .length = block_sel_end - block_sel_start,
    };
}
/// Clear all selections
pub fn clearSelection(doc: *NoteDoc) void {
    doc.selection = null;
}
/// Set selection
pub fn selectRange(doc: *NoteDoc, start_block: usize, start_char: usize, end_block: usize, end_char: usize) void {
    doc.selection = .{
        .start_block = start_block,
        .start_char = start_char,
        .end_block = end_block,
        .end_char = end_char,
    };
}
/// Check if there is a selection
pub fn hasSelection(doc: NoteDoc) bool {
    return doc.selection != null;
}
/// Start or continue selection to left
pub fn extendCharLeft(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    }

    const extend_end = self.selection.?.end_char == self.cursor_char_index and self.selection.?.end_block == self.active_block_index;
    // Move cursor left
    self.moveCharLeft();
    // Update selection_end to the new cursor
    if (extend_end) {
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = self.cursor_char_index;
    } else {
        self.selection.?.start_block = self.active_block_index;
        self.selection.?.start_char = self.cursor_char_index;
    }
}
/// Start or continue selection to previous word start
pub fn extendPrevWordStart(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    } else {
        // set cursor to start of selection
        self.cursor_char_index = self.selection.?.start_char;
    }
    // Move cursor to previous word start
    self.movePrevWordStart();
    // Update selection_end to the new cursor
    self.selection.?.start_block = self.active_block_index;
    self.selection.?.start_char = self.cursor_char_index;
}
/// Start or continue selection to next word end
pub fn extendNextWordEnd(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    } else {
        // set cursor to end of selection
        self.cursor_char_index = self.selection.?.end_char;
    }
    // Move cursor to next word end
    self.moveNextWordEnd();
    // Update selection_end to the new cursor
    self.selection.?.end_block = self.active_block_index;
    self.selection.?.end_char = self.cursor_char_index;
}
/// Start or continue selection to right
pub fn extendCharRight(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    }

    const extend_end = self.selection.?.end_char == self.cursor_char_index and self.selection.?.end_block == self.active_block_index;
    // Move cursor right
    self.moveCharRight();
    // Update selection_end to the new cursor
    if (extend_end) {
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = self.cursor_char_index;
    } else {
        self.selection.?.start_block = self.active_block_index;
        self.selection.?.start_char = self.cursor_char_index;
    }
}
/// Select inside the current word (select the entire word under the cursor).
///
/// Finds the word boundaries around the current cursor and sets selection start/end.
pub fn selectWordInner(self: *NoteDoc) void {
    const block_idx = self.active_block_index;
    const block = &self.blocks.items[block_idx];
    const text = block.data.string.asSlice();
    var start = self.cursor_char_index;
    var end = self.cursor_char_index;
    const len = text.len;
    if (len == 0) {
        // Empty block, no selection
        self.selection = null;
        return;
    }
    if (start > 0) {
        while (start > 0 and !isWhitespaceAt(text, start - 1)) : (start -= 1) {}
    }
    while (end < len and !isWhitespaceAt(text, end)) : (end += 1) {}
    self.selection = .{
        .start_block = block_idx,
        .start_char = start,
        .end_block = block_idx,
        .end_char = end,
    };
    self.cursor_char_index = end;
}
/// Extend to line start
pub fn extendToLineStart(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = 0,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    } else {
        // set cursor to start of selection
        self.selection.?.start_char = 0;
    }
    self.cursor_char_index = 0;
}
/// Extend to first non whitespace
pub fn extendToFirstNonWhitespace(self: *NoteDoc) void {
    self.selection = .{
        .start_block = self.active_block_index,
        .start_char = self.cursor_char_index,
        .end_block = self.active_block_index,
        .end_char = self.cursor_char_index,
    };
    self.gotoFirstNonWhitespace();
    self.selection.end_block = self.active_block_index;
    self.selection.end_char = self.cursor_char_index;
    // If needed, handle if old_cursor < new_cursor or not for selection direction
}
/// Extend to line end
pub fn extendToLineEnd(self: *NoteDoc) void {
    const block = &self.blocks.items[self.active_block_index];
    const end_char = block.data.characterCount();
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = end_char,
        };
    } else {
        // set cursor to end of selection
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = end_char;
    }
    self.cursor_char_index = end_char;
}

/// Extend to visual line end
pub fn extendToVisualLineEnd(self: *NoteDoc) void {
    const block = &self.blocks.items[self.active_block_index];
    var end_char = self.cursor_char_index;
    if (block.data.layout) |layout| {
        if (layout.findLineIndexOfChar(self.cursor_char_index)) |line_index| {
            end_char = layout.lines.items[line_index].range.end();
        }
    }
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = end_char,
        };
    } else {
        // set cursor to end of selection
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = end_char;
    }
    self.cursor_char_index = end_char;
}

/// Start or continue selection up by one visual line
pub fn extendVisualLineUp(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    }
    const extend_end = self.selection.?.end_char == self.cursor_char_index and self.selection.?.end_block == self.active_block_index;
    // Move cursor up
    self.moveVisualLineUp();
    // Update selection end to new cursor position
    if (extend_end) {
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = self.cursor_char_index;
    } else {
        self.selection.?.start_block = self.active_block_index;
        self.selection.?.start_char = self.cursor_char_index;
    }
}
/// Start or continue selection down by one visual line
pub fn extendVisualLineDown(self: *NoteDoc) void {
    if (self.selection == null) {
        // No existing selection, start one now
        self.selection = .{
            .start_block = self.active_block_index,
            .start_char = self.cursor_char_index,
            .end_block = self.active_block_index,
            .end_char = self.cursor_char_index,
        };
    }
    const extend_end = self.selection.?.end_char == self.cursor_char_index and self.selection.?.end_block == self.active_block_index;
    // Move cursor down
    self.moveVisualLineDown();
    // Update selection end to new cursor position
    if (extend_end) {
        self.selection.?.end_block = self.active_block_index;
        self.selection.?.end_char = self.cursor_char_index;
    } else {
        self.selection.?.start_block = self.active_block_index;
        self.selection.?.start_char = self.cursor_char_index;
    }
}

test "selectWordInner" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("Hello world", .text);
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 1; // "H|ello world"

    note_data.selectWordInner();
    // word under cursor is "Hello" from index 0 to 5
    try std.testing.expectEqual(0, note_data.selection.?.start_block);
    try std.testing.expectEqual(0, note_data.selection.?.start_char);
    try std.testing.expectEqual(0, note_data.selection.?.end_block);
    try std.testing.expectEqual(5, note_data.selection.?.end_char);
}

test "goto_first_nonwhitespace" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("   Hello", .text); // 3 leading spaces
    doc.active_block_index = 0;
    doc.cursor_char_index = 0;
    doc.gotoFirstNonWhitespace();
    // after skipping 3 spaces, first non-whitespace at index=3
    try std.testing.expectEqual(3, doc.cursor_char_index);
}

test "extend_to_line_start" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();

    try doc.appendParagraph("Hello world", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5; // after 'o'
    doc.extendToLineStart();
    // selection start at old cursor (5), end at line start (0)
    // Actually line start sets cursor at 0, so selection end = 0
    // Means we have selection from char 5 to 0
    try std.testing.expectEqual(0, doc.cursor_char_index);
    try std.testing.expectEqual(5, doc.selection.?.start_char);
    // direction not handled, but we know selection start is 5, end is 0
}

// Add test for the new functions
test "extendVisualLineUp" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("First line", .text);
    try note_data.appendParagraph("Second line", .text);
    try note_data.appendParagraph("Third line", .text);

    // Start at "Third line"
    note_data.active_block_index = 2;
    note_data.cursor_char_index = 5; // "Third| line"

    note_data.extendVisualLineUp();
    // Selection should now span from "Third line" to "Second line"
    try std.testing.expectEqual(2, note_data.selection.?.start_block);
    try std.testing.expectEqual(5, note_data.selection.?.start_char);
    try std.testing.expectEqual(1, note_data.selection.?.end_block);
    try std.testing.expectEqual(5, note_data.selection.?.end_char);
}

test "extendVisualLineDown" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.appendParagraph("First line", .text);
    try note_data.appendParagraph("Second line", .text);
    try note_data.appendParagraph("Third line", .text);

    // Start at "First line"
    note_data.active_block_index = 0;
    note_data.cursor_char_index = 3; // "Fir|st line"

    note_data.extendVisualLineDown();
    // Selection should now span from "First line" to "Second line"
    try std.testing.expectEqual(0, note_data.selection.?.start_block);
    try std.testing.expectEqual(3, note_data.selection.?.start_char);
    try std.testing.expectEqual(1, note_data.selection.?.end_block);
    try std.testing.expectEqual(3, note_data.selection.?.end_char);
}

pub fn isEndOfWrappingLine(self: *NoteDoc, block_index: usize, char_index: usize, end_of_line: bool) bool {
    const block = &self.blocks.items[block_index];
    if (block.data.layout) |layout| {
        const line_index = if (end_of_line) block.data.lineIndexAtCursor(char_index) else block.data.lineIndexAt(char_index);
        return layout.lines.items[line_index].range.end() == char_index;
    }
    return false;
}

/// Insert a character at the cursor position.
pub fn insertChar(self: *NoteDoc, ch: u8) !void {
    try self.insertText(&.{ch});
}
/// Insert text at the cursor position.
///
/// This function handles line breaks by splitting the text at each newline character.
pub fn insertText(self: *NoteDoc, text: []const u8) !void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    assert(self.cursor_char_index <= block.data.string.length());
    if (block.data.composition_text) |composition_text| {
        block.data.string.clear();
        try block.data.string.append(composition_text.text);
        self.cursor_char_index = composition_text.cursor_idx;
        self.allocator.free(composition_text.text);
        self.allocator.free(composition_text.comp_text);
        block.data.composition_text = null;
    }

    // Special case: single newline - just split the block
    if (text.len == 1 and text[0] == '\n') {
        const cursor_byte = try block.data.string.charIndexToByteOffset(self.cursor_char_index);
        const remaining = block.data.string.asSlice()[cursor_byte..];
        block.data.string.truncate(self.cursor_char_index);

        // Create new block with remaining text
        var new_data = Block.init(self.allocator);
        errdefer new_data.deinit();
        try new_data.string.append(remaining);
        try new_data.spans.append(.{
            .range = .{ .location = 0, .length = remaining.len },
            .style = .{},
        });
        try self.blocks.insert(self.active_block_index + 1, NoteBlock{
            .kind = block.kind,
            .indent = block.indent,
            .attributes = block.attributes,
            .data = new_data,
        });
        // Move to start of next block
        self.active_block_index += 1;
        self.cursor_char_index = 0;
        return;
    }
    // Split text at line breaks
    var start_byte: usize = 0;
    var iter = std.unicode.Utf8Iterator{ .bytes = text, .i = 0 };
    while (iter.nextCodepoint()) |codepoint| {
        if (codepoint == '\n') {
            // Insert text up to line break into current block
            if (iter.i > start_byte) {
                // iter.i is the byte index of the newline character, we don't want to include it in the insert
                const char_count = try block.data.string.insert(self.cursor_char_index, text[start_byte .. iter.i - 1]);
                self.cursor_char_index += char_count;
            }
            // Split block at cursor
            const cursor_byte = try block.data.string.charIndexToByteOffset(self.cursor_char_index);
            const remaining = block.data.string.asSlice()[cursor_byte..];
            block.data.string.truncate(self.cursor_char_index);

            // Create new block with remaining text
            var new_data = Block.init(self.allocator);
            errdefer new_data.deinit();
            try new_data.string.append(remaining);
            try new_data.spans.append(.{
                .range = .{ .location = 0, .length = remaining.len },
                .style = .{},
            });
            try self.blocks.insert(self.active_block_index + 1, NoteBlock{
                .kind = block.kind,
                .indent = block.indent,
                .attributes = block.attributes,
                .data = new_data,
            });
            // Move to start of next block
            self.active_block_index += 1;
            self.cursor_char_index = 0;
            block = &self.blocks.items[self.active_block_index];
            start_byte = iter.i;
        }
    }
    // Insert any remaining text after last line break
    if (start_byte < text.len) {
        const char_count = try block.data.string.insert(self.cursor_char_index, text[start_byte..]);
        self.cursor_char_index += char_count;
    }
}

pub fn insertCompositionText(self: *NoteDoc, text: []const u8) !void {
    assert(self.active_block_index < self.blocks.items.len);
    var block = &self.blocks.items[self.active_block_index];
    assert(self.cursor_char_index <= block.data.string.length());

    if (block.data.composition_text) |*composition_text| {
        block.data.string.clear();
        try block.data.string.append(composition_text.text);
        self.cursor_char_index = composition_text.cursor_idx;

        if (composition_text.comp_text.ptr != text.ptr) {
            const temp = try self.allocator.dupe(u8, text);
            self.allocator.free(composition_text.comp_text);
            composition_text.comp_text = temp;
        }
    } else {
        block.data.composition_text = .{
            .text = try self.allocator.dupe(u8, block.data.string.asSlice()),
            .comp_text = try self.allocator.dupe(u8, text),
            .cursor_idx = self.cursor_char_index,
        };
        if (block.data.composition_text.?.comp_text.ptr == text.ptr) {
            std.log.warn("comp_text and text are pointing to the same memory!", .{});
        }
    }
    // Special case: single newline - just split the block
    if (text.len == 1 and text[0] == '\n') {
        const cursor_byte = try block.data.string.charIndexToByteOffset(self.cursor_char_index);
        const remaining = block.data.string.asSlice()[cursor_byte..];
        block.data.string.truncate(self.cursor_char_index);

        // Create new block with remaining text
        var new_data = Block.init(self.allocator);
        errdefer new_data.deinit();
        try new_data.string.append(remaining);
        try new_data.spans.append(.{
            .range = .{ .location = 0, .length = remaining.len },
            .style = .{},
        });
        try self.blocks.insert(self.active_block_index + 1, NoteBlock{
            .kind = block.kind,
            .indent = block.indent,
            .attributes = block.attributes,
            .data = new_data,
        });
        // Move to start of next block
        self.active_block_index += 1;
        self.cursor_char_index = 0;
        return;
    }
    // Split text at line breaks
    var start_byte: usize = 0;
    var iter = std.unicode.Utf8Iterator{ .bytes = text, .i = 0 };
    while (iter.nextCodepoint()) |codepoint| {
        if (codepoint == '\n') {
            // Insert text up to line break into current block
            if (iter.i > start_byte) {
                const char_count = try block.data.string.insert(self.cursor_char_index, text[start_byte..iter.i]);
                self.cursor_char_index += char_count;
            }
            // Split block at cursor
            const cursor_byte = try block.data.string.charIndexToByteOffset(self.cursor_char_index);
            const remaining = block.data.string.asSlice()[cursor_byte..];
            block.data.string.truncate(self.cursor_char_index);
            // Create new block with remaining text
            var new_data = Block.init(self.allocator);
            errdefer new_data.deinit();
            try new_data.string.append(remaining);
            try new_data.spans.append(.{
                .range = .{ .location = 0, .length = remaining.len },
                .style = .{},
            });
            try self.blocks.insert(self.active_block_index + 1, NoteBlock{
                .kind = block.kind,
                .indent = block.indent,
                .attributes = block.attributes,
                .data = new_data,
            });
            // Move to start of next block
            self.active_block_index += 1;
            self.cursor_char_index = 0;
            block = &self.blocks.items[self.active_block_index];
            start_byte = iter.i;
        }
    }
    // Insert any remaining text after last line break
    if (start_byte < text.len) {
        const char_count = try block.data.string.insert(self.cursor_char_index, text[start_byte..]);
        self.cursor_char_index += char_count;
    }
}
/// Delete a character before the cursor (backspace).
/// If at start of block, merge with previous block if exists.
pub fn deleteCharBackward(self: *NoteDoc) !void {
    var block = &self.blocks.items[self.active_block_index];
    if (self.cursor_char_index > 0) {
        try block.data.string.delete(.{ .location = self.cursor_char_index - 1, .length = 1 });
        self.cursor_char_index -= 1;
    } else {
        // at start of block, merge with previous if possible
        if (self.active_block_index > 0) {
            const prev_idx = self.active_block_index - 1;
            var prev_block = &self.blocks.items[prev_idx];
            const old_len = prev_block.data.characterCount();
            // append this block's text to previous
            try prev_block.data.string.append(block.data.string.asSlice());
            // remove this block
            self.removeBlockAt(self.active_block_index);
            self.active_block_index = prev_idx;
            self.cursor_char_index = old_len;
        }
    }
}
/// Delete a character after the cursor (delete).
/// If at end of block, merge with next block if exists.
pub fn deleteCharForward(self: *NoteDoc) !void {
    var block = &self.blocks.items[self.active_block_index];
    const count = block.data.characterCount();
    if (self.cursor_char_index < count) {
        try block.data.string.delete(.{ .location = self.cursor_char_index, .length = 1 });
    } else {
        // at end of block, merge with next if exists
        if (self.active_block_index + 1 < self.blocks.items.len) {
            const next_idx = self.active_block_index + 1;
            var next_block = &self.blocks.items[next_idx];
            // append next block's text to current
            try block.data.string.append(next_block.data.string.asSlice());
            self.removeBlockAt(next_idx);
            // cursor stays at end of the merged block
        }
    }
}
/// Delete word backward:
pub fn deleteWordBackward(self: *NoteDoc) !void {
    const block = &self.blocks.items[self.active_block_index];
    const original_cursor = self.cursor_char_index;
    self.movePrevWordStart();
    const start = self.cursor_char_index;
    try block.data.string.delete(.{ .location = self.cursor_char_index, .length = original_cursor - start });
    self.cursor_char_index = start;
}
/// Delete word forward.
pub fn deleteWordForward(self: *NoteDoc) !void {
    const block = &self.blocks.items[self.active_block_index];
    const original_cursor = self.cursor_char_index;
    self.moveNextWordStart();
    const end = self.cursor_char_index;
    const length = end - original_cursor;
    self.cursor_char_index = original_cursor;
    try block.data.string.delete(.{ .location = original_cursor, .length = length });
}
/// Kill to line start.
pub fn killToLineStart(self: *NoteDoc) !void {
    const block = &self.blocks.items[self.active_block_index];
    const length = self.cursor_char_index;
    try block.data.string.delete(.{ .location = 0, .length = length });
    self.cursor_char_index = 0;
}
/// Kill to line end.
pub fn killToLineEnd(self: *NoteDoc) !void {
    const block = &self.blocks.items[self.active_block_index];
    const total = block.data.characterCount();
    const length = total - self.cursor_char_index;
    try block.data.string.delete(.{ .location = self.cursor_char_index, .length = length });
    // cursor stays same
}
// On enter, split the block at the cursor.
// The text after cursor moves to a new block below.
pub fn splitAtCursor(self: *NoteDoc) !void {
    var block = &self.blocks.items[self.active_block_index];
    const count = block.data.string.length();
    // Validate cursor position
    if (self.cursor_char_index > count) {
        return error.InvalidCursorPosition;
    }
    if (self.cursor_char_index == count) {
        // Create new empty block below
        var new_data = Block.init(self.allocator);
        errdefer new_data.deinit();
        try new_data.spans.append(.{
            .range = .{ .location = 0, .length = 0 },
            .style = .{},
        });
        try self.blocks.insert(self.active_block_index + 1, NoteBlock{
            .kind = block.kind,
            .indent = block.indent,
            .attributes = block.attributes,
            .data = new_data,
        });
        self.active_block_index += 1;
        self.cursor_char_index = 0;
    } else {
        var new_data = Block.init(self.allocator);
        errdefer new_data.deinit();
        // Get text after cursor
        const after = try block.data.string.substring(.{
            .location = self.cursor_char_index,
            .length = count - self.cursor_char_index,
        });
        try new_data.string.append(after);
        // Truncate original block at cursor
        block.data.string.truncate(self.cursor_char_index);
        // Add default spans for both blocks
        block.data.spans.clearRetainingCapacity();
        try block.data.spans.append(.{
            .range = .{
                .location = 0,
                .length = self.cursor_char_index,
            },
            .style = .{},
        });
        try new_data.spans.append(.{
            .range = .{
                .location = 0,
                .length = after.len,
            },
            .style = .{},
        });
        // Insert new block
        try self.blocks.insert(self.active_block_index + 1, NoteBlock{
            .kind = block.kind,
            .indent = block.indent,
            .attributes = block.attributes,
            .data = new_data,
        });
        self.active_block_index += 1;
        self.cursor_char_index = 0;
    }
}
/// Indent selection:
pub fn indent(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    self.blocks.items[self.active_block_index].indent += 1;
}
/// Unindent selection:
pub fn unindent(self: *NoteDoc) void {
    assert(self.active_block_index < self.blocks.items.len);
    const block = &self.blocks.items[self.active_block_index];
    if (block.indent > 0) self.blocks.items[self.active_block_index].indent -= 1;
}
/// Delete text range from start_block:start_char to end_block:end_char
pub fn deleteRange(self: *NoteDoc, start_block: usize, start_char: usize, end_block: usize, end_char: usize) !void {
    // Ensure start is before end
    if (start_block > end_block or (start_block == end_block and start_char > end_char)) {
        return self.deleteRange(end_block, end_char, start_block, start_char);
    }
    // If same block, simple delete
    if (start_block == end_block) {
        var block = &self.blocks.items[start_block];
        try block.data.string.delete(.{ .location = start_char, .length = end_char - start_char });
        self.cursor_char_index = start_char;
        return;
    }
    // Different blocks:
    // 1. Keep text before start_char in first block
    const first_block = &self.blocks.items[start_block];
    first_block.data.string.truncate(start_char);
    // 2. Append remaining text from last block after end_char
    const last_block = &self.blocks.items[end_block];
    if (end_char < last_block.data.string.length()) {
        const remaining = try last_block.data.string.substring(.{
            .location = end_char,
            .length = last_block.data.string.length() - end_char,
        });
        try first_block.data.string.append(remaining);
    }
    // 3. Remove all blocks in between including last block
    var i: usize = end_block;
    while (i > start_block) : (i -= 1) {
        self.removeBlockAt(i);
    }
    // 4. Set cursor to start of deleted range
    self.active_block_index = start_block;
    self.cursor_char_index = start_char;
}
/// Delete the current selection if any and clear it
pub fn deleteSelection(self: *NoteDoc) !void {
    if (self.selection) |selection| {
        try self.deleteRange(selection.start_block, selection.start_char, selection.end_block, selection.end_char);
        self.selection = null;
    }
}

test "insertChar in the middle" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 1; // after 'e'

    try doc.insertChar('-');
    try std.testing.expectEqualStrings("H-ello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "insertChar to the end" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 5; // at end of "Hello"

    try doc.insertChar('!');
    try std.testing.expectEqualStrings("Hello!", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(6, doc.cursor_char_index);
}

test "insertChar to empty block" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 0; // at beginning of the block

    try doc.insertChar('#');
    try std.testing.expectEqualStrings("#", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(1, doc.cursor_char_index);
}

test "insert UTF8 string" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5;
    try doc.insertText("é");
    try std.testing.expectEqualStrings("Helloé", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(6, doc.cursor_char_index);
}

test "insert Chinese string to the end" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5;
    try doc.insertText("你好");
    try std.testing.expectEqualStrings("Hello你好", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(7, doc.cursor_char_index);
}

test "insert Chinese string to the beginning" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 0;
    try doc.insertText("你好");
    try std.testing.expectEqualStrings("你好Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "insert Chinese string in the middle" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 2;
    try doc.insertText("你好");
    try std.testing.expectEqualStrings("He你好llo", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(4, doc.cursor_char_index);
}

test "insertText with newline" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5;
    try doc.insertText("\n");
    try std.testing.expectEqualStrings("Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqualStrings("", doc.blocks.items[1].data.string.asSlice());
    try std.testing.expectEqual(2, doc.blockCount());
    try std.testing.expectEqual(0, doc.cursor_char_index);
}

test "deleteCharBackward basic" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello!", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 6; // after '!'

    try doc.deleteCharBackward();
    try std.testing.expectEqualStrings("Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(5, doc.cursor_char_index);

    // Delete char in middle
    doc.cursor_char_index = 3; // Hel|lo
    try doc.deleteCharBackward();
    // Removing 'l' before cursor (index 2)
    try std.testing.expectEqualStrings("Helo", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "deleteCharBackward at the beginning" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 0;
    try doc.deleteCharBackward();
    try std.testing.expectEqualStrings("Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(0, doc.cursor_char_index);
}

test "deleteCharBackward at the end" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5;
    try doc.deleteCharBackward();
    try std.testing.expectEqualStrings("Hell", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(4, doc.cursor_char_index);
}

test "deleteCharBackward Chinese string" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("你好", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 2;
    try doc.deleteCharBackward();
    try std.testing.expectEqual(1, doc.blocks.items[0].data.string.length());
    try std.testing.expectEqualStrings("你", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(1, doc.cursor_char_index);
}

test "deleteCharBackward with mixed Chinese and English" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("你好Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 3;
    try doc.deleteCharBackward();
    try std.testing.expectEqualStrings("你好ello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "deleteCharForward basic" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello!", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 0; // at 'H'

    try doc.deleteCharForward();
    // Removes 'H'
    try std.testing.expectEqualStrings("ello!", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(0, doc.cursor_char_index);

    // Delete char in middle
    doc.cursor_char_index = 2; // e(0)l(1)l(2) o(3)
    try doc.deleteCharForward();
    // Removes 'l' at index 2
    try std.testing.expectEqualStrings("elo!", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "deleteCharForward with mixed Chinese and English" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("你好Hello", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 2;
    try doc.deleteCharForward();
    try std.testing.expectEqualStrings("你好ello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(2, doc.cursor_char_index);
}

test "deleteWordBackward" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 11; // after 'd' in "Hello world"
    try doc.deleteWordBackward();
    // Previous word is "world", remove it
    // Result: "Hello "
    try std.testing.expectEqualStrings("Hello ", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(6, doc.cursor_char_index); // 'Hello ' length=6
}

// TODO
test "deleteWordForward" {
    if (true) return error.SkipZigTest;

    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 0; // at 'H'
    try doc.deleteWordForward();
    // Next word start from 'H', end at 'o'
    // Remove "Hello"
    try std.testing.expectEqualStrings(" world", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(0, doc.cursor_char_index);
}

test "killToLineStart" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("   Hello world", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 9; // somewhere in "Hello world"
    try doc.killToLineStart();
    // Removes all before cursor: original line: "   Hello world"
    // cursor=9 means after "   Hello"
    // from start(0) to (9) removed
    try std.testing.expectEqualStrings("world", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(0, doc.cursor_char_index);
}

test "killToLineEnd" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);

    doc.active_block_index = 0;
    doc.cursor_char_index = 5; // after 'o' in "Hello"
    try doc.killToLineEnd();
    // Removes everything after 'o', leaving "Hello"
    try std.testing.expectEqualStrings("Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqual(5, doc.cursor_char_index);
}

test "splitAtCursor basic" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);
    doc.active_block_index = 0;
    doc.cursor_char_index = 5;
    try doc.splitAtCursor();
    try std.testing.expectEqualStrings("Hello", doc.blocks.items[0].data.string.asSlice());
    try std.testing.expectEqualStrings(" world", doc.blocks.items[1].data.string.asSlice());
}

test "deleteRange basic" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);
    try doc.deleteRange(0, 0, 0, 5);
    try std.testing.expectEqualStrings(" world", doc.blocks.items[0].data.string.asSlice());
}

test "deleteRange with Chinese string" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("你好世界", .text);
    doc.selectRange(0, 0, 0, 2);
    try doc.deleteRange(0, 0, 0, 2);
    try std.testing.expectEqualStrings("世界", doc.blocks.items[0].data.string.asSlice());
}

test "deleteRange across multiple blocks" {
    var doc = NoteDoc.init(std.testing.allocator);
    defer doc.deinit();
    try doc.appendParagraph("Hello world", .text);
    try doc.appendParagraph("Hello world", .text);
    try doc.deleteRange(0, 0, 1, 5);
    try std.testing.expectEqual(1, doc.blocks.items.len);
    try std.testing.expectEqualStrings(" world", doc.blocks.items[0].data.string.asSlice());
}

/// Import blocks from a Markdown-like string.
pub fn importFromString(self: *NoteDoc, input: []const u8) !void {
    // Handle empty string input by creating an empty paragraph
    if (input.len == 0) {
        try self.appendParagraph("", .text);
        return;
    }

    var line_iter = std.mem.splitScalar(u8, input, '\n');
    var line_count: u32 = 1;
    var inside_code_block = false;
    var code_lines = std.ArrayList([]const u8).init(self.allocator);
    defer code_lines.deinit();
    var code_lang: []const u8 = "plain";

    while (line_iter.next()) |line| {
        var work = line;

        // Check for code block start/end
        if (inside_code_block) {
            if (std.mem.startsWith(u8, work, "```")) {
                // End code block
                inside_code_block = false;
                // Combine code lines
                const joined = try self.joinLines(code_lines.items, "\n");
                defer self.allocator.free(joined);
                try self.appendCode(joined, code_lang);
                code_lines.clearRetainingCapacity();
            } else {
                // Accumulate code line
                try code_lines.append(work);
            }
            line_count += 1;
            continue;
        }

        // Not inside code block
        var indention: u8 = 0;
        // Count leading tabs
        while ((work.len > 0) and (work[0] == '\t')) {
            indention += 1;
            work = work[1..];
        }

        if (std.mem.startsWith(u8, work, "```")) {
            // Start code block
            inside_code_block = true;
            work = work[3..];
            work = std.mem.trimLeft(u8, work, " ");
            // If there is something after ``` assume it's a language
            if (work.len > 0) {
                code_lang = work;
            } else {
                code_lang = "plain";
            }
            line_count += 1;
            continue;
        }

        // Create paragraph for empty lines (ignore empty lines after last linebreak)
        if (std.mem.trim(u8, work, " ").len == 0) {
            if (line_iter.peek() != null) {
                try self.appendParagraph("", .text);
                self.setIndentOf(self.blockCount() - 1, indention);
                line_count += 1;
            }
            continue;
        }

        // Detect heading
        var heading_count: usize = 0;
        while (heading_count < work.len and work[heading_count] == '#') {
            heading_count += 1;
        }

        if (heading_count > 0 and heading_count <= 6 and heading_count < work.len and work[heading_count] == ' ') {
            // It's a heading
            const header_text = work[(heading_count + 1)..];
            const kind: ParagraphAttributes.Kind = switch (heading_count) {
                1 => .h1,
                2 => .h2,
                3 => .h3,
                4 => .h4,
                5 => .h5,
                6 => .h6,
                else => .text,
            };
            try self.appendParagraph(header_text, kind);
            self.setIndentOf(self.blockCount() - 1, indention);
        }
        // Not a heading, check for other patterns
        else if (std.mem.startsWith(u8, work, "> ")) {
            // Quote paragraph
            const text = work[2..];
            try self.appendParagraph(text, .quote);
            self.setIndentOf(self.blockCount() - 1, indention);
        }
        // Check todo list
        else if (std.mem.startsWith(u8, work, "- [ ] ")) {
            const text = work[6..];
            try self.appendList(text, .todo);
            self.setIndentOf(self.blockCount() - 1, indention);
        } else if (std.mem.startsWith(u8, work, "- [x] ")) {
            const text = work[6..];
            try self.appendList(text, .todo);
            self.setIndentOf(self.blockCount() - 1, indention);
            self.blocks.items[self.blockCount() - 1].attributes.list.done = true;
        }
        // Bulleted list `- ` or `+ ` or `* `
        else if (std.mem.startsWith(u8, work, "- ") or
            std.mem.startsWith(u8, work, "+ ") or
            std.mem.startsWith(u8, work, "* "))
        {
            const text = work[2..];
            try self.appendList(text, .bulleted);
            self.setIndentOf(self.blockCount() - 1, indention);
        }
        // Numbered list: digit(s) followed by '. '
        else if (matchNumberedList(work)) |dot_index| {
            const text = work[(dot_index + 2)..];
            try self.appendList(text, .numbered);
            self.setIndentOf(self.blockCount() - 1, indention);
        }
        // Normal paragraph text
        else {
            try self.appendParagraph(work, .text);
            self.setIndentOf(self.blockCount() - 1, indention);
        }

        line_count += 1;
    }

    // If ended inside a code block without closing ``` - handle gracefully
    if (inside_code_block and code_lines.items.len > 0) {
        const joined = try self.joinLines(code_lines.items, "\n");
        defer self.allocator.free(joined);
        try self.appendCode(joined, code_lang);
    }
}
pub fn setDocSpans(self: *NoteDoc, spans: std.ArrayList(style.Span)) !void {
    for (spans.items) |span| {
        try self.spans.append(.{
            .range = .{
                .location = span.range.location,
                .length = span.range.length,
            },
            .style = span.style,
        });
    }
}

pub fn hasFontFamily(self: *NoteDoc, target_font_family_id: u32) bool {
    for (self.blocks.items) |block| {
        if (block.data.layout) |layout| {
            for (layout.visual_spans.items) |*v_span| {
                for (v_span.positioned_glyphs.items) |*glyph| {
                    if (glyph.font_family == target_font_family_id) {
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

/// Join multiple lines with a separator into a newly allocated buffer.
fn joinLines(self: *NoteDoc, lines: [][]const u8, sep: []const u8) ![]const u8 {
    var total_len: usize = 0;
    for (lines) |l| total_len += l.len;
    total_len += if (lines.len == 0) 0 else sep.len * (lines.len - 1);

    var buf = try self.allocator.alloc(u8, total_len);
    var i: usize = 0;
    for (lines, 0..) |l, idx| {
        if (idx != 0) {
            @memcpy(buf[i .. i + sep.len], sep);
            i += sep.len;
        }
        @memcpy(buf[i .. i + l.len], l);
        i += l.len;
    }
    return buf;
}
/// Match a numbered list line pattern.
fn matchNumberedList(line: []const u8) ?usize {
    // match pattern: ^\d+\.
    // find first non-digit until we hit a dot
    var i: usize = 0;
    var found_digit = false;
    while (i < line.len and line[i] >= '0' and line[i] <= '9') : (i += 1) {
        found_digit = true;
    }
    if (!found_digit or i == line.len or line[i] != '.') return null;
    // ensure after '.' is either space or EOL
    if (i + 1 < line.len and line[i + 1] == ' ') {
        return i;
    }
    return null;
}

test "import empty string" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    try note_data.importFromString("");

    try std.testing.expectEqual(1, note_data.blocks.items.len);
    const block = note_data.blocks.items[0];
    try std.testing.expectEqual(.paragraph, block.kind);
    try std.testing.expectEqual(.text, block.attributes.paragraph.kind);
    try std.testing.expectEqualStrings("", block.data.string.asSlice());
}

test "import simple markdown lines" {
    var note_data = NoteDoc.init(std.testing.allocator);
    defer note_data.deinit();

    const input =
        "# Heading 1\n" ++
        "## Heading 2\n" ++
        "\t- Bulleted item\n" ++
        "1. Numbered item\n" ++
        "- [ ] Todo not done\n" ++
        "- [x] Todo done\n" ++
        "> Quote line\n" ++
        "\n" ++
        "Normal paragraph line\n" ++
        "```zig\n" ++
        "const x = 42;\n" ++
        "```\n";

    try note_data.importFromString(input);

    try std.testing.expectEqual(10, note_data.blocks.items.len);

    const b1 = note_data.blocks.items[0]; // h1
    try std.testing.expectEqual(.paragraph, b1.kind);
    try std.testing.expectEqual(.h1, b1.attributes.paragraph.kind);
    try std.testing.expectEqualStrings("Heading 1", b1.data.string.asSlice());

    const b2 = note_data.blocks.items[1]; // h2
    try std.testing.expectEqual(.paragraph, b2.kind);
    try std.testing.expectEqual(.h2, b2.attributes.paragraph.kind);
    try std.testing.expectEqualStrings("Heading 2", b2.data.string.asSlice());

    const b3 = note_data.blocks.items[2]; // bulleted list
    try std.testing.expectEqual(.list, b3.kind);
    try std.testing.expectEqual(.bulleted, b3.attributes.list.kind);
    try std.testing.expectEqualStrings("Bulleted item", b3.data.string.asSlice());
    try std.testing.expectEqual(1, b3.indent);

    const b4 = note_data.blocks.items[3]; // numbered list
    try std.testing.expectEqual(.list, b4.kind);
    try std.testing.expectEqual(.numbered, b4.attributes.list.kind);
    try std.testing.expectEqualStrings("Numbered item", b4.data.string.asSlice());

    const b5 = note_data.blocks.items[4]; // todo not done
    try std.testing.expectEqual(.list, b5.kind);
    try std.testing.expectEqual(.todo, b5.attributes.list.kind);
    try std.testing.expectEqual(false, b5.attributes.list.done);
    try std.testing.expectEqualStrings("Todo not done", b5.data.string.asSlice());

    const b6 = note_data.blocks.items[5]; // todo done
    try std.testing.expectEqual(.list, b6.kind);
    try std.testing.expectEqual(.todo, b6.attributes.list.kind);
    try std.testing.expectEqual(true, b6.attributes.list.done);
    try std.testing.expectEqualStrings("Todo done", b6.data.string.asSlice());

    const b7 = note_data.blocks.items[6]; // quote
    try std.testing.expectEqual(.paragraph, b7.kind);
    try std.testing.expectEqual(.quote, b7.attributes.paragraph.kind);
    try std.testing.expectEqualStrings("Quote line", b7.data.string.asSlice());

    const b8 = note_data.blocks.items[7]; // empty paragraph
    try std.testing.expectEqual(.paragraph, b8.kind);
    try std.testing.expectEqual(.text, b8.attributes.paragraph.kind);
    try std.testing.expectEqual(0, b8.data.string.asSlice().len);

    const b9 = note_data.blocks.items[8]; // normal paragraph
    try std.testing.expectEqual(.paragraph, b9.kind);
    try std.testing.expectEqual(.text, b9.attributes.paragraph.kind);
    try std.testing.expectEqualStrings("Normal paragraph line", b9.data.string.asSlice());

    const b10 = note_data.blocks.items[9]; // code block
    try std.testing.expectEqual(.code, b10.kind);
    try std.testing.expectEqualStrings("const x = 42;", b10.data.string.asSlice());
    try std.testing.expectEqualStrings("zig", b10.attributes.code.lang);
}

pub const BlockPathList = std.ArrayList(struct { block_index: usize, path: Path.Builder });

/// Build paths for all blocks in the page.
///
/// Needs to be called after `buildLayout`.
pub fn buildPaths(note_doc: *NoteDoc, note_paths: *BlockPathList, font_db: *FontDB, allocator: std.mem.Allocator) !void {
    for (note_paths.items) |*item| {
        item.path.deinit();
    }
    note_paths.clearRetainingCapacity();
    for (note_doc.blocks.items, 0..) |*block, block_index| {
        switch (block.kind) {
            .list => {
                switch (block.attributes.list.kind) {
                    .bulleted => {
                        var path = Path.Builder.init(allocator);
                        try path.addOval(0.0, 11.0, 7.0, 7.0);
                        try note_paths.append(.{
                            .block_index = block_index,
                            .path = path,
                        });
                    },
                    else => {},
                }
            },
            else => {},
        }
        // TODO: add alignment offset  to path transform
        const paths = try block.data.buildPaths(font_db, allocator, math.mat2df_identity().mul(math.mat2df_translate_p(note_doc.blockOffset(block_index))));
        defer {
            paths.deinit();
        }
        try note_paths.ensureUnusedCapacity(paths.items.len);
        for (paths.items) |path| {
            try note_paths.append(.{
                .block_index = block_index,
                .path = path,
            });
        }
    }
}

pub fn resolveSpans(doc: *NoteDoc) !void {
    // First clear existing spans in blocks
    for (doc.blocks.items) |*block| {
        block.data.spans.clearRetainingCapacity();
    }

    // Sort doc spans by location to process them in order
    std.mem.sort(style.Span, doc.spans.items, {}, rangeLT);

    // Process each block to create its spans
    for (doc.spans.items) |span| {
        const span_start = span.range.location;
        const span_end = span_start + span.range.length;
        var doc_pos: usize = 0;

        for (doc.blocks.items) |*block| {
            const block_len = block.data.string.length();

            // Break if the span doesn't have intersection with the block(the span should be resolved)
            if (span_end <= doc_pos) {
                break;
            }

            // Move to the next block if span starts after this block
            if (span_start >= doc_pos + block_len) {
                doc_pos += block_len + 1;
                continue;
            }

            // Calculate intersection with block
            const start = if (span_start > doc_pos)
                span_start - doc_pos
            else
                0;
            const end = if (span_end < doc_pos + block_len)
                span_end - doc_pos
            else
                block_len;

            // Add span if it has non-zero length
            if (end > start) {
                try block.data.spans.append(.{
                    .range = .{
                        .location = start,
                        .length = end - start,
                    },
                    .style = span.style,
                });
            }

            // Move to next span if it ends within or at end of block
            if (span_end < doc_pos + block_len) {
                break;
            } else {
                // Move to the next block if the span is not done yet
                doc_pos += block_len + 1;
                continue;
            }
        }
    }
}

fn rangeLT(_: void, a: style.Span, b: style.Span) bool {
    return a.range.location < b.range.location;
}
