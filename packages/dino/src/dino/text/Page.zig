const Page = @This();
const dino = @import("../dino.zig");

const std = @import("std");
const app = @import("../app.zig");
const time = @import("../time.zig");
const Color = @import("../color.zig").Color;
const math = @import("../math.zig");
const canvas = @import("../render/canvas.zig");
const Path = @import("../Path.zig");
const Range = @import("Range.zig");
const NoteDoc = @import("NoteDoc.zig");
const FontDB = @import("FontDB.zig");

// Add external function declarations
const ext = struct {
    extern fn onTextUpdate(text_ptr: [*]const u8, len: u32, isComposing: bool, start: u32, end: u32, cursor: u32) void;
    extern fn onExitTextEditingMode() void;
    extern fn onCopyText(text: [*]const u8, len: u32) void;
    extern fn onSelectionUpdate(node_id: u32, start_block: u32, start_char: u32, end_block: u32, end_char: u32, cursor: u32, active_block_index: u32) void;
    extern fn onPasteText() void;
    extern fn onUndoComb() void;
    extern fn onRedoComb() void;
    extern fn onUpdatePageOffset(node_id: u32) void;
    extern fn onNodeUpdate(node_id: u32) void;
    extern fn onZoom(zoom: f32) void;
    extern fn onZoomToFit() void;
    extern fn onZoomToSelection() void;
    extern fn onZoomOut() void;
    extern fn onZoomIn() void;
    extern fn onESC() void;
    extern fn onTab() void;
    extern fn onShiftTab() void;
};

const BlockPathList = std.ArrayList(struct { block_index: usize, path: Path.Builder });

pub const config = struct {
    /// seconds between clicks to count as continuous multi click
    const multi_click_time: f64 = 0.5;
    /// pixels between clicks to count as continuous multi click
    const multi_click_distance: f32 = 5.0;

    /// Time in seconds for one blink cycle
    const cursor_blink_time: f32 = 0.53;
};

pub const EditMode = enum {
    /// General editing mode
    editing,
    normal,
};

allocator: std.mem.Allocator,

// data
doc: *NoteDoc = undefined,
width: f32 = -1.0, // -1 means auto width
offset: math.Vec2f = math.vec2fs(0.0),
view_zoom: f32 = 1.0,

// edit
edit_mode: EditMode = .normal,
edit_node_id: ?u32 = null,
// show end of line cursor, when right + cmd
show_end_of_line_cursor: bool = false,
// force cursor to be default regardless of edit_mode
force_default_cursor: bool = false,

mouse: struct {
    world: math.Vec2f = math.vec2fs(0),

    last_click_time: f64 = 0,
    last_click_pos: math.Vec2f = math.vec2fs(0),
    last_click_block: ?usize = null,
    click_count: u32 = 0,

    cursor_visible: bool = true,
    cursor_blink_timer: f32 = 0,
} = .{},

hovered_block_info: ?struct {
    block_index: usize,
    line_index: usize,
    char_index: usize,
    hover_line: bool,
} = null,

drag_start: ?struct {
    block_index: usize,
    char_index: usize,
} = null,

drag_state: struct {
    block_index: ?usize = null,
    offset: math.Vec2f = math.vec2fs(0),
    drop_target: ?struct {
        block_index: usize,
        position: enum { before, after },
    } = null,
} = .{},

pub fn init(allocator: std.mem.Allocator) Page {
    return Page{
        .allocator = allocator,
    };
}

pub inline fn characterCount(page: Page, block_index: usize) usize {
    return page.doc.characterCount(block_index);
}
pub inline fn lineCount(page: Page, block_index: usize) usize {
    return page.doc.lineCount(block_index);
}
pub inline fn spanCount(page: Page, block_index: usize) usize {
    return page.doc.spanCount(block_index);
}
pub inline fn blockCount(page: Page) usize {
    return page.doc.blockCount();
}

pub inline fn characterRangeAtLine(page: Page, block_index: usize, line_index: usize) Range {
    return page.doc.characterRangeAtLine(block_index, line_index);
}

pub fn characterBBox(page: Page, block_index: usize, char_index: usize) ?math.Rect2f {
    if (page.doc.characterBBox(block_index, char_index)) |bbox| {
        const offset = page.doc.blockOffset(block_index);
        return math.rect2f(
            bbox.left() + offset.x(),
            bbox.top() + offset.y(),
            bbox.width(),
            bbox.height(),
        );
    }
    return null;
}
pub fn lineBBox(page: Page, block_index: usize, line_index: usize) ?math.Rect2f {
    if (page.doc.lineBBox(block_index, line_index)) |bbox| {
        const offset = page.doc.blockOffset(block_index);
        return math.rect2f(
            bbox.left() + offset.x(),
            bbox.top() + offset.y(),
            bbox.width(),
            bbox.height(),
        );
    }
    return null;
}
pub fn spanBBox(page: Page, block_index: usize, span_index: usize) ?math.Rect2f {
    if (page.doc.spanBBox(block_index, span_index)) |bbox| {
        const offset = page.doc.blockOffset(block_index);
        return math.rect2f(
            bbox.left() + offset.x(),
            bbox.top() + offset.y(),
            bbox.width(),
            bbox.height(),
        );
    }
    return null;
}
pub fn blockBBox(page: Page, block_index: usize) ?math.Rect2f {
    if (page.doc.blockBBox(block_index)) |bbox| {
        const offset = page.doc.blockOffset(block_index);
        return math.rect2f(
            bbox.left() + offset.x(),
            bbox.top() + offset.y(),
            bbox.width(),
            bbox.height(),
        );
    }
    return null;
}
/// Get the bounding box of entire page content.
pub fn contentBBox(page: Page) ?math.Rect2f {
    return page.doc.docBBox();
}
/// Baseline for specific line in block.
pub fn baseline(page: Page, block_index: usize, line_index: usize) ?math.Linef {
    if (page.doc.baseline(block_index, line_index)) |line| {
        const offset = page.doc.blockOffset(block_index);
        return math.linef(
            line.x1() + offset.x(),
            line.y1() + offset.y(),
            line.x2() + offset.x(),
            line.y2() + offset.y(),
        );
    }
    return null;
}

/// Get the line for cursor rendering.
pub fn cursorLine(page: Page) ?math.Linef {
    // First try to get character bbox
    const end_of_line = page.doc.isEndOfWrappingLine(page.doc.active_block_index, page.doc.cursor_char_index, page.show_end_of_line_cursor);
    const line = page.doc.blocks.items[0].data.layout.?.lines.items[0];
    const style_line_height = page.doc.spans.items[0].style.line_height orelse 1.0;
    const style_font_size = page.doc.spans.items[0].style.font_size orelse 1.0;

    const cluster_height = @ceil(line.ascent - line.descent);
    // if the line height is -1, use the cluster height
    const height = if (style_line_height <= 0.0) cluster_height else style_font_size * style_line_height;
    const offset = @ceil(cluster_height - @round(height)) / 2.0;
    std.log.warn("scaled offset: {d}", .{offset});

    // const cluster_height = (line.ascent - line.descent);
    // const line_height = @ceil(line.ascent - line.descent) * 1.2;
    // const line_sacle_height: f32 = if (page.doc.spans.items[0].style.line_height.? < 0.0) cluster_height else page.doc.spans.items[0].style.line_height.? * page.doc.spans.items[0].style.font_size.?;
    // const scaled_offset = -@round(cluster_height - @round(line_sacle_height)) / 2.0;
    // std.log.warn("scaled offset: {d}", .{scaled_offset});
    // const line_sacle: f32 = if (page.doc.spans.items[0].style.line_height == -1) 1.2 else page.doc.spans.items[0].style.line_height.?;
    // const line_height = if (page.doc.spans.items[0].style.line_height.? < 0.0) line.ascent - line.descent else page.doc.spans.items[0].style.line_height.? * page.doc.spans.items[0].style.font_size.?;
    // const original_height = @round(line.ascent - line.descent);
    // const scaled_offset = if (line_sacle < original_height) @ceil((line.ascent - line.descent - @round(line_height)) / 2.0) else 0;
    const caret_height = @ceil(page.doc.spans.items[0].style.font_size.?) * 1.2;
    // const caret_height = @round(line.ascent - line.descent);
    // const cluster_scale = @ceil(line.ascent - line.descent) / page.doc.spans.items[0].style.font_size.?;
    // const line_sacle: f32 = if (page.doc.spans.items[0].style.line_height.? == -1.0) cluster_scale else page.doc.spans.items[0].style.line_height.?;
    // var scaled_offset: f32 = if (line.height >= line_height) 0 else -page.doc.spans.items[0].style.line_height.? * line_height;
    // scaled_offset = if (page.doc.spans.items[0].style.line_height.? < 0)
    const scaled_offset = 0; //@ceil((line_sacle - cluster_scale) * page.doc.spans.items[0].style.font_size.? / 2.0);

    // std.log.warn("scaled offset: {d}", .{scaled_offset});

    if (!end_of_line) {
        if (page.characterBBox(page.doc.active_block_index, page.doc.cursor_char_index)) |bbox| {
            if (page.lineBBox(page.doc.active_block_index, page.doc.getLineIndexAtCursor())) |line_bbox| {
                return math.linef(
                    bbox.left(),
                    line_bbox.top(),
                    bbox.left(),
                    line_bbox.top() - caret_height,
                );
            }
        }
    } else {
        // If no character bbox, try to get the line bbox
        const line_index = page.doc.blocks.items[page.doc.active_block_index].data.lineIndexAtCursor(page.doc.cursor_char_index);
        if (page.lineBBox(page.doc.active_block_index, line_index)) |line_bbox| {
            const line_range = page.doc.characterRangeAtLine(page.doc.active_block_index, line_index);
            if (line_range.length == 0) {
                // For empty lines, use line bbox left edge
                // move the cursor to the center of the line
                return math.linef(
                    line_bbox.left(),
                    line_bbox.top() + scaled_offset,
                    line_bbox.left(),
                    line_bbox.top() + scaled_offset + caret_height,
                );
            } else if (page.doc.cursor_char_index >= line_range.end()) {
                // For cursor at end of non-empty line, use last character's right edge
                if (page.characterBBox(page.doc.active_block_index, line_range.end() - 1)) |char_bbox| {
                    return math.linef(
                        char_bbox.right(),
                        line_bbox.top() + scaled_offset,
                        char_bbox.right(),
                        line_bbox.top() + scaled_offset + caret_height,
                    );
                }
            }
        }
        // We might be beyond text length, let's fallback to block end.
        const block_end_char_index = page.characterCount(page.doc.active_block_index);
        if (block_end_char_index > 0) {
            if (page.characterBBox(page.doc.active_block_index, block_end_char_index - 1)) |bbox| {
                return math.linef(
                    bbox.right(),
                    bbox.top() + scaled_offset,
                    bbox.right(),
                    bbox.top() + scaled_offset + caret_height,
                );
            }
        }
    }
    return null;
}

// Shifting Line bbox along with Alignment value

pub inline fn moveCharLeft(page: *Page) void {
    page.doc.moveCharLeft();
}
pub inline fn moveCharRight(page: *Page) void {
    page.doc.moveCharRight();
}
pub inline fn movePrevWordStart(page: *Page) void {
    page.doc.movePrevWordStart();
}
pub inline fn movePrevWordEnd(page: *Page) void {
    page.doc.movePrevWordEnd();
}
pub inline fn moveNextWordStart(page: *Page) void {
    page.doc.moveNextWordStart();
}
pub inline fn moveNextWordEnd(page: *Page) void {
    page.doc.moveNextWordEnd();
}
pub inline fn moveVisualLineUp(page: *Page) void {
    page.doc.moveVisualLineUp();
}
pub inline fn moveVisualLineDown(page: *Page) void {
    page.doc.moveVisualLineDown();
}

pub inline fn getSelectionRange(page: *Page, block_index: usize) ?Range {
    return page.doc.getSelectionRange(block_index);
}
pub inline fn clearSelection(page: *Page) void {
    page.doc.clearSelection();
}
pub inline fn extendCharLeft(page: *Page) void {
    page.doc.extendCharLeft();
}
pub inline fn extendCharRight(page: *Page) void {
    page.doc.extendCharRight();
}
pub inline fn extendPrevWordStart(page: *Page) void {
    page.doc.extendPrevWordStart();
}
pub inline fn extendNextWordEnd(page: *Page) void {
    page.doc.extendNextWordEnd();
}
pub inline fn extendVisualLineUp(page: *Page) void {
    page.doc.extendVisualLineUp();
}
pub inline fn extendVisualLineDown(page: *Page) void {
    page.doc.extendVisualLineDown();
}

pub inline fn isEndOfWrappingLine(page: *Page, block_index: usize, char_index: usize, end_of_line: bool) bool {
    return page.doc.isEndOfWrappingLine(block_index, char_index, end_of_line);
}

/// Find the block that contains the mouse.
pub fn searchHoverBlock(page: *Page, mouse_world: math.Vec2f) void {
    page.hovered_block_info = null;
    for (0..page.blockCount()) |block_index| {
        if (page.blockBBox(block_index)) |bbox| {
            // Check if mouse is within the block's bbox range VERTICALLY.
            if ((mouse_world.y() >= bbox.top()) and (mouse_world.y() < bbox.bottom())) {
                // Find hovered line
                const block_height = bbox.height();
                const line_count: f32 = @as(f32, @floatFromInt(page.lineCount(block_index)));
                var line_index: usize = 0;
                var hovered_line = false;
                var char_index: usize = page.characterCount(block_index);
                // Check each line in the block
                for (0..page.lineCount(block_index)) |i| {
                    if (page.lineBBox(block_index, i)) |line_bbox| {
                        const line_top = bbox.top() + @as(f32, @floatFromInt(i)) * block_height / line_count;
                        const line_bottom = line_top + block_height / line_count;
                        if (mouse_world.y() >= line_top and mouse_world.y() < line_bottom) {
                            line_index = i;
                            hovered_line = (mouse_world.x() >= line_bbox.left() and mouse_world.x() < line_bbox.right());
                            // If mouse is over this line, find the exact character
                            if (hovered_line) {
                                const range = page.characterRangeAtLine(block_index, i);
                                // Default to end of line if not over any specific character
                                char_index = range.end();
                                // Find the specific character under cursor
                                for (range.start()..range.end()) |ci| {
                                    if (page.characterBBox(block_index, ci)) |char_bbox| {
                                        // If mouse is before character center, select this character
                                        // Otherwise select next character
                                        if (mouse_world.x() < char_bbox.center().x()) {
                                            char_index = ci;
                                            break;
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
                page.hovered_block_info = .{
                    .block_index = block_index,
                    .line_index = line_index,
                    .char_index = char_index,
                    .hover_line = hovered_line,
                };
                // Only break if we found a line that contains the mouse horizontally
                if (hovered_line) break;
            }
        }
    }
}

/// Build layout for all blocks in the page.
pub fn buildLayout(page: *Page, font_db: *FontDB) !void {
    try page.doc.buildDocLayout(font_db, page.width);
}

// pub const GizmoOptions = struct {
//     cursor_color: Color = Color.initWithHex("#000000"),
//     selection_color: Color = Color.init(0.3, 0.5, 1.0, 0.4),
//     drag_handle_color: Color = Color.initWithHex("#C0BFC1"),
//     cursor_width: f32 = 1.5,
// };
/// Draw gizmos for the page.
///
/// Make sure this is called every frame while the page is visible.
pub fn drawGizmos(page: *Page, delta: f32) void {
    switch (page.edit_mode) {
        .editing => {
            // Cursor blinking
            page.mouse.cursor_blink_timer += delta;
            if (page.mouse.cursor_blink_timer >= config.cursor_blink_time) {
                page.mouse.cursor_visible = !page.mouse.cursor_visible;
                page.mouse.cursor_blink_timer = 0;
            }
            ext.onUpdatePageOffset(page.edit_node_id.?);

            if (page.mouse.cursor_visible) {
                if (page.cursorLine()) |line| {
                    // Composition mode
                    const width: f32 = 4.0;
                    const left = line.x1();
                    const top = line.y1();
                    const bottom = line.y2();
                    const height = @abs(bottom - top);
                    app.setInputRect(
                        @intFromFloat(left + page.offset.x()),
                        @intFromFloat(top + page.offset.y()),
                        @intFromFloat(width),
                        @intFromFloat(height),
                    );
                }
            }

            // move the selection and caret to phase panes.index.js
            // becuase if we do it here, the UI will got delayed
        },
        else => {},
    }
}

pub fn handleEvent(page: *Page, ev: *const app.Event) !void {
    const mouse_world = math.vec2f(
        (ev.mouse_x / app.dpiScale() - page.offset.x()) / page.view_zoom,
        (ev.mouse_y / app.dpiScale() - page.offset.y()) / page.view_zoom,
    );
    page.mouse.world = mouse_world;
    switch (page.edit_mode) {
        .editing => {
            switch (ev.type) {
                .MOUSE_DOWN => {
                    if (page.force_default_cursor) return;
                    if (page.isPointInContent(mouse_world.x(), mouse_world.y())) {
                        page.searchHoverBlock(mouse_world);
                        if (page.hovered_block_info) |info| {
                            // Reset click count if too much time passed or mouse moved too far
                            if ((time.sec(time.now()) - page.mouse.last_click_time) > config.multi_click_time or
                                page.mouse.last_click_pos.distance(mouse_world) > config.multi_click_distance or
                                page.mouse.last_click_block != info.block_index)
                            {
                                page.mouse.click_count = 0;
                            }
                            // Increment click count for left clicks
                            if (ev.mouse_button == .LEFT) {
                                page.mouse.click_count += 1;
                            }
                            if (ev.mouse_button == .LEFT and page.mouse.click_count == 3) {
                                // Handle triple click - select whole line
                                const line_range = page.doc.characterRangeAtLine(info.block_index, info.line_index);
                                page.doc.selectRange(
                                    info.block_index,
                                    line_range.start(),
                                    info.block_index,
                                    line_range.end(),
                                );
                                page.updateTextRangeBySelection();
                                // Move cursor to end of the selected range
                                page.doc.active_block_index = info.block_index;
                                page.doc.cursor_char_index = line_range.end();
                                page.forceCursorVisible();
                            } else if (ev.mouse_button == .LEFT and page.mouse.click_count == 2) {
                                // Handle double click - select word
                                page.doc.active_block_index = info.block_index;
                                page.doc.cursor_char_index = info.char_index;
                                page.doc.selectWordInner();
                                page.updateTextRangeBySelection();
                            } else {
                                // Single click behavior
                                page.clearSelection();
                                page.doc.active_block_index = info.block_index;
                                page.doc.cursor_char_index = info.char_index;
                                page.drag_start = .{
                                    .block_index = info.block_index,
                                    .char_index = page.doc.cursor_char_index,
                                };
                                page.updateTextRangeByCursorPos();
                                page.forceCursorVisible();
                            }
                            // Update click tracking state
                            page.mouse.last_click_time = time.sec(time.now());
                            page.mouse.last_click_pos = mouse_world;
                            page.mouse.last_click_block = info.block_index;
                        } else {
                            // Reset click tracking when clicking outside blocks
                            page.mouse.last_click_block = null;
                            page.mouse.click_count = 0;
                            page.forceCursorVisible();
                        }
                    }
                },
                .MOUSE_MOVE => {
                    if (page.force_default_cursor) return;
                    page.searchHoverBlock(mouse_world);
                    if (page.hovered_block_info) |info| {
                        if (info.hover_line) {
                            // Update selection while dragging
                            if (page.drag_start) |start| {
                                page.doc.active_block_index = info.block_index;
                                page.doc.cursor_char_index = info.char_index;
                                // Always set selection with ordered block/char indices
                                if (info.block_index < start.block_index or
                                    (info.block_index == start.block_index and info.char_index <= start.char_index))
                                {
                                    page.doc.selectRange(
                                        info.block_index,
                                        info.char_index,
                                        start.block_index,
                                        start.char_index,
                                    );
                                } else {
                                    page.doc.selectRange(
                                        start.block_index,
                                        start.char_index,
                                        info.block_index,
                                        info.char_index,
                                    );
                                }
                                page.updateTextRangeBySelection();
                                page.forceCursorVisible();
                            }
                        }
                    }
                },
                .MOUSE_UP => {
                    if (page.force_default_cursor) return;
                    if (page.doc.selection) |selection| {
                        if (selection.start_block == selection.end_block and selection.start_char == selection.end_char) {
                            page.doc.clearSelection();
                        }
                    }
                    page.drag_start = null;
                },
                .CHAR => {
                    if (page.doc.hasSelection()) {
                        try page.doc.deleteSelection();
                    }
                    if (ev.composition_len > 0) {
                        try page.doc.insertText(ev.composition_ptr[0..ev.composition_len]);
                    } else if (ev.char_code != 0) {
                        try page.doc.insertChar(@intCast(ev.char_code));
                    } else {
                        return;
                    }
                    page.hovered_block_info = null;
                    page.updateTextContentAndRange();
                    page.forceCursorVisible();
                },
                .TEXT_EDITING => {
                    if (page.doc.hasSelection()) {
                        try page.doc.deleteSelection();
                    }
                    try page.doc.insertCompositionText(ev.composition_ptr[0..ev.composition_len]);
                    page.hovered_block_info = null;
                    page.updateTextContentAndRange();
                    page.forceCursorVisible();
                },
                .KEY_DOWN => {
                    switch (ev.key_code) {
                        // Move cursor
                        .LEFT => {
                            page.show_end_of_line_cursor = false;
                            if (app.isOSX() and ev.modifiers.super) {
                                if (!ev.modifiers.shift) {
                                    page.clearSelection();
                                    page.doc.gotoLineStart();
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    page.doc.extendToLineStart();
                                    page.updateTextRangeBySelection();
                                }
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                if (!ev.modifiers.shift) {
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                        page.doc.active_block_index = page.doc.selection.?.start_block;
                                        page.clearSelection();
                                    }
                                    page.doc.movePrevWordStart();
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    if (page.doc.hasSelection()) {
                                        if (page.doc.selection.?.start_char != page.doc.cursor_char_index) {
                                            page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                            page.doc.active_block_index = page.doc.selection.?.start_block;
                                        }
                                    }
                                    page.extendPrevWordStart();
                                    page.updateTextRangeBySelection();
                                }
                            } else {
                                if (!ev.modifiers.shift) {
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                        page.doc.active_block_index = page.doc.selection.?.start_block;
                                        page.clearSelection();
                                    } else {
                                        page.doc.moveCharLeft();
                                    }
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    page.extendCharLeft();
                                    page.updateTextRangeBySelection();
                                }
                            }
                            page.forceCursorVisible();
                        },
                        .RIGHT => {
                            page.show_end_of_line_cursor = false;

                            if (app.isOSX() and ev.modifiers.super) {
                                if (!ev.modifiers.shift) {
                                    page.clearSelection();
                                    page.doc.gotoVisualLineEnd();
                                    page.show_end_of_line_cursor = true;
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    page.doc.extendToLineEnd();
                                    page.updateTextRangeBySelection();
                                }
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                if (!ev.modifiers.shift) {
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                        page.doc.active_block_index = page.doc.selection.?.end_block;
                                        page.clearSelection();
                                    }
                                    page.doc.moveNextWordEnd();
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    if (page.doc.hasSelection()) {
                                        if (page.doc.selection.?.end_char != page.doc.cursor_char_index) {
                                            page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                            page.doc.active_block_index = page.doc.selection.?.end_block;
                                        }
                                    }
                                    page.extendNextWordEnd();
                                    page.updateTextRangeBySelection();
                                }
                            } else {
                                if (!ev.modifiers.shift) {
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                        page.doc.active_block_index = page.doc.selection.?.end_block;
                                        page.clearSelection();
                                    } else {
                                        page.doc.moveCharRight();
                                    }
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    page.extendCharRight();
                                    page.updateTextRangeBySelection();
                                }
                            }
                            page.forceCursorVisible();
                        },
                        .UP => {
                            page.show_end_of_line_cursor = false;

                            if (app.isOSX() and ev.modifiers.super) {
                                if (!ev.modifiers.shift) {
                                    // Move to the first line
                                    if (page.doc.hasSelection()) {
                                        page.clearSelection();
                                    }
                                    page.doc.active_block_index = 0;
                                    page.doc.cursor_char_index = 0;
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    // extend to the first line
                                    // page.doc.extendToLineStart();
                                    if (page.doc.hasSelection()) {
                                        page.doc.selection.?.start_block = 0;
                                        page.doc.selection.?.start_char = 0;
                                        page.updateTextRangeBySelection();
                                    } else {
                                        page.doc.selectRange(
                                            0,
                                            0,
                                            page.doc.active_block_index,
                                            page.doc.cursor_char_index,
                                        );
                                    }
                                    page.doc.active_block_index = 0;
                                    page.doc.cursor_char_index = 0;
                                    page.updateTextRangeBySelection();
                                }
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                if (!ev.modifiers.shift) {
                                    // move to the first character of the active line
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                        page.doc.active_block_index = page.doc.selection.?.start_block;
                                        page.clearSelection();
                                    }
                                    if (page.doc.cursor_char_index == 0) {
                                        page.doc.moveVisualLineUp();
                                    }
                                    page.doc.gotoLineStart();

                                    page.updateTextRangeByCursorPos();
                                } else {
                                    if (page.doc.hasSelection()) {
                                        if (page.doc.selection.?.start_char != page.doc.cursor_char_index) {
                                            page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                            page.doc.active_block_index = page.doc.selection.?.start_block;
                                            page.clearSelection();
                                        }
                                    }
                                    if (page.doc.hasSelection() and page.doc.cursor_char_index == 0 and page.doc.active_block_index > 0) {
                                        page.doc.active_block_index -= 1;
                                        page.doc.selection.?.start_block -= 1;
                                    }
                                    page.doc.extendToLineStart();
                                    page.updateTextRangeBySelection();
                                }
                            } else if (!ev.modifiers.shift) {
                                if (page.doc.hasSelection()) {
                                    page.doc.active_block_index = page.doc.selection.?.start_block;
                                    page.doc.cursor_char_index = page.doc.selection.?.start_char;
                                    page.clearSelection();
                                }
                                page.doc.moveVisualLineUp();
                                page.updateTextRangeByCursorPos();
                            } else {
                                page.extendVisualLineUp();
                                page.updateTextRangeBySelection();
                            }
                            page.forceCursorVisible();
                        },
                        .DOWN => {
                            page.show_end_of_line_cursor = false;

                            if (app.isOSX() and ev.modifiers.super) {
                                if (ev.modifiers.shift) {
                                    // extend to the end of the document
                                    if (page.doc.hasSelection()) {
                                        page.doc.active_block_index = page.doc.blocks.items.len - 1;
                                        page.doc.cursor_char_index = page.doc.blocks.items[page.doc.blocks.items.len - 1].data.string.length();
                                        page.doc.selection.?.end_block = page.doc.blocks.items.len - 1;
                                        page.doc.selection.?.end_char = page.characterCount(page.doc.blocks.items.len - 1);
                                    } else {
                                        page.doc.selectRange(
                                            page.doc.active_block_index,
                                            page.doc.cursor_char_index,
                                            page.doc.blocks.items.len - 1,
                                            page.characterCount(page.doc.blocks.items.len - 1),
                                        );
                                        page.doc.cursor_char_index = page.doc.blocks.items[page.doc.blocks.items.len - 1].data.string.length();
                                        page.doc.active_block_index = page.doc.blocks.items.len - 1;
                                    }
                                    page.updateTextRangeBySelection();
                                } else {
                                    // move to the end of the document and clear selection
                                    if (page.doc.hasSelection()) {
                                        page.clearSelection();
                                    }
                                    page.doc.active_block_index = page.doc.blocks.items.len - 1;
                                    page.doc.cursor_char_index = page.doc.blocks.items[page.doc.blocks.items.len - 1].data.string.length();
                                    page.updateTextRangeByCursorPos();
                                }
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                if (!ev.modifiers.shift) {
                                    // move to the first character of the active line
                                    if (page.doc.hasSelection()) {
                                        page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                        page.doc.active_block_index = page.doc.selection.?.end_block;
                                        page.clearSelection();
                                    }
                                    // page.doc.moveVisualLineDown();
                                    if (page.doc.cursor_char_index == page.characterCount(page.doc.active_block_index) and page.doc.active_block_index < page.doc.blocks.items.len - 1) {
                                        page.doc.active_block_index += 1;
                                    }
                                    page.doc.gotoLineEnd();
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    if (page.doc.hasSelection()) {
                                        if (page.doc.selection.?.end_char != page.doc.cursor_char_index) {
                                            page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                            page.doc.active_block_index = page.doc.selection.?.end_block;
                                            page.clearSelection();
                                        }
                                    }
                                    // page.doc.extendVisualLineDown();
                                    if (page.doc.cursor_char_index == page.characterCount(page.doc.active_block_index) and page.doc.active_block_index < page.doc.blocks.items.len - 1) {
                                        page.doc.active_block_index += 1;
                                    }
                                    page.doc.extendToLineEnd();
                                    page.updateTextRangeBySelection();
                                }
                            } else {
                                if (!ev.modifiers.shift) {
                                    if (page.doc.hasSelection()) {
                                        page.doc.active_block_index = page.doc.selection.?.end_block;
                                        page.doc.cursor_char_index = page.doc.selection.?.end_char;
                                        page.clearSelection();
                                    } else {
                                        page.doc.moveVisualLineDown();
                                    }
                                    page.updateTextRangeByCursorPos();
                                } else {
                                    page.extendVisualLineDown();
                                    page.updateTextRangeBySelection();
                                }
                            }
                            page.forceCursorVisible();
                        },
                        // Enter support
                        .ENTER, .KP_ENTER => {
                            if (page.doc.selection) |selection| {
                                try page.doc.deleteRange(selection.start_block, selection.start_char, selection.end_block, selection.end_char);
                                page.clearSelection();
                            }
                            try page.doc.splitAtCursor();
                            page.hovered_block_info = null;
                            page.updateTextContentAndRange();
                            page.forceCursorVisible();
                        },
                        // Delete support
                        .BACKSPACE => {
                            page.show_end_of_line_cursor = false;

                            if (page.doc.selection) |selection| {
                                try page.doc.deleteRange(selection.start_block, selection.start_char, selection.end_block, selection.end_char);
                                page.clearSelection();
                            } else if (app.isOSX() and ev.modifiers.super) {
                                try page.doc.killToLineStart();
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                try page.doc.deleteWordBackward();
                            } else {
                                try page.doc.deleteCharBackward();
                            }
                            page.hovered_block_info = null;
                            page.updateTextContentAndRange();
                            page.forceCursorVisible();
                        },
                        .DELETE => {
                            page.show_end_of_line_cursor = false;

                            if (page.doc.selection) |selection| {
                                try page.doc.deleteRange(selection.start_block, selection.start_char, selection.end_block, selection.end_char);
                                page.clearSelection();
                            } else if (app.isOSX() and ev.modifiers.super) {
                                try page.doc.killToLineEnd();
                            } else if ((app.isOSX() and ev.modifiers.alt) or (!app.isOSX() and ev.modifiers.ctrl)) {
                                try page.doc.deleteWordForward();
                            } else {
                                try page.doc.deleteCharForward();
                            }
                            page.hovered_block_info = null;
                            page.updateTextContentAndRange();
                            page.forceCursorVisible();
                        },
                        // Clipboard support
                        .C => {
                            if (ev.modifiers.super) {
                                if (page.doc.selection) |selection| {
                                    const start_char = if (selection.start_char > selection.end_char) selection.end_char else selection.start_char;
                                    const end_char = if (selection.start_char > selection.end_char) selection.start_char else selection.end_char;
                                    const options = NoteDoc.GetTextInRangeOptions{
                                        .start_block = selection.start_block,
                                        .start_char = start_char,
                                        .end_block = selection.end_block,
                                        .end_char = end_char,
                                        .with_sentinel = true,
                                    };
                                    const text = try page.doc.getTextInRange(page.allocator, options);
                                    defer page.allocator.free(text);
                                    ext.onCopyText(text.ptr, text.len);
                                    // app.setClipboardString(@ptrCast(text));
                                }
                            }
                            page.hovered_block_info = null;
                            page.forceCursorVisible();
                        },
                        .X => {
                            if (ev.modifiers.super) {
                                if (page.doc.selection) |selection| {
                                    const start_char = if (selection.start_char > selection.end_char) selection.end_char else selection.start_char;
                                    const end_char = if (selection.start_char > selection.end_char) selection.start_char else selection.end_char;
                                    const text = try page.doc.getTextInRange(page.allocator, .{
                                        .start_block = selection.start_block,
                                        .start_char = start_char,
                                        .end_block = selection.end_block,
                                        .end_char = end_char,
                                        .with_sentinel = true,
                                    });
                                    defer page.allocator.free(text);
                                    ext.onCopyText(text.ptr, text.len);
                                    try page.doc.deleteRange(
                                        selection.start_block,
                                        selection.start_char,
                                        selection.end_block,
                                        selection.end_char,
                                    );
                                    // app.setClipboardString(@ptrCast(text));
                                    page.updateTextContentAndRange();
                                    page.clearSelection();
                                }
                            }
                            page.hovered_block_info = null;
                            page.forceCursorVisible();
                        },
                        .Z => {
                            if (ev.modifiers.super) {
                                if (!ev.modifiers.shift) {
                                    ext.onUndoComb();
                                } else {
                                    ext.onRedoComb();
                                }
                            }
                        },
                        .A => {
                            if (ev.modifiers.super) {
                                selectAll(page);
                            }
                        },
                        ._0 => {
                            if (ev.modifiers.super) {
                                ext.onZoom(1.0);
                            }
                        },
                        ._1 => {
                            if (ev.modifiers.super) {
                                ext.onZoomToFit();
                            }
                        },
                        ._2 => {
                            if (ev.modifiers.super) {
                                ext.onZoomToSelection();
                            }
                        },
                        .MINUS => {
                            if (ev.modifiers.super) {
                                ext.onZoomOut();
                            }
                        },
                        .EQUAL => {
                            if (ev.modifiers.super) {
                                ext.onZoomIn();
                            }
                        },
                        .ESCAPE => {
                            ext.onESC();
                        },
                        .TAB => {
                            if (ev.modifiers.shift) {
                                ext.onShiftTab();
                            } else {
                                ext.onTab();
                            }
                            selectAll(page);
                        },
                        else => {},
                    }
                },
                .CLIPBOARD_PASTED => {
                    if (page.doc.hasSelection()) {
                        try page.doc.deleteSelection();
                    }
                    page.forceCursorVisible();
                    ext.onPasteText();
                },
                else => {},
            }
        },
        else => {},
    }
}

pub fn selectAll(page: *Page) void {
    // move the cursor to the end of the document
    page.doc.cursor_char_index = page.doc.blocks.items[page.doc.blocks.items.len - 1].data.string.length();
    // select the entire document
    page.doc.selectRange(0, 0, page.doc.blocks.items.len - 1, page.doc.blocks.items[page.doc.blocks.items.len - 1].data.string.length());

    page.forceCursorVisible();
    page.updateTextRangeBySelection();
}

pub fn isPointInContent(page: *Page, x: f32, y: f32) bool {
    if (page.contentBBox()) |bbox| {
        return (x >= bbox.left() and x < bbox.right() and y >= bbox.top() and y < bbox.bottom());
    }
    return false;
}

pub fn isPointInBlock(page: *Page, x: f32, y: f32) bool {
    const doc = page.doc;
    for (doc.blocks.items, 0..) |*block, block_index| {
        const offset = doc.blockOffset(block_index);
        if (block.data.blockBBox()) |b| {
            const bbox = math.rect2f(
                b.left() + offset.x(),
                b.top() + offset.y(),
                b.width(),
                b.height(),
            );
            // std.log.warn("contains: {}", .{bbox.contains(math.vec2f(x, y))});
            if (bbox.contains(math.vec2f(x, y))) {
                return true;
            }
        }
    }
    return false;
}

fn getLineIndexAtCursor(page: *Page) usize {
    return page.doc.getLineIndexAtCursor();
}

pub fn getLineIndexByBlockAndCharIndex(page: *Page, block_index: usize, char_index: usize) usize {
    return page.doc.blocks.items[block_index].data.lineIndexAt(char_index);
}

fn getWholeDocText(page: *Page) []const u8 {
    // TODO: free the memory
    var text = std.ArrayList(u8).init(page.allocator);
    for (page.doc.blocks.items, 0..) |block, block_index| {
        if (block_index != 0) {
            text.append('\n') catch unreachable;
        }
        text.appendSlice(block.data.string.data.items) catch unreachable;
    }
    return text.items;
}

pub fn forceCursorVisible(page: *Page) void {
    page.mouse.cursor_visible = true;
    page.mouse.cursor_blink_timer = 0;
}

pub fn updateTextContentAndRange(page: *Page) void {
    const text = getWholeDocText(page);
    const range = getTextRange(
        text,
        page.doc.active_block_index,
        page.doc.cursor_char_index,
        page.doc.active_block_index,
        page.doc.cursor_char_index,
        page.doc.cursor_char_index,
        page.doc.active_block_index,
    );
    if (range) |r| {
        ext.onTextUpdate(text.ptr, text.len, isComposing(page.doc), r.start, r.end, r.cursor_idx);
    } else {
        ext.onTextUpdate(text.ptr, text.len, isComposing(page.doc), 0, 0, 0);
    }
}

pub fn updateTextRangeByCursorPos(page: *Page) void {
    ext.onSelectionUpdate(
        page.edit_node_id.?,
        page.doc.active_block_index,
        page.doc.cursor_char_index,
        page.doc.active_block_index,
        page.doc.cursor_char_index,
        page.doc.cursor_char_index,
        page.doc.active_block_index,
    );
}

pub fn updateTextRangeBySelection(page: *Page) void {
    if (page.doc.selection) |selection| {
        ext.onSelectionUpdate(
            page.edit_node_id.?,
            selection.start_block,
            selection.start_char,
            selection.end_block,
            selection.end_char,
            page.doc.cursor_char_index,
            page.doc.active_block_index,
        );
    }
}
fn getTextRange(
    content: []const u8,
    start_block: usize,
    start_char: usize,
    end_block: usize,
    end_char: usize,
    cursor: usize,
    active_block_index: usize,
) ?struct {
    start: usize,
    end: usize,
    cursor_idx: usize,
} {
    // Create a UTF-8 iterator to properly handle multi-byte characters
    var utf8_view = std.unicode.Utf8View.init(content) catch |err| {
        std.log.err("Invalid UTF-8 in content: {}", .{err});
        return null;
    };
    var utf8_iter = utf8_view.iterator();

    var byte_index: usize = 0; // Current byte position in the content
    var char_index: usize = 0; // Current character (codepoint) position within current block
    var block_index: usize = 0; // Current block index
    var start: ?usize = null; // Byte position for selection start
    var end: ?usize = null; // Byte position for selection end
    var cursor_index: ?usize = null; // Byte position for cursor placement
    var letter_index: usize = 0;

    // Process each codepoint (character) in the content
    while (utf8_iter.nextCodepointSlice()) |codepoint_slice| {
        // Check if we need to set cursor position
        if (cursor_index == null and block_index == active_block_index) {
            cursor_index = letter_index + cursor;
        }

        // Check if we need to set start position
        if (block_index == start_block and start == null) {
            start = letter_index + start_char;
        }

        // Check if we need to set end position
        if (block_index == end_block and end == null) {
            end = letter_index + end_char;
        }

        // Check if this is a newline character (block separator)
        if (codepoint_slice.len == 1 and codepoint_slice[0] == '\n') {
            block_index += 1;
            char_index = 0; // Reset character index for the new block
        } else {
            char_index += 1; // Increment character index within the current block
        }

        // Advance byte index by the size of the current codepoint
        byte_index += codepoint_slice.len;
        letter_index += 1;

        // If we've found both start and end positions, we can stop
        if (start != null and end != null and cursor_index != null) {
            break;
        }
    }

    // Special case: if we're at the end of a block and need to set positions
    if (block_index == start_block and start == null) {
        start = letter_index;
    }
    if (block_index == end_block and end == null) {
        end = letter_index;
    }
    if (block_index == active_block_index and cursor_index == null) {
        cursor_index = letter_index;
    }

    // Handle case where indices are not found
    if (start == null or end == null or cursor_index == null) {
        start = content.len;
        end = content.len;
        cursor_index = if (cursor_index) |c| c else content.len;
    }

    return .{
        .start = start.?,
        .end = end.?,
        .cursor_idx = cursor_index.?,
    };
}

fn isComposing(doc: *NoteDoc) bool {
    const block = doc.blocks.items[doc.active_block_index];
    if (block.data.composition_text) |composition_text| {
        _ = composition_text;
        return true;
    }
    return false;
}
