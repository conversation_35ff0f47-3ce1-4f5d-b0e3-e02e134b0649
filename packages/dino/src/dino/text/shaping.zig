const std = @import("std");
const math = @import("../math.zig");
const hb = @import("harfbuzz.zig");
const FontDB = @import("FontDB.zig");
const FontSetting = FontDB.FontSetting;
const style = @import("style.zig");
const Range = @import("Range.zig");
const bidi = @import("bidi.zig");
const unicode_ranges = @import("../../dino/text/unicode_ranges.zig");

const ext = struct {
    extern fn onReady() void;
    extern fn onFontRequest(font_family_ptr: [*]const u8, font_family_len: usize, font_style: u32, font_weight: u32) void;
};

pub fn init(allocator: std.mem.Allocator, font_db: *FontDB, default_font_id: u32) !void {
    static.arena = std.heap.ArenaAllocator.init(allocator);

    static.default_font_id = default_font_id;
    static.buf = hb.Buffer.init().?;
    const font = font_db.getPtrByID(static.default_font_id).?;

    const code = try std.unicode.utf8Decode(static.missing_char);
    static.missing_char_gid = font.tt_face.getGlyphIndex(@intCast(code));
    static.buf.clearContents();
    static.buf.reset();
    static.buf.addUTF8(static.missing_char, 0, @intCast(static.missing_char.len));
    static.buf.setDirection(.ltr);
    static.buf.guessSegmentProps();
    font.hb_font.shape(static.buf, null);
    if (static.buf.getGlyphPositions()) |positions| {
        const pos = positions[0];
        static.missing_char_dx = pos.x_offset;
        static.missing_char_dy = pos.y_offset;
        static.missing_char_width = pos.x_advance;
    }
}
pub fn deinit() void {
    static.buf.deinit();
    static.arena.deinit();
}

pub const Glyph = struct {
    /// the id of the glyph in the font.
    id: u16 = 0,
    /// the byte index of the glyph in the text.
    byte_idx: usize = 0,
    /// the length of the cluster in characters.
    cluster_len: usize = 0,
    /// the text of the glyph.
    text: []const u8 = &.{},
    /// the dx of the glyph.
    dx: i32 = 0,
    /// the dy of the glyph.
    dy: i32 = 0,
    /// the width of the glyph.
    width: i32 = 0,
    // FIXME: save a pointer might not be safe if we have font auto-unload mechanism
    font: *FontDB.Font = undefined,
    font_family: u32 = 0,
    codepoint: u32 = 0,
    range_idx: usize = 0,
    is_ltr: bool = true,

    pub fn isMissing(glyph: Glyph) bool {
        return glyph.id == 0;
    }
};
pub const GlyphList = std.ArrayList(Glyph);

/// A glyph that has been positioned.
pub const PositionedGlyph = struct {
    cluster: Cluster = cluster_none,
    /// Transform of the glyph within its cluster.
    glyph_xform: math.Mat2Df = math.mat2df_identity(),
    /// Transform of the span this glyphs belongs to.
    span_xform: math.Mat2Df = math.mat2df_identity(),
    units_per_em: u16 = 0,
    font_size: f32 = 0.0,
    id: u16 = 0,
    byte_idx: usize = 0,
    range_idx: usize = 0,
    /// Text from original string that corresponds to this glyph.
    text: []const u8 = &.{},
    font: FontDB.ID = 0,
    cluster_len: usize = 0,
    font_family: u32 = 0,
    /// Full transform of this glyph.
    pub fn outlineXform(glyph: *PositionedGlyph) math.Mat2Df {
        const sx = glyph.font_size / @as(f32, @floatFromInt(glyph.units_per_em));
        return glyph.span_xform
            .mul(glyph.cluster.transform())
            .mul(math.mat2df_scales(sx))
            .mul(math.mat2df_scale(1.0, -1.0))
            .mul(glyph.glyph_xform);
    }
};
const PositionedGlyphList = std.ArrayList(PositionedGlyph);

pub const Cluster = struct {
    byte_idx: usize = 0,
    codepoint: u32 = 0,
    width: f32 = 0.0,
    adv: f32 = 0.0,
    adv_scale: f32 = 0.0,
    ascent: f32 = 0.0,
    descent: f32 = 0.0,
    has_relative_shift: bool = false,
    glyphs: PositionedGlyphList = undefined,
    xform: math.Mat2Df = math.mat2df_identity(),
    path_xform: math.Mat2Df = math.mat2df_identity(),
    visible: bool = true,
    cluster_len: usize = 1,
    range_idx: usize = 0,

    pub fn advance(cluster: Cluster) f32 {
        return (cluster.adv) + cluster.adv_scale;
    }
    pub fn height(cluster: Cluster) f32 {
        return cluster.ascent - cluster.descent;
    }
    pub fn localBBox(cluster: Cluster) math.Rect2f {
        return math.rect2f(0.0, -cluster.ascent, cluster.advance(), cluster.height());
    }
    pub fn bbox(cluster: Cluster) math.Rect2f {
        return cluster.transform().transformRect(cluster.localBBox());
    }
    pub fn transform(cluster: Cluster) math.Mat2Df {
        return cluster.path_xform.mul(cluster.xform);
    }
};
const cluster_none = Cluster{
    .visible = false,
};
pub const ClusterList = std.ArrayList(Cluster);

pub const Line = struct {
    /// Cluster range for the line.
    range: Range = .{},
    ascent: f32 = 0.0,
    descent: f32 = 0.0,
    width: f32 = 0.0,
    height: f32 = 0.0,
    /// Horizontal position of this line
    x_offset: f32 = 0.0,
    /// Vertical position of this line
    y_offset: f32 = 0.0,

    pub fn init(clusters: ClusterList, range: Range, height: f32) Line {
        var total_width: f32 = 0.0;
        var max_ascent: f32 = 0.0;
        var max_descent: f32 = 0.0;
        // Compute line metrics based on clusters within the range
        for (range.span(clusters.items)) |*cluster| {
            const adv = cluster.advance();
            total_width += adv;
            max_ascent = @max(max_ascent, cluster.ascent);
            max_descent = @min(max_descent, cluster.descent);
        }
        return Line{
            .range = range,
            .ascent = max_ascent,
            .descent = max_descent,
            .width = total_width, // for baseline
            .height = height,
        };
    }
};
pub const LineList = std.ArrayList(Line);

pub const VisualSpan = struct {
    range: Range = .{},
    positioned_glyphs: PositionedGlyphList = undefined,
    bbox: math.Rect2f = math.rect2fse(0.0, 0.0, 0.0, 0.0),
};
pub const VisualSpanList = std.ArrayList(VisualSpan);

pub const Layout = struct {
    visual_spans: VisualSpanList = undefined,
    lines: LineList = undefined,
    pub fn deinit(layout: Layout) void {
        for (layout.visual_spans.items) |*v_span| {
            v_span.positioned_glyphs.deinit();
        }
        layout.visual_spans.deinit();
        layout.lines.deinit();
    }
    pub fn calcBlockBBox(layout: Layout) math.Rect2f {
        var bbox: ?math.Rect2f = null;
        for (layout.visual_spans.items) |*v_span| {
            bbox = math.uniteBBox(bbox, v_span.bbox);
        }
        return bbox orelse math.rect2fse(0.0, 0.0, 0.0, 0.0);
    }
    pub fn findLineIndexOfChar(layout: Layout, char_index: usize) ?usize {
        for (layout.lines.items, 0..) |*line, i| {
            if (line.range.contains(char_index)) {
                // If this is not the last line and we're at the end of this line,
                // return the next line's index
                if (i < layout.lines.items.len - 1 and char_index == line.range.location + line.range.length) {
                    return i + 1;
                }
                return i;
            }
        }
        // If we get here, we're beyond the last line, so return the last line index
        if (layout.lines.items.len > 0) {
            return layout.lines.items.len - 1;
        }
        return null;
    }
};

pub const static = struct {
    var arena: std.heap.ArenaAllocator = undefined;

    pub var default_font_id: u32 = 0;

    var missing_font_rid: u32 = 0;
    var missing_char: []const u8 = "�";
    var missing_char_gid: u16 = 0;
    var missing_char_dx: i32 = 0;
    var missing_char_dy: i32 = 0;
    var missing_char_width: i32 = 0;

    var buf: hb.Buffer = undefined;
};

/// Line will be wrapped only if max_width is positive.
pub fn layoutText(allocator: std.mem.Allocator, font_db: *FontDB, text: []const u8, spans: []style.FlatSpan, max_width: f32) !Layout {
    _ = static.arena.reset(.retain_capacity);
    var lines = LineList.init(allocator);
    var visual_spans = VisualSpanList.init(allocator);
    var clusters = try buildClusters(static.arena.allocator(), font_db, text, spans);
    const horizontalAlignment = spans[0].style.horizontal_alignment;
    const font = if (font_db.getPtr(spans[0].style.font)) |f| f else font_db.getPtrByID(static.default_font_id).?;
    const ascent = @as(f32, @floatFromInt(font.tt_face.getAscender())) / @as(f32, @floatFromInt(font.tt_face.getUnitsPerEm())) * spans[0].style.font_size;
    const descent = @as(f32, @floatFromInt(font.tt_face.getDescender())) / @as(f32, @floatFromInt(font.tt_face.getUnitsPerEm())) * spans[0].style.font_size;
    const line_height = if (spans[0].style.line_height == -1) @round(ascent - descent) else @round(spans[0].style.font_size * spans[0].style.line_height);
    if (clusters.items.len > 0) {
        applyLetterSpacing(spans, &clusters);
        applyWordSpacing(spans, &clusters);
        if (max_width > 0.0) {
            // Resolve lines based on max width
            var curr_line_clusters_start: usize = 0;
            // because the cluster length is not the same as the text length,
            // we need to count the sub_text length
            var curr_line_text_count: usize = 0;
            var total_text_count: usize = 0;
            var curr_width: f32 = 0.0;
            var max_line_width: f32 = 0.0;

            // Track the last whitespace position in the current line
            var last_whitespace_text_count: ?usize = null;
            var last_whitespace_width: ?f32 = null;
            var cluster_index: usize = 0;
            var pre_adv_scale: f32 = 0;

            for (clusters.items) |*cluster| {
                var sub_text_count: usize = 0;
                const is_whitespace = cluster.glyphs.items[0].text[0] == ' ';
                for (cluster.glyphs.items) |*glyph| {
                    const factor = if (glyph.text.len > 0) try std.unicode.utf8ByteSequenceLength(glyph.text[0]) else 1;
                    sub_text_count += glyph.text.len / factor;
                    total_text_count += glyph.text.len / factor;
                }
                const adv = cluster.width;

                // Check if we need to wrap the line
                if ((curr_width + adv > max_width) and (curr_line_text_count > curr_line_clusters_start) and !is_whitespace) {
                    // If we found a whitespace in this line, break at that position
                    if (last_whitespace_text_count != null and last_whitespace_width != null) {
                        const line_ptr = try lines.addOne();
                        line_ptr.* = Line.init(clusters, Range{
                            .location = curr_line_clusters_start,
                            .length = last_whitespace_text_count.? - curr_line_clusters_start,
                        }, line_height);
                        line_ptr.*.width -= pre_adv_scale;
                        max_line_width = @max(max_line_width, line_ptr.width);
                        curr_line_clusters_start = last_whitespace_text_count.?;
                        curr_width = curr_width - last_whitespace_width.?;
                    } else {
                        // No whitespace found, break at current position (original behavior)
                        const line_ptr = try lines.addOne();
                        line_ptr.* = Line.init(clusters, Range{
                            .location = curr_line_clusters_start,
                            .length = curr_line_text_count - curr_line_clusters_start,
                        }, line_height);
                        line_ptr.*.width -= pre_adv_scale;
                        max_line_width = @max(max_line_width, line_ptr.width);
                        curr_line_clusters_start = curr_line_text_count;
                        curr_width = 0.0;
                    }
                    // Reset whitespace tracking for the new line
                    last_whitespace_text_count = null;
                    last_whitespace_width = null;
                }

                // Track whitespace for potential line breaks
                if (is_whitespace) {
                    last_whitespace_text_count = curr_line_text_count + sub_text_count;
                    last_whitespace_width = curr_width + cluster.advance();
                }

                curr_line_text_count += sub_text_count;
                // add cluster and the letter spacing
                curr_width += cluster.advance();
                cluster_index += 1;
                pre_adv_scale = cluster.adv_scale;
            }

            // Add the last line
            if (curr_line_clusters_start < total_text_count) {
                const line_ptr = try lines.addOne();
                line_ptr.* = Line.init(clusters, Range{
                    .location = curr_line_clusters_start,
                    .length = curr_line_text_count - curr_line_clusters_start,
                }, line_height);
                max_line_width = @max(max_line_width, line_ptr.width);
            }

            // Resolve cluster xform
            var y: f32 = 0.0;
            for (lines.items) |*line| {
                line.y_offset = y;
                var x: f32 = 0.0;
                if (horizontalAlignment == .center) {
                    x = (max_line_width - line.width) * 0.5;
                } else if (horizontalAlignment == .right) {
                    x = max_line_width - line.width;
                }
                line.x_offset = x;
                for (line.range.span(clusters.items)) |*cluster| {
                    cluster.xform = math.mat2df_translate(x, y);
                    x += cluster.advance();
                }
                y += line_height;
            }
        } else {
            // Simply create a line for all clusters.
            var cluster_sub_text_count: usize = 0;
            for (clusters.items) |*cluster| {
                for (cluster.glyphs.items) |*glyph| {
                    const factor = if (glyph.text.len > 0) try std.unicode.utf8ByteSequenceLength(glyph.text[0]) else 1;
                    cluster_sub_text_count += glyph.text.len / factor;
                }
            }
            try lines.append(Line.init(clusters, Range{
                .length = cluster_sub_text_count,
            }, line_height));
            // Resolve cluster xform
            var x: f32 = 0.0;
            for (clusters.items) |*cluster| {
                cluster.xform = math.mat2df_translate(x, 0.0);
                x += cluster.advance();
            }
        }

        for (spans) |*span| {
            // TODO: decorations
            const span_xform = math.mat2df_identity();
            // TODO: decoration to path
            if (buildVisualSpan(allocator, clusters, span, span_xform)) |v_span| {
                try visual_spans.append(v_span);
            } else {
                // We give it an empty visual span for spans that don't generate glyphs.
                // This makes sure that Span number always matches VisualSpan.
                try visual_spans.append(VisualSpan{
                    .range = span.range,
                    .positioned_glyphs = PositionedGlyphList.init(allocator),
                    .bbox = math.rect2f(0, 0, 0, 0),
                });
            }
        }
    } else {
        // empty line height should apply line spacing
        const offset = @ceil((ascent - descent - line_height) / 2.0);

        try visual_spans.append(VisualSpan{
            .positioned_glyphs = PositionedGlyphList.init(allocator),
            .bbox = math.rect2f(0, @floor(-ascent + offset), 0, line_height),
        });
        // Single empty line
        try lines.append(Line{
            .ascent = ascent,
            .descent = descent,
            .width = 0.0,
            .height = line_height,
        });
    }
    return Layout{
        .visual_spans = visual_spans,
        .lines = lines,
    };
}

fn buildClusters(allocator: std.mem.Allocator, font_db: *FontDB, text: []const u8, spans: []style.FlatSpan) !ClusterList {
    var is_first_span = true;
    var all_glyphs: GlyphList = undefined;
    var has_found_glyphs = false;
    for (spans) |*span| {
        // Skip invalid ranges
        if ((span.range.length == 0) or (span.range.location >= text.len)) continue;
        // Fallback to default one if desired font is missing
        const font = if (font_db.getPtr(span.style.font)) |f| f else font_db.getPtrByID(static.default_font_id).?;

        var fallback_fonts = std.StringHashMap(void).init(static.arena.allocator());
        var fallback_font_style: FontDB.Style = .normal;
        var fallback_font_weight: FontDB.Weight = .regular;
        // Shape the entire text range with given font

        var current_span_glyphs = try shapeTextForFont(allocator, text, font);
        defer current_span_glyphs.deinit();
        // First pass, just copy
        if (is_first_span) {
            is_first_span = false;
            has_found_glyphs = true;
            var i: usize = 0;

            while (i < current_span_glyphs.items.len) : (i += 1) {
                const glyph = &current_span_glyphs.items[i];
                const font_setting = FontDB.getFontSetting(font_db, glyph.font.id);
                if (font_setting) |setting| {
                    _ = setting;
                    glyph.font_family = glyph.font.id;
                }

                // Replace glyph with missing character
                if (glyph.isMissing()) {
                    if (font_setting) |setting| {
                        fallback_font_style = setting.style;
                        fallback_font_weight = setting.weight;
                    }
                    if (glyph.text.len > 0) {
                        if (std.unicode.utf8ByteSequenceLength(glyph.text[0])) |len| {
                            const codepoint = try std.unicode.utf8Decode(glyph.text[0..len]);
                            glyph.codepoint = codepoint;
                            const fallback_font = unicode_ranges.fallbackFontForCodepoint(codepoint);

                            try fallback_fonts.put(fallback_font, {});
                        } else |_| {
                            break;
                        }
                    }
                }
            }
            all_glyphs = try current_span_glyphs.clone();
        }

        const whitespace_glyph = try shapeTextForFont(allocator, " ", font);
        defer whitespace_glyph.deinit();
        const missing_char = try shapeTextForFont(allocator, static.missing_char, font_db.getPtrByID(static.default_font_id).?);
        defer missing_char.deinit();

        // for all fallback font, and get
        var iter = fallback_fonts.keyIterator();
        while (iter.next()) |fallback_font| {
            var font_data = font_db.getPtrByID(static.default_font_id).?;
            var is_using_whitespace_glyph = false;
            if (font_db.getPtr(.{
                .family = fallback_font.*,
                .weight = fallback_font_weight,
                .style = fallback_font_style,
            })) |data| {
                font_data = data;
            } else {
                const font_family_slice = try allocator.dupeZ(u8, fallback_font.*);
                ext.onFontRequest(font_family_slice.ptr, @intCast(font_family_slice.len), @intFromEnum(fallback_font_style), FontDB.Weight.get(fallback_font_weight));
                allocator.free(font_family_slice);
                is_using_whitespace_glyph = true;
            }

            var new_font_glyphs = try shapeTextForFont(allocator, text, font_data);
            defer new_font_glyphs.deinit();

            var is_replaced = false;
            var i: usize = current_span_glyphs.items.len;
            var j_offset: usize = 0;
            while (i > 0) : (i -= 1) {
                const glyph = &current_span_glyphs.items[i - 1];
                if (glyph.isMissing()) {
                    var j: usize = new_font_glyphs.items.len - j_offset;
                    var is_found = false;
                    while (j > 0) : (j -= 1) {
                        const new_glyph = &new_font_glyphs.items[j - 1];
                        new_glyph.font_family = font_data.id;
                        if (new_glyph.byte_idx == glyph.byte_idx) {
                            is_found = true;
                            j_offset += 1;
                            var same_byte_glyph_count: usize = 0;
                            var same_byte_new_glyph_count: usize = 0;
                            if (!new_glyph.isMissing()) {
                                is_replaced = true;
                                while (i - 1 >= same_byte_glyph_count and current_span_glyphs.items[i - 1 - same_byte_glyph_count].byte_idx == glyph.byte_idx) : (same_byte_glyph_count += 1) {}
                                while (j - 1 >= same_byte_new_glyph_count and new_font_glyphs.items[j - 1 - same_byte_new_glyph_count].byte_idx == glyph.byte_idx) : (same_byte_new_glyph_count += 1) {}

                                try current_span_glyphs.replaceRange(i - same_byte_glyph_count, same_byte_glyph_count, new_font_glyphs.items[j - same_byte_new_glyph_count .. j]);
                                i -= same_byte_glyph_count - 1;
                                j_offset += same_byte_new_glyph_count - 1;
                            } else {
                                // the fallback font is not loaded, use the whitespace glyph instead
                                if (new_glyph.cluster_len == 0) {
                                    j = if (j > 1) j - 1 else j;
                                }
                                is_replaced = true;
                                if (current_span_glyphs.items[i - 1].cluster_len == 0) {
                                    _ = current_span_glyphs.orderedRemove(i - 1);
                                    _ = all_glyphs.orderedRemove(i - 1);
                                } else {
                                    const range = current_span_glyphs.items[i - 1].range_idx;
                                    // if the fallback font is not loaded, use the whitespace glyph instead
                                    if (is_using_whitespace_glyph) {
                                        current_span_glyphs.items[i - 1] = whitespace_glyph.items[0];
                                        current_span_glyphs.items[i - 1].range_idx = range;
                                        current_span_glyphs.items[i - 1].font_family = all_glyphs.items[i - 1].font_family;
                                        all_glyphs.items[i - 1].cluster_len = whitespace_glyph.items[0].cluster_len;
                                    } else {
                                        // if the fallback font is loaded, use the missing character glyph instead
                                        current_span_glyphs.items[i - 1] = missing_char.items[0];
                                        current_span_glyphs.items[i - 1].range_idx = range;
                                        current_span_glyphs.items[i - 1].font_family = all_glyphs.items[i - 1].font_family;
                                        all_glyphs.items[i - 1].cluster_len = missing_char.items[0].cluster_len;
                                    }
                                }
                            }
                            break;
                        }
                    }
                    if (!is_found) {
                        _ = current_span_glyphs.orderedRemove(i - 1);
                    }
                }
            }

            // update the byte_idx and range_idx for the current_span_glyphs and all_glyphs
            if (is_replaced) {
                var byte_index: usize = 0;
                var range_idx: usize = 0;
                for (current_span_glyphs.items) |*glyph| {
                    if (!glyph.is_ltr) {
                        if (range_idx != 0 and glyph.cluster_len == 0) {
                            range_idx -= 1;
                        }
                    }
                    glyph.byte_idx = byte_index;
                    byte_index += glyph.cluster_len;
                    glyph.range_idx = range_idx;
                    if (glyph.is_ltr) {
                        range_idx += if (glyph.cluster_len == 0) 0 else 1;
                    } else {
                        range_idx += 1;
                    }
                }
                byte_index = 0;
                range_idx = 0;
                for (all_glyphs.items) |*glyph| {
                    if (!glyph.is_ltr) {
                        if (range_idx != 0 and glyph.cluster_len == 0) {
                            range_idx -= 1;
                        }
                    }
                    glyph.byte_idx = byte_index;
                    byte_index += glyph.cluster_len;
                    glyph.range_idx = range_idx;
                    if (glyph.is_ltr) {
                        range_idx += if (glyph.cluster_len == 0) 0 else 1;
                    } else {
                        range_idx += 1;
                    }
                }
            }

            new_font_glyphs.deinit();
        }

        var i: usize = 0;
        while (i < current_span_glyphs.items.len) : (i += 1) {
            const new_glyph = current_span_glyphs.items[i];
            var is_replaced = false;
            // Resolve clusters <-> glyphs
            if (span.range.contains(new_glyph.range_idx)) {
                var idx_opt: ?usize = null;
                var j = i;
                while (j < all_glyphs.items.len) : (j += 1) {
                    is_replaced = all_glyphs.items[j].isMissing() and !new_glyph.isMissing();
                    if (all_glyphs.items[j].byte_idx >= new_glyph.byte_idx) {
                        idx_opt = j;
                        break;
                    }
                    // if the missing char is not matched, remove the missing char
                    if (is_replaced) {
                        _ = all_glyphs.orderedRemove(j);
                        j -= 1;
                    }
                }
                if (idx_opt) |idx| {
                    const old_cluster_len = all_glyphs.items[idx].cluster_len;
                    const glyphs_exist = new_glyph.cluster_len != 0 and old_cluster_len != 0;
                    if (glyphs_exist) {
                        // Fewer glyphs for this font?
                        // when the old cluster len is greater than the new glyph cluster len, we need to remove the extra glyphs form old glyphs
                        if (old_cluster_len > new_glyph.cluster_len) {
                            while (all_glyphs.items[idx].byte_idx != new_glyph.byte_idx) {
                                _ = all_glyphs.orderedRemove(idx);
                            }
                        }

                        // More glyphs for this font?
                        else if (old_cluster_len < new_glyph.cluster_len) {
                            const next_idx = new_glyph.byte_idx + new_glyph.cluster_len;
                            var it = idx + 1;
                            while (all_glyphs.items[it].byte_idx < next_idx) {
                                _ = all_glyphs.orderedRemove(it);
                                it += 1;
                            }
                        }
                    }
                    if (old_cluster_len == 0 and new_glyph.cluster_len > 0) {
                        const k: usize = idx;
                        while (all_glyphs.items[k].cluster_len == 0) {
                            _ = all_glyphs.orderedRemove(k);
                        }
                        all_glyphs.items[idx] = new_glyph;
                    } else if (old_cluster_len > 0 and new_glyph.cluster_len == 0) {
                        try all_glyphs.insert(idx, new_glyph);
                    } else {
                        all_glyphs.items[idx] = new_glyph;
                    }
                }
            }
        }
        current_span_glyphs.deinit();
        whitespace_glyph.deinit();
        missing_char.deinit();
    }
    var clusters = ClusterList.init(allocator);
    if (has_found_glyphs) {
        var iter = ClusterIter{ .glyphs = all_glyphs.items };
        while (iter.next()) |item| {
            if (searchSpan(spans, item.range_idx)) |span| {
                // Skip if text is a newline character
                if (text[item.byte_idx] == '\n') continue;
                const start = item.start;
                var end = item.end;
                for (all_glyphs.items[start..], start..) |glyph, i| {
                    if (glyph.byte_idx == all_glyphs.items[start].byte_idx) {
                        end = @max(end, i);
                        break;
                    }
                }
                try clusters.append(try makeGlyphCluster(
                    allocator,
                    all_glyphs.items[start..end],
                    text,
                    span.style.font_size,
                ));
            }
        }
        all_glyphs.deinit();
    }
    return clusters;
}

fn buildVisualSpan(allocator: std.mem.Allocator, clusters: ClusterList, span: *style.FlatSpan, xform: math.Mat2Df) ?VisualSpan {
    var span_clusters = std.ArrayList(Cluster).init(allocator);
    defer {
        for (span_clusters.items) |*c| {
            c.glyphs.deinit();
        }
        span_clusters.deinit();
    }
    var bbox: ?math.Rect2f = null;
    var byte_offset: usize = 0;
    var total_width: f32 = 0;
    for (clusters.items) |*cluster| {
        if (cluster.visible) {
            if (span.range.contains(cluster.range_idx)) {
                var cluster_copy = cluster.*;
                cluster_copy.glyphs = cluster_copy.glyphs.clone() catch break;
                cluster_copy.path_xform = xform;
                span_clusters.append(cluster_copy) catch break;
                // Merge bbox of this cluster
                if (!cluster.localBBox().isEmpty()) {
                    // apply the line height to the cluster bbox
                    const cluster_height = cluster.height();
                    // if the line height is -1, use the cluster height
                    const height = if (span.style.line_height == -1.0) cluster_height else span.style.font_size * span.style.line_height;
                    const offset = @ceil((cluster_height - @round(height))) / 2.0;
                    std.log.warn("span offset: {d}", .{offset});

                    total_width += cluster.bbox().width();
                    const c_bbox = math.rect2f(
                        @floor(cluster.bbox().left()),
                        @floor(cluster.bbox().top() + offset),
                        @ceil(cluster.bbox().width()),
                        @round(height),
                    );
                    bbox = math.uniteBBox(bbox, c_bbox);
                }
            }
            byte_offset += (if (cluster.cluster_len == 0) 1 else cluster.cluster_len) - 1;
        }
    }
    if (span_clusters.items.len > 0) {
        var positioned_glyphs = std.ArrayList(PositionedGlyph).init(allocator);
        positioned_glyphs.ensureTotalCapacity(span_clusters.items.len) catch return null;
        for (span_clusters.items) |*cluster| {
            for (cluster.glyphs.items) |*g| {
                var pg = g.*;
                pg.cluster = cluster.*;
                positioned_glyphs.append(pg) catch break;
            }
        }
        if (positioned_glyphs.items.len > 0) {
            var new_bbox = math.rect2fse(0.0, 0.0, 0.0, 0.0);
            if (bbox) |b| {
                new_bbox = math.rect2f(
                    @floor(b.left()),
                    @floor(b.top()),
                    @ceil(total_width),
                    @round(b.height()),
                );
            }
            return VisualSpan{
                .range = span.range,
                .positioned_glyphs = positioned_glyphs,
                .bbox = new_bbox,
            };
        }
    }

    return null;
}

fn applyLetterSpacing(spans: []style.FlatSpan, clusters: *ClusterList) void {
    const num = clusters.items.len;
    for (0..num) |i| {
        var cluster = &clusters.items[i];
        // TODO: check if character belongs to script that support spacing.
        const support_letter_spacing = true;
        if (support_letter_spacing) {
            // for (spans) |*span| {
            //     if (span.range.contains(cluster.byte_idx)) {}
            // }
            if (searchSpan(spans, cluster.range_idx)) |span| {
                if (i < num - 1) {
                    cluster.adv_scale = (span.style.font_size * span.style.letter_spacing);
                }
            }
        }
    }
}

fn applyWordSpacing(spans: []style.FlatSpan, clusters: *ClusterList) void {
    const num = clusters.items.len;
    for (0..num) |i| {
        var cluster = &clusters.items[i];
        if (isWordSeparatorCharacter(cluster.codepoint)) {
            if (searchSpan(spans, cluster.byte_idx)) |span| {
                cluster.adv_scale += span.style.word_spacing;
            }
        }
    }
}

fn shapeTextForFont(allocator: std.mem.Allocator, text: []const u8, font: *FontDB.Font) !GlyphList {
    var glyphs = GlyphList.init(allocator);

    var runs = bidi.visualRuns(text);
    defer runs.deinit();
    for (runs.items) |run| {
        if (run.length == 0) continue;

        const sub_text = text[run.offset..(run.offset + run.length)];
        const ltr = ((run.level % 2) == 0);
        const offset = run.offset;

        // 1. setup buffer for harfbuzz shaping
        static.buf.clearContents();
        static.buf.reset();
        static.buf.addUTF8(sub_text, 0, @intCast(sub_text.len));
        static.buf.setDirection(if (ltr) .ltr else .rtl);
        static.buf.guessSegmentProps();

        // 2. create glyphs based on shaping result
        // TODO: features (small cap and kerning)
        font.hb_font.shape(static.buf, null);
        if (static.buf.getGlyphPositions()) |positions| {
            const infos = static.buf.getGlyphInfos();
            var range_idx: usize = 0;
            for (positions, 0..) |*pos, i| {
                const info = &infos[i];
                const start: usize = @intCast(info.cluster);
                const end: usize = blk: {
                    const last_opt = if (ltr) checkAdd(i, 1) else checkSub(i, 1);
                    if (last_opt) |last| {
                        if (last < infos.len) {
                            break :blk @intCast((infos[last].cluster));
                        }
                    }
                    break :blk sub_text.len;
                };
                const idx: usize = offset + start;
                const curr_cluster_len = if (end > start) (end - start) else 0;
                if (!ltr) {
                    if (range_idx != 0 and curr_cluster_len == 0) {
                        range_idx -= 1;
                    }
                }
                try glyphs.append(.{
                    .byte_idx = idx,
                    .cluster_len = curr_cluster_len,
                    .text = if (end == 0 and start == 0) "" else sub_text[start..end],
                    .id = @intCast(info.codepoint),
                    .dx = pos.x_offset,
                    .dy = pos.y_offset,
                    .width = pos.x_advance,
                    .font = font,
                    .codepoint = info.codepoint,
                    .range_idx = range_idx,
                    .is_ltr = ltr,
                });
                if (ltr) {
                    range_idx += if (curr_cluster_len == 0) 0 else 1;
                } else {
                    range_idx += 1;
                }
            }
        }
    }

    return glyphs;
}

inline fn checkAdd(a: usize, b: usize) ?usize {
    const la: u128 = @intCast(a);
    const lb: u128 = @intCast(b);
    const result = la + lb;
    return if (result > std.math.maxInt(usize)) null else @intCast(result);
}
inline fn checkSub(a: usize, b: usize) ?usize {
    return if (b > a) null else (a - b);
}
const ClusterIter = struct {
    const Item = struct {
        start: usize,
        end: usize,
        byte_idx: usize,
        range_idx: usize,
        cluster_len: usize,
    };
    glyphs: []Glyph,
    idx: usize = 0,
    pub fn next(self: *ClusterIter) ?Item {
        if (self.idx == self.glyphs.len) return null;
        const start = self.idx;
        const byte_idx = self.glyphs[self.idx].byte_idx;
        for (self.glyphs[self.idx..]) |g| {
            if (g.byte_idx != byte_idx) break;
            self.idx += 1;
        }
        return Item{
            .start = start,
            .end = self.idx,
            .byte_idx = byte_idx,
            .range_idx = self.glyphs[self.idx - 1].range_idx,
            .cluster_len = self.glyphs[self.idx - 1].cluster_len,
        };
    }
};

fn makeGlyphCluster(allocator: std.mem.Allocator, glyphs: []Glyph, text: []const u8, font_size: f32) !Cluster {
    _ = text;
    var width: f32 = 0.0;
    var dx: f32 = 0.0;
    var positioned_glyphs = std.ArrayList(PositionedGlyph).init(allocator);
    for (glyphs) |glyph| {
        const sx = glyph.font.scaleForSize(font_size);
        const xform = math.mat2df_translate(dx + @as(f32, @floatFromInt(glyph.dx)), @floatFromInt(glyph.dy));
        try positioned_glyphs.append(PositionedGlyph{
            .cluster = undefined,
            .glyph_xform = xform,
            .span_xform = math.mat2df_identity(),
            .units_per_em = glyph.font.units_per_em,
            .font_size = font_size,
            .font = glyph.font.id,
            .text = glyph.text,
            .id = glyph.id,
            .range_idx = glyph.range_idx,
            .byte_idx = glyph.byte_idx,
            .cluster_len = glyph.cluster_len,
            .font_family = glyph.font_family,
        });
        dx += @floatFromInt(glyph.width);
        const glyph_width = @as(f32, @floatFromInt(glyph.width)) * sx;
        width += glyph_width;
    }
    const byte_idx = glyphs[glyphs.len - 1].byte_idx;
    const font = glyphs[glyphs.len - 1].font;
    const ch = Cluster{
        .byte_idx = byte_idx,
        .codepoint = glyphs[glyphs.len - 1].codepoint,
        .width = width,
        .adv = width,
        .ascent = font.ascentForSize(font_size),
        .descent = font.descentForSize(font_size),
        .has_relative_shift = false,
        .xform = math.mat2df_identity(),
        .path_xform = math.mat2df_identity(),
        .glyphs = positioned_glyphs,
        .visible = true,
        .cluster_len = glyphs[glyphs.len - 1].cluster_len,
        .range_idx = glyphs[glyphs.len - 1].range_idx,
    };
    for (positioned_glyphs.items) |*g| {
        g.cluster = ch;
    }

    return ch;
}
fn searchSpan(spans: []style.FlatSpan, byte_offset: usize) ?*style.FlatSpan {
    for (spans) |*span| {
        if (span.range.contains(byte_offset)) {
            return span;
        }
    }
    return null;
}
/// Checks that the selected character is a word separator.
///
/// According to: https://www.w3.org/TR/css-text-3/#word-separator
pub fn isWordSeparatorCharacter(codepoint: u32) bool {
    return switch (codepoint) {
        0x0020,
        0x00A0,
        0x1361,
        0x010100,
        0x010101,
        0x01039F,
        0x01091F,
        => true,
        else => false,
    };
}
test "isWordSeparatorCharacter tests" {
    try std.testing.expect(isWordSeparatorCharacter(0x0020));
    try std.testing.expect(!isWordSeparatorCharacter(0x0041)); // 'A' should not be a separator
}

fn measureClustersWidth(clusters: *std.ArrayList(Cluster)) f32 {
    var w: f32 = 0.0;
    for (clusters.items) |*cluster| {
        w += cluster.advance();
    }
    return w;
}

test "layoutText" {
    const allocator = std.testing.allocator;

    var font_db = FontDB.init(allocator);
    defer font_db.deinit();

    const font_id = try font_db.loadFromMemory(.{
        .family = "Sans",
    }, @embedFile("../OpenSans-subset.ttf"));

    try init(allocator, font_db.getPtrByID(font_id).?);
    defer deinit();

    // Prepare text and spans
    const text = "Hello World";
    // We'll have one style span covering the entire text, and another covering "World"
    var spans = std.ArrayList(style.Span).init(allocator);
    defer spans.deinit();

    // A base style: (using CascadeStyle defaults)
    // font_size override on entire text
    try spans.append(.{
        .range = .{ .location = 0, .length = 11 }, // covers "Hello World"
        .style = .{ .font_size = 40.0 },
    });
    // Another span: letter_spacing for "World"
    try spans.append(.{
        .range = .{ .location = 6, .length = 5 }, // "World" starts at index 6
        .style = .{ .letter_spacing = 0.1 },
    });

    const base_style = style.CascadeStyle.Flattened{
        .font = .{
            .family = "Sans",
        },
        .font_size = 32.0,
        .letter_spacing = 0.0,
        .word_spacing = 0.0,
    };

    // Flatten these spans
    const flat_spans = try style.flattenSpans(allocator, spans.items, base_style);
    defer allocator.free(flat_spans);

    // Now call layoutText
    const layout = try layoutText(allocator, &font_db, text, flat_spans, -1.0);
    defer layout.deinit();

    // Basic checks
    try std.testing.expect(layout.visual_spans.items.len > 0);
    // We expect at least one VisualSpan corresponding to the flattened segments.
    // For our test input ("Hello World" with two style influences), we might have multiple segments.

    const first_span = layout.visual_spans.items[0];
    // Check that the span covers some subset of the text.
    try std.testing.expect(first_span.range.location == 0);
    // The end depends on how flattening split the spans.
    // But we can at least check that span_xform, positioned_glyphs are non-empty.
    try std.testing.expect(first_span.positioned_glyphs.items.len > 0);

    // Check one of the glyphs:
    const first_glyph = first_span.positioned_glyphs.items[0];
    // We can't know exact glyph metrics without a fully integrated environment,
    // but we can expect that it's not the missing glyph if the font and text are correct.
    try std.testing.expect(first_glyph.id != 0);
}
