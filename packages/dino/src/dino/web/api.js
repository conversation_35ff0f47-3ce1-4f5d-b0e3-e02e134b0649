mergeInto(LibraryManager.library, {
    onReady: () => {
        window.__ph__.onReady()
    },
    onFrameBegin: () => {
        window.__ph__.onFrameBegin()
    },
    onOverlay: () => {
        window.__ph__.onOverlay()
    },
    onUpdateSceneTree: () => {
        window.__ph__.onUpdateSceneTree()
    },
    onNodeUpdate: (node_id, flag) => {
        window.__ph__.onNodeUpdate(node_id, flag)
    },
    onSelectionUpdate: (node_id, start_block, start_char, end_block, end_char, cursor, active_block_index) => {
        window.__ph__.onSelectionUpdate(node_id, start_block, start_char, end_block, end_char, cursor, active_block_index)
    },
    onTextUpdate: (text_ptr, text_len, isComposing, start, end, cursor) => {
        window.__ph__.onTextUpdate(text_ptr, text_len, isComposing, start, end, cursor)
    },
    onExitTextEditingMode: () => {
        window.__ph__.onExitTextEditingMode()
    },
    onCopyText: (text, len) => {
        window.__ph__.onCopyText(text, len)
    },
    onPasteText: () => {
        window.__ph__.onPasteText()
    },
    onUndoComb: () => {
        window.__ph__.onUndoComb()
    },
    onRedoComb: () => {
        window.__ph__.onRedoComb()
    },
    onUpdatePageOffset: (node_id) => {
        window.__ph__.onUpdatePageOffset(node_id)
    },
    onUpdateElementSize: (node_id, width, height) => {
        window.__ph__.onUpdateElementSize(node_id, width, height)
    },
    onGetTextPathId: (node_id) => {
        return window.__ph__.onGetTextPathId(node_id)
    },
    onPrepareCamera: () => window.__ph__.onPrepareCamera(),
    onFrameEnd: () => {
        window.__ph__.onFrameEnd()
    },
    onCapture: (ptr, len) => {
        window.__ph__.onCapture(ptr, len)
    },
    onFontRequest: (font_family_ptr, font_family_len, font_style, font_weight) => {
        window.__ph__.onFontRequest(font_family_ptr, font_family_len, font_style, font_weight)
    },
    onZoom: (zoom) => {
        window.__ph__.onZoom(zoom)
    },
    onZoomToFit: () => {
        window.__ph__.onZoomToFit()
    },
    onZoomToSelection: () => {
        window.__ph__.onZoomToSelection()
    },
    onZoomOut: () => {
        window.__ph__.onZoomOut()
    },
    onZoomIn: () => {
        window.__ph__.onZoomIn()
    },
    onESC: () => {
        window.__ph__.onESC()
    },
    onTab: () => {
        window.__ph__.onTab()
    },
    onShiftTab: () => {
        window.__ph__.onShiftTab()
    },
});
