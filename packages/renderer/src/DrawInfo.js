import { AABB, MIN_SIZE_THRESHOLD } from '@phase-software/data-utils'
import { PointShape, TextResizeMode } from '@phase-software/types'
import { constructHalfEdgePathsFromEdgesInPlace, pushContour } from './geometry/generate'
import { dino } from './dino'
import { Vector2, Transform2D, Rect2 } from './math'
import { centerSelection } from './actions'
import {
    CENTER_LINEAR,
    CENTER,
    BOTTOM,
    LEFT
} from './actions/gradient'
import { Command, PathData } from './geometry/PathData'
import { getSourceBases } from './visual_server/SpatialCache'
import { BezierShape, BooleanAction } from './geometry/bezier-shape/BezierShape'
import { stroke2 } from './geometry/bezier-shape/stroke2'
import { fixInfiniteSkew } from './visual_server/Transform'
import { MIN_SIZE } from './constants'
import { updateMotionPointMirror } from './panes/MotionPathHelper'
import { <PERSON><PERSON><PERSON><PERSON>ez } from './geometry/bezier-shape/CubicBez'
import { updateDinoTextPath, updateTextFromBBox } from './utils/textUtils'
import { UpdateType } from './visual_server/RenderItem'
import { TEXT_RESIZE_MODE_MAP } from './index'

/** @typedef {import('./visual_server/VisualServer').VisualServer} VisualServer */
/** @typedef {import('./visual_server/Transform').Transform} Transform */
/** @typedef {import('./visual_server/SpatialCache').SceneNode} SceneNode */
/** @typedef {import('./visual_server/RenderItem').RenderItem} RenderItem */
/** @typedef {import('@phase-software/data-utils').Mesh} Mesh */
/** @typedef {import('./geometry/generate').GetVertexPositionCallback} GetVertexPositionCallback */
/** @typedef {import('./visual_server/SpatialCache').SceneNode} SceneNode */

const api = dino()

/**
 * @todo This class aims to implement Inversion of Control but is missing an interface to establish the required contract.
 */
export class DrawInfo {

    /**
     * Creates a new clipboard
     * @param {VisualServer} visualServer
     */
    constructor(visualServer) {
        this.vs = visualServer
        this.pathCommands = Command
    }

    inject() {
        this.vs.dataStore.injectDrawInfo(this)
    }

    /**
     * Gets reference to canvas element
     * @returns {HTMLCanvasElement}
     */
    getCanvas() {
        return this.vs.canvas
    }

    /**
     * Convert mouse position to world position
     * @param {Vector2} mousePos
     * @returns {Vector2} output position
     */
    convertMousePosToWorldPosition(mousePos) {
        return this.vs.viewport.toWorld(mousePos)
    }

    toBaseWorldPosition(elementId, pos, out = new Vector2()) {
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            throw new Error(`Invalid element ID provided ${elementId}`)
        }
        if (this.vs.dataStore.isWorkspace(elementId)) {
            out.x = pos.x
            out.y = pos.y
            return out
        }
        const world = node.baseTransform.world

        /** @type {Vector2} */
        const worldPos = this._applyMatrix(pos, world)
        if (out instanceof Vector2) {
            out.x = worldPos.x
            out.y = worldPos.y
        } else {
            out[0] = worldPos.x
            out[1] = worldPos.y
        }
        return out
    }

    /**
     * Converts object space position into world space position
     * @param {string} elementId  element in which space input position is defined.
     *                             for mesh point positions, it's the element itself
     *                             for element position, it is that element's parent
     * @param {Vector2} pos input position
     * @param {Vector2} [out] output position
     * @returns {[number, number]}  output position (in world space)
     */
    toWorldPosition(elementId, pos, out = new Vector2()) {
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            throw new Error(`Invalid element ID provided ${elementId}`)
        }
        if (this.vs.dataStore.isWorkspace(elementId)) {
            out.x = pos.x
            out.y = pos.y
            return out
        }
        const world = node.transform.world

        /** @type {Vector2} */
        const worldPos = this._applyMatrix(pos, world)
        if (out instanceof Vector2) {
            out.x = worldPos.x
            out.y = worldPos.y
        } else {
            out[0] = worldPos.x
            out[1] = worldPos.y
        }
        return out
    }

    vertex2World(elementId, pos, out = new Vector2()) {
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            throw new Error(`Invalid element ID provided ${elementId}`)
        }

        const pathXform = this._getPathTransform(node)
        /** @type {Vector2} */
        const worldPos = this._applyMatrix(pos, pathXform)
        if (out instanceof Vector2) {
            out.x = worldPos.x
            out.y = worldPos.y
        } else {
            out[0] = worldPos.x
            out[1] = worldPos.y
        }
        return out
    }

    getVerticesBoundWorld(element) {
        const verticesBounds = new AABB()
        const node = this.vs.getRenderItemOfElement(element)
        const pathXform = this._getPathTransform(node)
        const mesh = element.get('geometry').get('mesh')

        const vertexPos = new Vector2()
        const worldVertexPos = new Vector2()
        for (const vertex of mesh.getVertices()) {
            vertexPos.x = vertex.pos[0]
            vertexPos.y = vertex.pos[1]
            pathXform.xform(vertexPos, worldVertexPos)
            verticesBounds.minMax([worldVertexPos.x, worldVertexPos.y])
        }

        return verticesBounds
    }

    world2Vertex(elementId, pos, out = new Vector2()) {
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            throw new Error(`Invalid element ID provided ${elementId}`)
        }

        const pathXform = this._getPathTransform(node)
        pathXform.affine_inverse()
        /** @type {Vector2} */
        const worldPos = this._applyMatrix(pos, pathXform)
        if (out instanceof Vector2) {
            out.x = worldPos.x
            out.y = worldPos.y
        } else {
            out[0] = worldPos.x
            out[1] = worldPos.y
        }
        return out
    }

    /**
     *
     * @param {RenderItem} node
     * @returns {Transform2D}
     */
    _getPathTransform(node) {
        const { translate, rotation, scale, skew, contentAnchor } = node.transform

        const pathXform = node.transform.parent.clone()
            .translate_right(translate.x, translate.y)
            .rotate_right(rotation)
            .skew_right(fixInfiniteSkew(skew.x), fixInfiniteSkew(skew.y))
            .scale_right(scale.x, scale.y)
            .translate_right(-contentAnchor.x, -contentAnchor.y)
        return pathXform
    }

    /**
     * Converts world space position into object space position
     * @param {string} elementId  element to which space input position needs to be converted.
     *                             for mesh point positions, it's the element itself
     *                             for element position, it is that element's parent
     * @param {[number, number]} pos input position
     * @param {[number, number]} out output position
     * @param {Vector2} [offset]
     * @returns {[number, number]}  output position (in world space)
     */
    toObjectPosition(elementId, pos, out = new Vector2(), offset) {
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            throw new Error(`Invalid element ID provided ${elementId}`)
        }
        const worldInv = node.transform.worldInv

        /** @type {Vector2} */
        const objPos = this._applyMatrix(pos, worldInv, undefined, offset)
        out.x = objPos.x
        out.y = objPos.y
        return out
    }

    /**
     * Returns bounds of the viewport
     * @param {AABB} vpBounds   if provided, will set viewport bounds into this AABB and return it
     * @returns {AABB}  vpBounds if provided, otherwise new instance of AABB with viewport bounds
     */
    getViewportBounds(vpBounds = new AABB()) {
        vpBounds.copy(this.vs.viewport.rectW)
        return vpBounds
    }

    /**
     * Returns current selection bounds
     * @param {AABB} bounds   if provided, will set selection bounds into this AABB and return it
     * @param {boolean} onlyVisible if ture, the bound only includes visible elements
     * @returns {AABB}        if provided, otherwise new instance of AABB with selection bounds
     */
    getSelectionBoundsWorld(bounds = new AABB(), onlyVisible = false) {
        if (!onlyVisible) {
            return bounds.copy(this.vs.selection.bounds)
        }

        const rect = new Rect2()
        for (const { node } of this.vs.selection.iter()) {
            if (node.item.visible) {
                if (rect.is_zero()) {
                    rect.copy(node.boundsWorldAABB)
                } else {
                    rect.merge_with(node.boundsWorldAABB)
                }
            }
        }
        return bounds.copy(rect)
    }

    /**
     * Generate current boolean result bounds of the given elements
     * Notice: this function will utlize boolean algorithm which might be a heavy calculation,
     * should be careful to use it.
     * @param {BooleanOperation} booleanType
     * @param {Element} parent
     * @param {[Element]} elements
     * @returns {AABB}
     */
    getBooleanlocalBounds(booleanType, parent, elements) {
        const bounds = new AABB()

        const bases = []
        for (let i = 0; i < elements.length; i++) {
            const node = this.vs.indexer.getNode(elements[i].get('id'))
            getSourceBases(node, bases)
        }

        // calculate the boolean base with all bases
        /** @type {BezierShape} */
        let base = null
        for (let i = 0; i < bases.length; i++) {
            base = base ? base[BooleanAction[booleanType]](bases[i]) : bases[i]
        }

        // if no base, create a point at the top left of the selection bounds
        if (!base || base.isEmpty()) {
            const selectionBounds = this.getSelectionBoundsWorld(bounds)
            const point = selectionBounds.topLeft
            const pathData = new PathData()
            pathData.commands = [1]
            pathData.vertices = [point.x, point.y]
            base = BezierShape.createFromPathData(pathData)
        }

        const bbox = base.getBounds()
        return bounds.copy(bbox)
    }

    /**
     * Generate current bounds of the element
     * @param {Element} element
     * @param {AABB} bounds
     * @param {boolean} including_stroke
     * @returns {AABB}
     */
    getElementBounds(element, bounds = new AABB(), including_stroke = false) {
        const node = this.vs.indexer.getNode(element.get('id'))

        // get base area bounds
        let shape = node.item.base.getFinalShape()
        shape = shape.clone().transform(node.item.transform.local)
        const bbox = shape.getBounds().clone()

        if (including_stroke) {
            // merge with all stroke area bounds
            for (let i = 0; i < node.item.strokeLayers.length; i++) {
                const style = node.item.strokes[i].style
                const shape = node.item.base.getFinalShape()
                let strokeShape = stroke2(shape, style)
                strokeShape = strokeShape.clone().transform(node.item.transform.local)
                bbox.merge_with(strokeShape.getBounds())
            }
        }

        return bounds.copy(bbox)
    }

    /**
     * @typedef {object} GradientHandles
     * @property {Vector2} center
     * @property {Vector2} bottom
     * @property {Vector2} left
     */
    /**
     * getGradientHandlesPosition
     * @param {number} width
     * @param {number} height
     * @param {boolean} isLinear
     * @param {number[]} gradientTransform
     * @returns {GradientHandles}
     */
    getGradientHandlesPosition(width, height, isLinear, gradientTransform) {
        const IT = new Transform2D()
        IT.set(...gradientTransform).affine_inverse().scale(width, height)
        return {
            center: IT.xform((isLinear ? CENTER_LINEAR : CENTER)),
            bottom: IT.xform(BOTTOM),
            left: IT.xform(LEFT)
        }
    }

    centerSelection() {
        centerSelection()
    }

    _applyMatrix(pos, xform, out = new Vector2(), offset = undefined) {
        if (pos instanceof Vector2) {
            out.set(pos.x, pos.y)
        } else {
            out.set(pos[0], pos[1])
        }
        if (offset) out.add(offset)
        xform.xform(out, out)
        return out
    }

    getMeshOutlinesForExport(mesh, basePosition) {
        /** @type {Set<HalfEdge[]>} */
        const pathSet = new Set()
        constructHalfEdgePathsFromEdgesInPlace(mesh, pathSet)
        const res = new PathData()
        /** @type {GetVertexPositionCallback} */
        const getVertexPosition = (mesh, id) => {
            const pos = mesh.cellTable.get(id).pos
            return [pos[0] - basePosition[0], pos[1] - basePosition[1]]
        }
        for (const path of pathSet) {
            pushContour(mesh, path, res, getVertexPosition, true)
        }
        return res
    }

    /**
     * Get position fixed by other properties changes, always call this function before apply changes, the change should only contain one type of property
     * @param {string} elementId
     * @param {object} changes
     * @param {Vector2} specifiedRefPoint
     * @returns {object|number|null} translate | translate.x | translate.y | null
     */
    getFixedPositionByChanges(elementId, changes, specifiedRefPoint) {
        /** @type {RenderItem} */
        const node = this.vs.getRenderItem(elementId)
        if (!node) {
            return null
        }
        /** @type {Transform} */
        const transform = node.transform
        // In the following cases, we don't need to fix position
        if (changes.contentAnchorX === undefined &&
            changes.contentAnchorY === undefined &&
            changes.width === undefined &&
            changes.height === undefined) {
            return null
        }

        let affinedLocalPos
        let referencePointX
        let referencePointY
        const contentAnchor = transform.contentAnchor
        if (specifiedRefPoint === undefined) {
            affinedLocalPos = transform.local.get_origin()
            const referencePoint = transform.referencePoint
            referencePointX = referencePoint.x
            referencePointY = referencePoint.y
        } else {
            referencePointX = specifiedRefPoint.x
            referencePointY = specifiedRefPoint.y
            const { translate, rotation, scale, skew } = transform
            const dummyTF = new Transform2D()
            dummyTF
                .translate_right(translate.x, translate.y)
                .rotate_right(rotation)
                .skew_right(fixInfiniteSkew(skew.x), fixInfiniteSkew(skew.y))
                .scale_right(scale.x, scale.y)
                .translate_right(-contentAnchor.x, -contentAnchor.y)
                .translate_right(-referencePointX, -referencePointY)
            affinedLocalPos = dummyTF.get_origin()
        }

        let contentAnchorX = contentAnchor.x
        let contentAnchorY = contentAnchor.y
        const currWidth = transform.size.x
        const currHeight = transform.size.y
        const translateX = transform.translate.x
        const translateY = transform.translate.y
        // Only contentAnchor
        if (changes.contentAnchorX !== undefined) {
            contentAnchorX = changes.contentAnchorX
        }
        if (changes.contentAnchorY !== undefined) {
            contentAnchorY = changes.contentAnchorY
        }

        // 0 size offset
        let offsetX = 0
        let offsetY = 0
        if (changes.width !== undefined) {
            const isNewWidthZero = changes.width !== 0 && changes.width < MIN_SIZE_THRESHOLD
            const isCurWidthZero = currWidth !== 0 && currWidth < MIN_SIZE_THRESHOLD
            if (isNewWidthZero && !isCurWidthZero) {
                offsetX = - changes.width * 0.5
            } else if (isCurWidthZero && !isNewWidthZero) {
                offsetX = currWidth * 0.5
            }
        }

        if (changes.height !== undefined) {
            const isNewHeightZero = changes.height !== 0 && changes.height < MIN_SIZE_THRESHOLD
            const isCurHeightZero = currHeight !== 0 && currHeight < MIN_SIZE_THRESHOLD
            if (isNewHeightZero && !isCurHeightZero) {
                offsetY = - changes.height * 0.5
            } else if (isCurHeightZero && !isNewHeightZero) {
                offsetY = currHeight * 0.5
            }
        }


        if (changes.referencePointX !== undefined) {
            referencePointX = changes.referencePointX
        }

        if (changes.referencePointY !== undefined) {
            referencePointY = changes.referencePointY
        }

        const newPivotOffset = new Vector2(
            contentAnchorX + referencePointX,
            contentAnchorY + referencePointY
        )
        const newTranslate = new Vector2(translateX, translateY)
        const newSkew = transform.skew
        const newScale = transform.scale
        const newRotation = transform.rotation

        const newLocal = new Transform2D()
            .translate_right(newTranslate.x, newTranslate.y)
            .rotate_right(newRotation)
            .skew_right(fixInfiniteSkew(newSkew.x), fixInfiniteSkew(newSkew.y))
            .scale_right(newScale.x, newScale.y)
            .translate_right(-newPivotOffset.x, -newPivotOffset.y)

        const newAffinedLocalPos = newLocal.get_origin()
        const x = offsetX + newTranslate.x + (affinedLocalPos.x - newAffinedLocalPos.x)
        const y = offsetY + newTranslate.y + (affinedLocalPos.y - newAffinedLocalPos.y)
        return new Vector2(x, y)
    }

    /**
     * Get the properties of the children after resize
     * @param {Element} element
     * @param {number} originalWidth
     * @param {number} originalHeight
     * @param {number} newWidth
     * @param {number} newHeight
     * @returns {Map<string, object>}
     */
    getChildrenPropetiesAfterResize(element, originalWidth, originalHeight, newWidth, newHeight) {
        const result = new Map()
        /** @type {RenderItem} */
        const parentItem = this.vs.getRenderItem(element.get('id'))
        if (!parentItem) {
            return result
        }

        // Get selection scale martix
        let ow = originalWidth, oh = originalHeight
        if (originalWidth === 0) {
            ow = MIN_SIZE
        }
        if (originalHeight === 0) {
            oh = MIN_SIZE
        }
        const v_scale = new Vector2(newWidth / ow, newHeight / oh)

        for (const child of element.children) {
            const childId = child.get('id')
            const childItem = this.vs.getRenderItem(childId)
            const childTransform = childItem.transform
            if (!childItem) continue


            // Get new transform by selection scale
            const t_new = new Transform2D()
                .scale_right(v_scale.x, v_scale.y)
                .rotate_right(childTransform.rotation)
                .skew_right(childTransform.skew.x, childTransform.skew.y)

            // Get ne w rotation, size and skew
            const { rotation: n_rotation, scale: n_scale, skew: n_skew } = t_new.decompose()

            const newSize = new Vector2().copy(childTransform.size).multiply(n_scale)
            const newTrasnalte = new Vector2().copy(childTransform.translate).multiply(v_scale)
            const newContentAnchor = new Vector2().copy(childTransform.contentAnchor).multiply(n_scale)
            const refPt = child.get('referencePoint')
            const newRefPt = new Vector2(refPt[0], refPt[1]).multiply(n_scale)

            if (child.isText) {
                const node = this.vs.indexer.getNode(childId)
                if (node.item.resizingMode === TEXT_RESIZE_MODE_MAP[TextResizeMode.AUTO_WIDTH]) {
                    if (Math.abs(newSize.x - childTransform.size.x) > 0.01) {
                        child.set('resizingMode', TextResizeMode.AUTO_HEIGHT)
                    }
                    if (Math.abs(newSize.y - childTransform.size.y) > 0.01) {
                        child.set('resizingMode', TextResizeMode.FIXED)
                    }
                } else if (node.item.resizingMode === TEXT_RESIZE_MODE_MAP[TextResizeMode.AUTO_HEIGHT]) {
                    if (Math.abs(newSize.y - childTransform.size.y) > 0.01) {
                        child.set('resizingMode', TextResizeMode.FIXED)
                    }
                }
            }

            const changes = {
                skew: new Vector2(n_skew.x, n_skew.y),
                rotation: n_rotation
            }

            if (childTransform.scale.x !== 0) {
                changes.width = newSize.x
                changes.translateX = newTrasnalte.x
                changes.contentAnchorX = newContentAnchor.x
                changes.referencePointX = newRefPt.x
            }

            if (childTransform.scale.y !== 0) {
                changes.height = newSize.y
                changes.translateY = newTrasnalte.y
                changes.contentAnchorY = newContentAnchor.y
                changes.referencePointY = newRefPt.y
            }
            result.set(childId, changes)
        }
        return result
    }

    /**
     * Get the properties of the children after width resize
     * @param {Element} element
     * @param {number} originalWidth
     * @param {number} newWidth
     * @returns {Map<string, object>}
     */
    getChildrenPropertiesAfterWidthResize(element, originalWidth, newWidth) {
        const result = new Map()
        /** @type {RenderItem} */
        const parentItem = this.vs.getRenderItem(element.get('id'))
        if (!parentItem) {
            return result
        }

        // Get selection scale matrix for width only
        let ow = originalWidth
        if (originalWidth === 0) {
            ow = MIN_SIZE
        }
        const widthScale = newWidth / ow

        for (const child of element.children) {
            const childId = child.get('id')
            const childItem = this.vs.getRenderItem(childId)
            const childTransform = childItem.transform
            if (!childItem) continue

            // Get new transform by width scale only
            const t_new = new Transform2D()
                .scale_right(widthScale, 1) // Only scale width, keep height scale as 1
                .rotate_right(childTransform.rotation)
                .skew_right(childTransform.skew.x, childTransform.skew.y)

            // Get new rotation, size and skew
            const { rotation: n_rotation, scale: n_scale, skew: n_skew } = t_new.decompose()

            const newSize = new Vector2().copy(childTransform.size)
            newSize.x *= n_scale.x // Only modify width
            const newTranslate = new Vector2().copy(childTransform.translate)
            newTranslate.x *= widthScale // Only modify x translation
            const newContentAnchor = new Vector2().copy(childTransform.contentAnchor)
            newContentAnchor.x *= n_scale.x // Only modify x anchor
            const refPt = child.get('referencePoint')
            const newRefPt = new Vector2(refPt[0], refPt[1])
            newRefPt.x *= n_scale.x // Only modify x reference point

            if (child.isText) {
                const node = this.vs.indexer.getNode(childId)
                if (node.item.resizingMode === TEXT_RESIZE_MODE_MAP[TextResizeMode.AUTO_WIDTH]) {
                    if (Math.abs(newSize.x - childTransform.size.x) > 0.01) {
                        child.set('resizingMode', TextResizeMode.AUTO_HEIGHT)
                    }
                }
            }

            const changes = {
                skew: new Vector2(n_skew.x, n_skew.y),
                rotation: n_rotation
            }

            if (childTransform.scale.x !== 0) {
                changes.width = newSize.x
                changes.translateX = newTranslate.x
                changes.contentAnchorX = newContentAnchor.x
                changes.referencePointX = newRefPt.x
            }

            result.set(childId, changes)
        }
        return result
    }

    /**
     * Update render data from dirty nodes at the moment
     */
    updateNodes() {
        const updateList = [...this.vs.updateList.values()]
        this.vs.indexer.updateNodes(updateList)
        this.vs.updateList.clear()
    }

    /**
     * Check whether the element is present in SpacialCache.nodeMap
     * @param {string} elementId
     * @returns {boolean}
     */
    isNodePresent(elementId) {
        return Boolean(this.vs.indexer.getNode(elementId))
    }

    /**
     * Trigger updateNodes first then get the latest bounds of the element
     * @param {string} elementId
     * @returns {Rect2}
     */
    getLatestLocalBounds(elementId) {
        const node = this.vs.indexer.getNode(elementId)
        return node.boundsLocal
    }

    updateMotionPathPointShape(pointShapeType, options) {
        updateMotionPointMirror(pointShapeType, options)
    }

    /**
     * @param {PointShape} point
     * @param {string} type
     */
    updateMotionPointCurveControl(point, type) {
        // if moving the curveControl, update the oppositeCurveControl
        const oppositeType = type === 'in' ? 'out' : 'in'
        const oppositeCurveControl = new Vector2().fromArray(point[oppositeType])
        const mirrorDir = new Vector2(-point[type][0], -point[type][1])
        switch (point.mirror) {
            case PointShape.ANGLE: {
                const norm = oppositeCurveControl.length()
                mirrorDir.normalize().scale(norm)
                point[oppositeType] = [mirrorDir.x, mirrorDir.y]
                break
            }
            case PointShape.ANGLE_AND_LENGTH: {
                point[oppositeType] = [mirrorDir.x, mirrorDir.y]
                break
            }
            case PointShape.NONE:
            case PointShape.INDEPENDENT:
                break
        }
    }

    /**
     * @param {CubicBez} curve
     * @param {number} percent
     * @returns {CubicBez}
     */
    subdivideMotionPath(curve, percent) {
        const bez = new CubicBez().initWithPointsAndHandlesN(...curve)
        const len = bez.getLength()
        const offset = len * percent
        return CubicBez.subdivide(bez.getValues(), bez.getTimeAt(offset))
    }

    /**
     * Update render data from dirty nodes at the moment
     * @param {string} id
     * @returns {PathData}
     */
    getTextPathDataWithPathID(id) {
        const dinoTree = this.vs.view.getOrCreateDinoTree(id)
        const path_id = dinoTree.path_id
        const commands = api.getPathCmd(path_id)
        const vertices = api.getPathVtx(path_id)
        return new PathData(vertices, commands)
    }

    /**
     * Generates a path representation of text with specified styling options
     *
     * This method creates a path that represents text with the given font size, line height,
     * and letter spacing. If any of these parameters are not provided, it falls back to
     * the element's default values.
     *
     * @param {string} elementId - The ID of the text element
     * @param {number} [fontSize] - Font size to use (defaults to element's fontSize if not provided)
     * @param {number} [lineHeight] - Line height to use (defaults to element's lineHeight if not provided)
     * @param {number} [letterSpacing] - Letter spacing to use (defaults to element's letterSpacing if not provided)
     * @returns {PathData} A PathData object containing the commands and vertices for the text path
     */
    getTextPathWithStyle(elementId, fontSize, lineHeight, letterSpacing) {
        const item = this.vs.indexer.nodeMap.get(elementId).item
        const dinoTree = this.vs.view.getOrCreateDinoTree(elementId)
        const textNodeId = dinoTree.text_node_id
        const text_doc_id = dinoTree.text_doc_id
        const textOption = {
            font_family: item.fontFamily,
            font_style: item.fontStyle,
            font_weight: item.fontWeight,
            font_size: fontSize || item.fontSize, // Font size, minimum value is 1
            letter_spacing: letterSpacing ?? item.letterSpacing, // Value can be 0
            line_spacing: (lineHeight ?? item.lineHeight), // Value can be 0
            paragraph_spacing: item.paragraphSpacing || 0,
            text_horizontal_alignment: item.horizontalAlignment,
            text_vertical_alignment: item.verticalAlignment,
        }
        const { commands, vertices } = dino().getTextPathWithStyle(textNodeId, text_doc_id, textOption)
        const pathData = new PathData(vertices, commands)

        return pathData
    }

    /**
     * @param {SceneNode} node
     * @param {PathData} pathData
     */
    handleTextPaintsLayer(node, pathData) {
        const dinoTree = this.vs.view.getOrCreateDinoTree(node.id)
        api.setPath(dinoTree.path_id, pathData)

        node.setGeometry(pathData)
        for (let i = 0; i < dinoTree.fills.children.length; i++) {
            // TODO: only add first 4 fills and strokes
            const fill = dinoTree.fills.children[i]
            api.destroyNode(fill.id)
            fill.id = api.makeNode(api.PATH)
            api.setNodePath(fill.id, dinoTree.path_id)
            node.item.visualServer.view.setParent(fill.id, node.item.id)
            api.addNodeChild(dinoTree.fills.id, fill.id)
            api.setNodeFillPaint(fill.id, fill.fill_tag, fill.fill_id)
            api.markNodeChanged(fill.id)
        }
        for (let i = 0; i < dinoTree.strokes.children.length; i++) {
            const stroke = dinoTree.strokes.children[i]
            api.destroyNode(stroke.id)
            stroke.id = api.makeNode(api.PATH)
            api.setNodePath(stroke.id, dinoTree.path_id)
            node.item.visualServer.view.setParent(stroke.id, node.item.id)
            api.addNodeChild(dinoTree.strokes.id, stroke.id)
            api.setNodeStrokeData(stroke.id, stroke.stroke_data_id)
            api.setNodeStrokePaint(stroke.id, stroke.stroke_tag, stroke.stroke_id)
            api.markNodeChanged(stroke.id)
        }
    }

    /**
     * @param {PathData} pathData
     * @returns {Rect2}
     */
    getTextPathOffset(pathData) {
        const shape = BezierShape.createFromPathData(pathData)
        return shape.getBounds()
    }

    /**
     * @param {PathData} pathData
     * @param {number} x
     * @param {number} y
     * @returns {PathData}
     */
    translatePathData(pathData, x, y) {
        const T = new Transform2D().translate(x, y)
        return pathData.applyXorm(T)
    }

    setTextEditingMode(nodeId) {
        const dinoTree = this.vs.view.getOrCreateDinoTree(nodeId)
        dino().enterTextEditingMode(dinoTree.text_node_id)

        // Issue: currently sokol forcibly focus the canvas input every tick which often stops
        // frontend property inputs to be focused.
        // We decided to remove that tick in the next commit,
        // but the canvas input still needs at least to be focused once to receive keyboard events,
        // so here's a workaround.
        //
        // Todo: If later dino.enterTextEditingMode could focus the canvas input itself,
        // then remove this workaround
        document.getElementById('input').focus()
    }

    changeTextEditingNode(nodeId, tabbing) {
        const dinoTree = this.vs.view.getOrCreateDinoTree(nodeId)
        dino().changeTextEditingNode(dinoTree.text_node_id, tabbing)
    }

    exitTextEditingMode() {
        dino().exitTextEditingMode()

        // Issue: currently dino/sokol does not handle the blur as well,
        // so here's a workaround.
        //
        // Todo: If later dino/sokol could just handle focus and blur itself,
        // then remove this workaround
        document.getElementById('input').blur()
        // The above might not work, so here's a workaround.
        setTimeout(() => {
            if (document.activeElement.id === 'input') {
                document.getElementById('input').blur()
            }
        }, 100)
    }

    setForceDefaultCursor(force) {
        dino().setForceDefaultCursor(force)
    }
    getUpdatedTextPathBBox(elementId) {
        const dinoTree = this.vs.view.getOrCreateDinoTree(elementId)
        const node = this.vs.indexer.getNode(elementId)
        updateDinoTextPath(this.vs, node.item)
        return dino().getTextPathBBox(dinoTree.text_doc_id)
    }

    /**
     * Updates text element properties based on text content and style
     * Used for both interpolatable and non-interpolatable changes in design mode
     *
     * @param {Element} element - The text element to update
     * @param {object} options - Additional options for the update
     * @returns {void}
     */
    updateTextProperties(element, options) {
        const elementId = element.get('id')
        const resizingMode = element.get('resizingMode')

        // Skip updates for fixed-size text elements
        if (resizingMode === TextResizeMode.FIXED) {
            return
        }

        // Verify element exists in the scene
        if (!this.vs.indexer.hasNode(elementId)) {
            return
        }

        // Get the Dino tree for this text element
        const dinoTree = this.vs.view.getOrCreateDinoTree(elementId)

        // Update text properties if text document exists
        if (dinoTree.text_doc_id) {
            // Get updated bounding box after applying text changes
            const bbox = this.getUpdatedTextPathBBox(elementId)

            // Update element properties based on new bounding box
            updateTextFromBBox(this.vs, element, bbox.w, bbox.h, options)
            const elementNode = this.vs.indexer.getNode(elementId)
            elementNode.item.update(UpdateType.TRANSFORM)
            this.updateNodes()
        } else {
            console.warn('No text document ID found for element:', elementId)
        }
    }

    /**
     * Updates text element properties for non-interpolatable changes
     * Handles special cases like content changes, alignment changes, and resizing mode changes
     *
     * @param {Element} element - The text element to update
     * @param {Map<string, any>} changes - Map of property changes
     * @param {object} options - Additional options for the update
     */
    updateTextNonInterpolatableProps(element, changes, options) {
        // Handle content and alignment changes by updating all properties
        if (changes.has('content') || changes.has('horizontalAlignment') || changes.has('verticalAlignment')) {
            this.updateTextProperties(element, options)
        }
        // Handle resizing mode changes
        else if (changes.has('resizingMode')) {
            const resizingMode = changes.get('resizingMode').value
            const previousMode = changes.get('resizingMode').original
            switch (resizingMode) {
                case TextResizeMode.AUTO_WIDTH:
                    this.updateTextProperties(element, options)
                    this.vs.dataStore.interaction.deleteElementPropertyTrack(element.get('id'), 'dimensions', true)
                    break
                case TextResizeMode.AUTO_HEIGHT:
                    if (previousMode === TextResizeMode.FIXED) {
                        this.vs.dataStore.interaction.deleteElementPropertyTrack(element.get('id'), 'dimensions', true)
                    } else {
                        this.updateTextProperties(element, options)
                    }
                    break
            }
        } else if (changes.has('width') || changes.has('height') || changes.has('size')) {
            this.updateTextProperties(element, options)
        }
    }
}
