/* eslint-disable no-unused-vars */
/** @typedef {import('./geometry/PathData').PathData} PathData */
/** @typedef {import('./math/Color').Color} Color */
import config from './config'
import { Rect2, Vector2 } from './math'
import loadDinoWasm from './libs/dino.mjs'

let _dinoLib
const loadDinoLib = async () => {
    _dinoLib = await loadDinoWasm()
}

/** @typedef {number} DinoID */

/**
 * @returns {typeof _dinoLib}
 */
export const getDinoLib = () => _dinoLib
/**
 * @returns {typeof apis}
 */
export function dino() {
    return apis
}

/**
 * @param {HTMLCanvasElement} canvas
 * @param {object} options
 * @param {Function} options.onPaint
 */
export async function init(canvas, { onPaint }) {
    if (apis.initialized) return

    await loadDinoLib()

    // extract APIs from Module, rename and save
    extract(apis, _dino<PERSON>ib, "_ph_")

    // resize canvas for the first time
    await new Promise((resolve) => requestAnimationFrame(resolve))
    const { width, height } = canvas.parentElement.getBoundingClientRect()
    apis.setWindowSize(width, height)

    apis.setAssetsPath(config.baseUrl)

    const originalOnFrameBegin = window.__ph__.onFrameBegin

    window.__ph__.onFrameBegin = () => {
        if (typeof originalOnFrameBegin === 'function') {
            originalOnFrameBegin()
        }
        if (typeof onPaint === 'function') {
            onPaint()
        }
    }
}

export class Slice {
    ptr = 0
    len = 0
}

/**
 * @param {number[]} arr
 * @returns {Slice}
 */
export function allocU8Arena(arr) {
    if (!arr || arr.length === 0) return { ptr: 0, len: 0 }
    const len = arr.length
    const ptr = _dinoLib._ph_allocArena(arr.length) >>> 0
    const view = new Uint8Array(_dinoLib.HEAP8.buffer, ptr, len)
    view.set(arr)
    return { ptr, len }
}
/**
 * @param {number[]} arr
 * @returns {Slice}
 */
export function allocF32Arena(arr) {
    if (!arr || arr.length === 0) return { ptr: 0, len: 0 }
    const len = arr.length
    const ptr = _dinoLib._ph_allocArena32(len) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, len)
    view.set(arr)
    return { ptr, len }
}
/**
 * @param {number[]} arr
 * @returns {Slice}
 */
export function allocU8Perm(arr) {
    if (!arr || arr.length === 0) return { ptr: 0, len: 0 }
    const len = arr.length
    const ptr = _dinoLib._ph_allocPerm(arr.length) >>> 0
    const view = new Uint8Array(_dinoLib.HEAP8.buffer, ptr, len)
    view.set(arr)
    return { ptr, len }
}
/**
 * @param {number[]} arr
 * @returns {Slice}
 */
export function allocF32Perm(arr) {
    if (!arr || arr.length === 0) return { ptr: 0, len: 0 }
    const len = arr.length
    const ptr = _dinoLib._ph_allocPerm32(len) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, len)
    view.set(arr)
    return { ptr, len }
}

/**
 * @param {DinoID} id
 * @returns {{x: number, y: number, w: number, h: number}}
 */
export function getPathBBox(id) {
    const ptr = apis.getPathBBoxEx(id) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
    // duplicate the buffer
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return {
        x: view_dup[0],
        y: view_dup[1],
        w: view_dup[2],
        h: view_dup[3],
    }
}
/**
 * @param {number} id
 * @returns {object}
 */
export function getTextPathBBox(id) {
    const ptr = apis.getTextPathBBoxEx(id) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return {
        x: view_dup[0],
        y: view_dup[1],
        w: view_dup[2],
        h: view_dup[3],
    }
}
/**
 * @param {DinoID} id
 * @returns {number}
 */
export function getTextLineLength(id) {
    return apis.getTextLineLength(id)
}

/**
 * @param {DinoID} id
 * @returns {number}
 */
export function getTextBaseline(id) {
    const len = apis.getTextLineLength(id) * 4
    const ptr = apis.getTextBaseline(id) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, len)
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return view_dup
}

/**
 * @param id
 * @returns {Float32Array}
 */
export function getTextSelectionBlock(id) {
    const len = apis.getTextLineLength(id) * 4
    const ptr = apis.getTextSelectionBlockEx(id) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, len)
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return view_dup
}

/**
 *
 */
export function getTextEditingNodeId() {
    return apis.getTextEditingNodeIdEx()
}

/**
 *
 */
export function getTextCaretPos() {
    const ptr = apis.getTextCaretPosEx() >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return view_dup
}

/**
 * @param {DinoID} path_id
 * @param {DinoID} stroke_id
 * @returns {{x: number, y: number, w: number, h: number}}
 */
export function getStrokeBBox(path_id, stroke_id) {
    const ptr = apis.getStrokeBBox(path_id, stroke_id) >>> 0
    const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
    // duplicate the buffer
    const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
    return {
        x: view_dup[0],
        y: view_dup[1],
        w: view_dup[2],
        h: view_dup[3],
    }
}

const apis = {
    initialized: false,

    readU8Array: (ptr, len) =>
        new Uint8Array(_dinoLib.HEAP8.buffer, ptr, len),

    /**
     * @param {number} w
     * @param {number} h
     */
    setWindowSize: (w, h) => {
        console.warn("API missing")
    },

    /**
     * @returns {number} width
     */
    getCanvasWidth: () => {
        console.warn("API missing")
        return 0
    },

    /**
     * @returns {number} height
     */
    getCanvasHeight: () => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number} len
     */
    allocArena: (len) => {
        console.warn("API missing")
    },
    /**
     * @param {number} len
     */
    allocArena32: (len) => {
        console.warn("API missing")
    },
    /**
     * @param {number} len
     */
    allocPerm: (len) => {
        console.warn("API missing")
    },
    /**
     * @param {number} len
     */
    allocPerm32: (len) => {
        console.warn("API missing")
    },
    /**
     * @param {number} ptr
     */
    freePerm: (ptr) => {
        console.warn("API missing")
    },
    /**
     * @param {number} ptr
     */
    freePerm32: (ptr) => {
        console.warn("API missing")
    },

    /**
     * @param {string} path
     */
    setAssetsPath: (path) => {
        const encoder = new TextEncoder()
        const arr = encoder.encode(path)
        const mem = allocU8Arena(arr)
        apis.setAssetsPathEx(mem.ptr, mem.len)
    },
    /**
     * @param {number} ptr
     * @param {number} len
     */
    setAssetsPathEx: (ptr, len) => {
        console.warn("API missing")
    },

    /**
     * @param {number} id
     */
    getPathBBoxEx: (id) => {
        console.warn("API missing")
    },

    getPathBBox(id) {
        const ptr = apis.getPathBBoxEx(id) >>> 0
        const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
        // duplicate the buffer
        const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
        return {
            x: view_dup[0],
            y: view_dup[1],
            w: view_dup[2],
            h: view_dup[3],
        }
    },
    getTextPathBBoxEx: (id) => {
        console.warn("API missing")
    },
    getTextPathBBox: (id) => {
        const ptr = apis.getTextPathBBoxEx(id) >>> 0
        const view = new Float32Array(_dinoLib.HEAP32.buffer, ptr, 4)
        const view_dup = new Float32Array(view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength))
        return {
            x: view_dup[0],
            y: view_dup[1],
            w: view_dup[2],
            h: view_dup[3],
        }
    },
    /**
     * @param {DinoID} id
     */
    getTextLineLength: (id) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} id
     */
    getTextBaseline: (id) => {
        console.warn("API missing")
    },

    getTextEditingNodeIdEx: () => {
        console.warn("API missing")
    },

    getTextSelectionBlockEx: (id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_id
     */
    getStrokeBBox: (path_id, stroke_id) => {
        console.warn("API missing")
    },

    GROUP: 0,
    PATH: 1,
    TEXT: 2,
    /**
     * @param {DinoID} node_id
     */
    markNodeChanged: (node_id) => {
        console.warn("API missing")
    },
    /**
     * @param {0|1|2} type
     */
    makeNode: (type) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} id
     */
    destroyNode: (id) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} parent_id
     * @param {DinoID} child_id
     */
    addNodeChild: (parent_id, child_id) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} parent_id
     */
    getNodeChildCount: (parent_id) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} parent_id
     * @param {DinoID} child_id
     * @param {number} index
     */
    insertNodeChild: (parent_id, child_id, index) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} node_id
     */
    addNodeToRoot: (node_id) => {
        console.warn("API missing")
    },
    /**
     * reorder node in parent
     * @param {DinoID} node_id
     * @param {number} index
     */
    reorderNode: (node_id, index) => {
        console.warn("API missing")
    },
    /**
     * Remove relationship of node with its parent and siblings, but will keep the child
     * @param {DinoID} node_id
     */
    removeNode: (node_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {number} x
     * @param {number} y
     * @param {number} rotation
     * @param {number} scale_x
     * @param {number} scale_y
     * @param {number} skew_x
     * @param {number} skew_y
     */
    setNodeTransform: (node_id, x, y, rotation, scale_x, scale_y, skew_x, skew_y) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {boolean} visible
     */
    setNodeVisible: (node_id, visible) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} node_id
     * @param {number} opacity
     */
    setNodeOpacity: (node_id, opacity) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} node_id
     * @param {number} blend
     */
    setNodeBlend: (node_id, blend) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} node_id
     * @param {number} idx
     * @param {DinoID} comp_id
     */
    setNodeCompose: (node_id, idx, comp_id) => {
        console.warn("API missing")
    },
    /**
     * Remove compose from node, would not destroy anything
     * @param {DinoID} node_id
     * @param {number} idx
     */
    removeNodeCompose: (node_id, idx) => {
        console.warn("API missing")
    },
    /**
     * Do three things
     * 1. Remove compose from node
     * 2. Destroy whole compose subtree
     * 3. Destroy compose
     * @param {DinoID} node_id
     * @param {number} idx
     */
    destroyNodeCompose: (node_id, idx) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} node_id
     * @param {DinoID} path_id
     */
    setNodePath: (node_id, path_id) => {
        console.warn("API missing")
    },

    updatePageOffset: (x, y) => {
        console.warn("API missing")
    },

    COLOR: 1,
    GRADIENT: 2,
    IMAGE: 3,
    PATTERN: 4,

    /**
     * @param {DinoID} node_id
     * @param {number} tag
     * @param {DinoID} paint_id
     */
    setNodeFillPaint: (node_id, tag, paint_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {number} opacity
     */
    setNodeFillOpacity: (node_id, opacity) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {number} tag
     * @param {DinoID} paint_id
     */
    setNodeStrokePaint: (node_id, tag, paint_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {number} opacity
     */
    setNodeStrokeOpacity: (node_id, opacity) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {DinoID} stroke_id
     */
    setNodeStrokeData: (node_id, stroke_id) => {
        console.warn("API missing")
    },

    /**
     * @param {PathData} path
     * @returns {DinoID} path_id
     */
    makePath: (path) => {
        if (
            (path.commands.length === 0)
            ||
            (path.vertices.length === 0)
        ) {
            return apis.makeEmptyPath()
        }
        const cmd_slice = allocU8Arena(path.commands)
        const vtx_slice = allocF32Arena(path.vertices)
        return apis.makePathEx(cmd_slice.ptr, cmd_slice.len, vtx_slice.ptr, vtx_slice.len)
    },
    makeEmptyPath: () => {
        console.warn("API missing")
    },
    /**
     * @param {number} cmd_ptr
     * @param {number} cmd_len
     * @param {number} vtx_ptr
     * @param {number} vtx_len
     */
    makePathEx: (cmd_ptr, cmd_len, vtx_ptr, vtx_len) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} path_id
     * @param {PathData} path
     */
    setPath: (path_id, path) => {
        if (
            (path.commands.length === 0)
            ||
            (path.vertices.length === 0)
        ) {
            apis.setEmptyPath(path_id)
        } else {
            const cmd_slice = allocU8Arena(path.commands)
            const vtx_slice = allocF32Arena(path.vertices)
            apis.setPathEx(path_id, cmd_slice.ptr, cmd_slice.len, vtx_slice.ptr, vtx_slice.len)
        }
    },
    /**
     * @param {DinoID} path_id
     */
    setEmptyPath: (path_id) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} path_id
     * @param {number} cmd_ptr
     * @param {number} cmd_len
     * @param {number} vtx_ptr
     * @param {number} vtx_len
     */
    setPathEx: (path_id, cmd_ptr, cmd_len, vtx_ptr, vtx_len) => {
        console.warn("API missing")
    },
    /**
     * @param {DinoID} path_id
     */
    destroyPath: (path_id) => {
        console.warn("API missing")
    },

    fetchFontEx(font_family_ptr, font_family_len, url_ptr, url_len, style, weight) {
        console.warn("API missing")
    },

    fetchFont(font_family, url, style, weight) {
        const fontAlloc = allocU8Arena(Array.from(new TextEncoder().encode(font_family)))
        const urlAlloc = allocU8Arena(Array.from(new TextEncoder().encode(url)))
        const styleIndex = style === 'Regular' ? 0 : 1
        return apis.fetchFontEx(fontAlloc.ptr, fontAlloc.len, urlAlloc.ptr, urlAlloc.len, styleIndex, weight)
    },

    getTextDocID: (node_id) => apis.getTextDocIDEx(node_id),

    getTextDocIDEx: (node_id) => {
        console.warn("API missing")
    },

    setTextDoc: (doc_id, content, cursor_index, active_block_index) => {
        const { ptr, len } = allocU8Arena(Array.from(new TextEncoder().encode(content)))
        return apis.setTextDocEx(doc_id, ptr, len, cursor_index, active_block_index)
    },

    setTextDocEx: (doc_id, ptr, len, cursor_index, active_block_index) => {
        console.warn("API missing")
    },

    /**
     * @param {number} doc_id
     */
    destroyTextDoc: (doc_id) => {
        console.warn("API missing")
    },


    setTextStyleEx(doc_id, font_family_ptr, font_family_len, font_style, fontWeight, font_size, letter_spacing, line_spacing, paragraph_spacing, horizontal_alignment, vertical_alignment) {
        console.warn("API missing")
    },

    setTextStyle(doc_id, font_family, font_style, fontWeight, font_size, letter_spacing, line_spacing, paragraph_spacing, horizontal_alignment, vertical_alignment) {
        const styleIndex = font_style === 'Regular' ? 0 : 1
        const { ptr, len } = allocU8Arena(Array.from(new TextEncoder().encode(font_family)))
        apis.setTextStyleEx(doc_id, ptr, len, styleIndex, fontWeight, font_size, letter_spacing, line_spacing, paragraph_spacing, horizontal_alignment, vertical_alignment)
    },

    enterTextEditingMode(node_id) {
        console.warn("API missing")
    },

    changeTextEditingNode(node_id, tabbing) {
        console.warn("API missing")
    },

    exitTextEditingMode() {
        console.warn("API missing")
    },

    setForceDefaultCursor(force) {
        console.warn("API missing")
    },

    setTextSelection(doc_id, start_block, start_char, end_block, end_char, cursor_index, active_block_index) {
        console.warn("API missing")
    },

    buildLayoutAndPathEx(node_id, doc_id, max_width, max_height) {
        console.warn("API missing")
    },

    buildLayoutAndPath(node_id, doc_id, max_width, max_height) {
        apis.buildLayoutAndPathEx(node_id, doc_id, max_width, max_height)
    },

    getTextOffsetEx(node_rid) {
        console.warn("API missing")
    },

    getTextOffset(node_rid) {
        const res_ptr = apis.getTextOffsetEx(node_rid) >>> 0
        const pos = new Float32Array(_dinoLib.HEAP32.buffer, res_ptr, 2)
        // duplicate the buffer
        const pos_dup = new Float32Array(pos.buffer.slice(pos.byteOffset, pos.byteOffset + pos.byteLength))
        return new Vector2(pos_dup[0], pos_dup[1])
    },


    getTextLayoutBoxEx(doc_id) {
        console.warn("API missing")
    },
    getTextLayoutBox(doc_id) {
        const res_ptr = apis.getTextLayoutBoxEx(doc_id) >>> 0
        const buf = new Float32Array(_dinoLib.HEAP32.buffer, res_ptr, 4)
        // duplicate the buffer
        const buf_dup = new Float32Array(buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength))
        return new Rect2(buf_dup[0], buf_dup[1], buf_dup[2], buf_dup[3])
    },
    /**
     * @param {number} r
     * @param {number} g
     * @param {number} b
     * @param {number} a
     * @returns {number} color_id
     */
    makeColor: (r, g, b, a) => {
        console.warn("API missing")
        return 0
    },
    /**
     * @param {number} id
     * @param {number} r
     * @param {number} g
     * @param {number} b
     * @param {number} a
     */
    setColor: (id, r, g, b, a) => {
        console.warn("API missing")
    },
    /**
     * @param {number} id
     */
    destroyColor: (id) => {
        console.warn("API missing")
    },

    /**
     * @param {number} gradient_type
     * @param {number} a grad matrix a
     * @param {number} b grad matrix b
     * @param {number} c grad matrix c
     * @param {number} d grad matrix d
     * @param {number} e grad matrix e
     * @param {number} f grad matrix f
     * @returns {DinoID} gradient_id
     */
    makeGradientEx: (gradient_type, a, b, c, d, e, f) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number} gradient_type
     * @param {Transform2D} matrix
     * @returns {DinoID} gradient_id
     */
    makeGradient: (gradient_type, matrix) =>
        apis.makeGradientEx(gradient_type, matrix.a, matrix.b, matrix.c, matrix.d, matrix.tx, matrix.ty),

    /**
     * @param {DinoID} gradient_id
     * @param {number} a grad matrix a
     * @param {number} b grad matrix b
     * @param {number} c grad matrix c
     * @param {number} d grad matrix d
     * @param {number} e grad matrix e
     * @param {number} f grad matrix f
     */
    setGradientMatrixEx: (gradient_id, a, b, c, d, e, f) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} gradient_id
     * @param {number} matrix
     */
    setGradientMatrix: (gradient_id, matrix) => {
        apis.setGradientMatrixEx(gradient_id, matrix.a, matrix.b, matrix.c, matrix.d, matrix.tx, matrix.ty)
    },

    /**
     * @param {DinoID} gradient_id
     * @param {number} tag
     */
    setGradientTag: (gradient_id, tag) => {
        console.warn("API missing")
    },

    /**
     * note: Remember to call updateGradientPixels after setting all stops
     * @param {DinoID} gradient_id
     * @param {number} len stop count
     */
    setGradientStopLen: (gradient_id, len) => {
        console.warn("API missing")
    },

    /**
     * note: Remember to call updateGradientPixels after setting all stops
     * @param {DinoID} gradient_id
     * @param {number} index
     * @param {number} pos
     * @param {number} r
     * @param {number} g
     * @param {number} b
     * @param {number} a
     */
    setGradientStop: (gradient_id, index, pos, r, g, b, a) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} gradient_id
     */
    updateGradientPixels: (gradient_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} gradient_id
     */
    destroyGradient: (gradient_id) => {
        console.warn("API missing")
    },

    /**
     * @param {number} width
     * @param {number} cap
     * @param {number} join
     * @param {number} miter_limit
     * @param {number} dash
     * @param {number} gap
     * @returns {DinoID} stroke_id
     */
    makeStroke: (width, cap, join, miter_limit, dash, gap) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} stroke_id
     * @param {number} width
     * @param {number} cap
     * @param {number} join
     * @param {number} miter_limit
     * @param {number} dash
     * @param {number} gap
     */
    setStroke: (stroke_id, width, cap, join, miter_limit, dash, gap) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {DinoID} path_id
     */
    destroyStrokePathCacheEx: (node_id, path_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} node_id
     * @param {DinoID} path_id
     */
    destroyStrokePathCache: (node_id, path_id) => {
        apis.destroyStrokePathCacheEx(node_id, path_id)
    },

    /**
     * @param {DinoID} stroke_id
     */
    destroyStroke: (stroke_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} path_id
     * @returns {number} ptr
     */
    getPathVtxEx: (path_id) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} path_id
     * @returns {number[]} vertices
     */
    getPathVtx: (path_id) => {
        const ptr = apis.getPathVtxEx(path_id) >>> 0
        const view = new Uint32Array(_dinoLib.HEAP32.buffer, ptr, 2)
        if (view[1] === 0) return []
        const vertices = new Float32Array(_dinoLib.HEAP32.buffer, view[0], view[1])
        // duplicate the buffer
        const vertices_dup = new Float32Array(vertices.buffer.slice(vertices.byteOffset, vertices.byteOffset + vertices.byteLength))
        return Array.from(vertices_dup)
    },

    /**
     * @param {DinoID} path_id
     * @returns {number} ptr
     */
    getPathCmdEx: (path_id) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} path_id
     * @returns {number[]} commands
     */
    getPathCmd: (path_id) => {
        const ptr = apis.getPathCmdEx(path_id) >>> 0
        const view = new Uint32Array(_dinoLib.HEAP32.buffer, ptr, 2)
        if (view[1] === 0) return []
        const commands = new Uint8Array(_dinoLib.HEAP8.buffer, view[0], view[1])
        // duplicate the buffer
        const commands_dup = new Uint8Array(commands.buffer.slice(commands.byteOffset, commands.byteOffset + commands.byteLength))
        return Array.from(commands_dup)
    },

    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_id
     * @returns {number} ptr
     */
    getStrokePathCmdEx: (path_id, stroke_id) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_id
     * @returns {number[]} commands
     */
    getStrokePathCmd: (path_id, stroke_id) => {
        const ptr = apis.getStrokePathCmdEx(path_id, stroke_id) >>> 0
        const view = new Uint32Array(_dinoLib.HEAP32.buffer, ptr, 2)
        if (view[1] === 0) return []
        const commands = new Uint8Array(_dinoLib.HEAP8.buffer, view[0], view[1])
        // duplicate the buffer
        const commands_dup = new Uint8Array(commands.buffer.slice(commands.byteOffset, commands.byteOffset + commands.byteLength))
        return Array.from(commands_dup)
    },

    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_id
     * @returns {number} ptr
     */
    getStrokePathVtxEx: (path_id, stroke_id) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_id
     * @returns {number[]} vertices
     */
    getStrokePathVtx: (path_id, stroke_id) => {
        const ptr = apis.getStrokePathVtxEx(path_id, stroke_id) >>> 0
        const view = new Uint32Array(_dinoLib.HEAP32.buffer, ptr, 2)
        if (view[1] === 0) return []
        const vertices = new Float32Array(_dinoLib.HEAP32.buffer, view[0], view[1])
        // duplicate the buffer
        const vertices_dup = new Float32Array(vertices.buffer.slice(vertices.byteOffset, vertices.byteOffset + vertices.byteLength))
        return Array.from(vertices_dup)
    },

    getTextPathEx: (text_doc_id) => {
        console.warn("API missing")
        return 0
    },
    getTextPathData: (node_rid, text_doc_id) => {
        const buf = apis.getTextPathEx(text_doc_id)
        const ptr = buf >>> 0
        const view = new Uint32Array(_dinoLib.HEAP32.buffer, ptr, 4)
        if (view[1] === 0 || view[3] === 0) return { commands: [], vertices: []}
        const commands = new Uint8Array(_dinoLib.HEAP8.buffer, view[0], view[1])
        const vertices = new Float32Array(_dinoLib.HEAP32.buffer, view[2], view[3])
        // duplicate the buffer
        const commands_dup = new Uint8Array(commands.buffer.slice(commands.byteOffset, commands.byteOffset + commands.byteLength))
        const vertices_dup = new Float32Array(vertices.buffer.slice(vertices.byteOffset, vertices.byteOffset + vertices.byteLength))
        return {
            commands: commands_dup,
            vertices: vertices_dup,
        }
    },
    /**
     * @param {string} doc_id
     */
    destoryTextPath: (doc_id) => {
        console.warn("API missing")
    },

    getTextPathWithStyle: (node_rid, text_doc_id, textOption) => {
        apis.destoryTextPath(text_doc_id)
        apis.setTextStyle(
            text_doc_id,
            textOption.font_family,
            textOption.font_style,
            textOption.font_weight,
            textOption.font_size,
            textOption.letter_spacing,
            textOption.line_spacing,
            textOption.paragraph_spacing,
            textOption.text_horizontal_alignment,
            textOption.text_vertical_alignment,
        )

        apis.buildLayoutAndPath(node_rid, text_doc_id, -1, -1)
        const data = apis.getTextPathData(node_rid, text_doc_id)
        const path_id = apis.makePath(data)
        const commands = dino().getPathCmd(path_id)
        const vertices = dino().getPathVtx(path_id)
        apis.destoryTextPath(text_doc_id)
        return {vertices, commands}
    },

    /**
     * @returns {DinoID} image_id
     */
    allocImage: () => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number} ptr
     * @param {number} len
     * @returns {DinoID} image_id
     */
    makeImageWithURLEx: (ptr, len) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {string} url
     * @returns {DinoID} image_id
     */
    makeImageWithURL: (url) => {
        const { ptr, len } = allocU8Arena(Array.from(new TextEncoder().encode(url)))
        return apis.makeImageWithURLEx(ptr, len)
    },

    /**
     * @param {number} pixels_ptr
     * @param {number} pixels_len
     * @param {number} width
     * @param {number} height
     * @returns {DinoID} image_id
     */
    makeImageWithPixelsEx: (pixels_ptr, pixels_len, width, height) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number[]} pixels
     * @param {number} width
     * @param {number} height
     * @returns {DinoID} image_id
     */
    makeImageWithPixels: (pixels, width, height) => {
        const { ptr, len } = allocU8Arena(pixels)
        return apis.makeImageWithPixelsEx(ptr, len, width, height)
    },

    /**
     * @param {number} ptr
     * @param {number} len
     * @returns {DinoID} image_id
     */
    makeImageWithFileDataEx: (ptr, len) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number[]} fileData
     * @returns {DinoID} image_id
     */
    makeImageWithFileData: (fileData) => {
        const { ptr, len } = allocU8Arena(fileData)
        return apis.makeImageWithFileDataEx(ptr, len)
    },

    /**
     * @param {DinoID} image_id
     * @param {number[]} pixels
     * @param {number} width
     * @param {number} height
     */
    setImageWithPixels: (image_id, pixels, width, height) => {
        const { ptr, len } = allocU8Arena(pixels)
        apis.setImageWithPixelsEx(image_id, ptr, len, width, height)
    },

    /**
     * @param {DinoID} image_id
     * @param {number} pixels_ptr
     * @param {number} pixels_len
     * @param {number} width
     * @param {number} height
     */
    setImageWithPixelsEx: (image_id, pixels_ptr, pixels_len, width, height) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} image_id
     * @param {number} fill_mode
     */
    setImageFillMode: (image_id, fill_mode) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} image_id
     */
    destroyImage: (image_id) => {
        console.warn("API missing")
    },

    /**
     * @param {number} comp_tag
     * @param {DinoID} root_id
     * @returns {DinoID} comp_id
     */
    makeCompose: (comp_tag, root_id) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {DinoID} comp_id
     * @param {DinoID} root_id
     */
    setComposeRoot: (comp_id, root_id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} comp_id
     */
    destroyCompose: (comp_id) => {
        console.warn("API missing")
    },

    /**
     * @param {number} r
     * @param {number} g
     * @param {number} b
     * @param {number} a
     */
    setBackgroundColor: (r, g, b, a) => {
        console.warn("API missing")
    },

    /**
     * @returns {number}
     */
    numNodesRender: () => {
        console.warn("API missing")
        return 0
    },

    /**
     * @returns {number}
     */
    numTilesRender: () => {
        console.warn("API missing")
        return 0
    },

    // overlay

    /**
     * @param {number} a
     * @param {number} b
     * @param {number} c
     * @param {number} d
     * @param {number} e
     * @param {number} f
     */
    setTransform: (a, b, c, d, e, f) => {
        console.warn("API missing")
    },

    /**
     * @param {number} r 0-1
     * @param {number} g 0-1
     * @param {number} b 0-1
     * @param {number} a 0-1
     */
    fillStyle: (r, g, b, a) => {
        console.warn("API missing")
    },

    /**
     * @param {number} width
     * @param {number} r 0-1
     * @param {number} g 0-1
     * @param {number} b 0-1
     * @param {number} a 0-1
     */
    strokeStyle: (width, r, g, b, a) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} cmd_ptr
     * @param {number} cmd_len
     * @param {number} vtx_ptr
     * @param {number} vtx_len
     */
    drawPathEx: (layer_id, x, y, cmd_ptr, cmd_len, vtx_ptr, vtx_len) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {PathData} path
     */
    drawPath: (layer_id, x, y, path) => {
        if (
            (path.commands.length === 0)
            ||
            (path.vertices.length === 0)
        ) {
            return
        }
        const cmd_slice = allocU8Arena(path.commands)
        const vtx_slice = allocF32Arena(path.vertices)
        apis.drawPathEx(layer_id, x, y, cmd_slice.ptr, cmd_slice.len, vtx_slice.ptr, vtx_slice.len)
    },

    /**
     * @param {number} layer_id
     * @param {number} x0
     * @param {number} y0
     * @param {number} x1
     * @param {number} y1
     */
    drawLine: (layer_id, x0, y0, x1, y1) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x0
     * @param {number} y0
     * @param {number} x1
     * @param {number} y1
     */
    drawLineShadow: (layer_id, x0, y0, x1, y1) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} w
     * @param {number} h
     */
    drawRect: (layer_id, x, y, w, h) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} w
     * @param {number} h
     * @param {number} corner_radius
     */
    drawRoundedRect: (layer_id, x, y, w, h, corner_radius) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} w
     * @param {number} h
     */
    drawSolidRect: (layer_id, x, y, w, h) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} w
     * @param {number} h
     * @param {number} corner_radius
     */
    drawSolidRoundedRect: (layer_id, x, y, w, h, corner_radius) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} radius
     */
    drawCircle: (layer_id, x, y, radius) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} radius
     */
    drawSolidCircle: (layer_id, x, y, radius) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} radius
     */
    drawCircleShadow: (layer_id, x, y, radius) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} radius_x
     * @param {number} radius_y
     * @param {number} rot
     */
    drawEllipse: (layer_id, x, y, radius_x, radius_y, rot = 0) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} radius_x
     * @param {number} radius_y
     * @param {number} rot
     */
    drawSolidEllipse: (layer_id, x, y, radius_x, radius_y, rot = 0) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} width
     * @param {number} height
     * @param {number} rot
     */
    drawEllipseShadow: (layer_id, x, y, width, height, rot) => {
        throw new Error("API missing")
    },


    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {number} text_ptr
     * @param {number} text_len
     * @param {number} hor_align
     * @param {number} ver_align
     * @param {number} font
     */
    drawTextEx: (layer_id, x, y, text_ptr, text_len, hor_align, ver_align, font) => {
        console.warn("API missing")
    },

    /**
     * @param {number} layer_id
     * @param {number} x
     * @param {number} y
     * @param {string} text
     * @param {number} hor_align
     * @param {number} ver_align
     * @param {number} font
     * @param {number} rotation
     */
    drawText: (layer_id, x, y, text, hor_align, ver_align, font, rotation) => {
        const textAry = Array.from(new TextEncoder().encode(text))
        const { ptr, len } = allocU8Arena(textAry)
        const new_ptr = ptr >>> 0
        apis.drawTextEx(layer_id, x, y, new_ptr, len, hor_align, ver_align, font, rotation)
    },

    /**
     * @param {number} text_ptr
     * @param {number} text_len
     * @param {number} font
     */
    measureTextEx: (text_ptr, text_len, font) => {
        console.warn("API missing")
    },

    /**
     * @param {string} text
     * @param {number} font
     * @returns {{ w: number, h: number }}
     */
    measureText: (text, font) => {
        const textAry = Array.from(new TextEncoder().encode(text))
        const { ptr, len } = allocU8Arena(textAry)
        const res_ptr = apis.measureTextEx(ptr, len, font) >>> 0
        const view = new Float32Array(_dinoLib.HEAP32.buffer, res_ptr, 2)
        return {
            w: view[0],
            h: view[1],
        }
    },

    /**
     * @param {number} pixels_ptr
     * @param {number} pixels_len
     * @param {number} width
     * @param {number} height
     * @returns {DinoID} id
     */
    uploadImageEx: (pixels_ptr, pixels_len, width, height) => {
        console.warn("API missing")
        return 0
    },

    /**
     * @param {number[]} pixels
     * @param {number} width
     * @param {number} height
     * @returns {DinoID} id
     */
    uploadImage: (pixels, width, height) => {
        const { ptr, len } = allocU8Arena(pixels)
        return apis.uploadImageEx(ptr, len, width, height)
    },
    destroyCanvasImage: (id) => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} layer_id
     * @param {DinoID} image_id
     * @param {number} x
     * @param {number} y
     * @param {number} w
     * @param {number} h
     */
    drawImage: (layer_id, image_id, x, y, w, h) => {
        console.warn("API missing")
    },

    purge: () => {
        console.warn("API missing")
    },

    /**
     * @param {DinoID} path_id
     * @param {number} x
     * @param {number} y
     * @returns {boolean}
     */
    isPointInPath: (path_id, x, y) => {
        console.warn("API missing")
        return false
    },
    /**
     * @param {DinoID} path_id
     * @param {DinoID} stroke_data_id
     * @param {number} x
     * @param {number} y
     * @returns {boolean}
     */
    isPointInStroke: (path_id, stroke_data_id, x, y) => {
        console.warn("API missing")
        return false
    },

    /**
     * @param {DinoID} node_id
     * @param {number} x
     * @param {number} y
     * @returns {boolean}
     */
    isPointInText: (node_id, x, y) => {
        console.warn("API missing")
        return false
    },

    /**
     * @param {number} r 0.0 - 1.0
     * @param {number} g 0.0 - 1.0
     * @param {number} b 0.0 - 1.0
     * @param {number} a 0.0 - 1.0
     */
    setCaptureBackgroundColor: (r, g, b, a) => {
        console.warn("API missing")
    },
    /**
     * @param {number} node_id
     * @param {number} clip_x
     * @param {number} clip_y
     * @param {number} clip_w
     * @param {number} clip_h
     * @param {number} output_w
     * @param {number} output_y
     */
    capture: (node_id, clip_x, clip_y, clip_w, clip_h, output_w, output_y) => {
        console.warn("API missing")
    },

    pauseApp: () => {
        console.warn("API missing")
    },

    resumeApp: () => {
        console.warn("API missing")
    },

    setNodeFontFamily: (node_id, fontFamily) => {
    },
    setNodeFontWeight: (node_id, fontWeight) => {
    },
    setNodeLineHeight: (node_id, lineHeight) => {
    },
    setNodeLetterSpacing: (node_id, letterSpacing) => {
    },
    setNodeFontSize: (node_id, fontSize) => {
    },
    setNodeTextContent: (node_id, content) => {
    },
    setNodeHorizontalAlignment: (node_id, horizontalAlignment) => {
    },
    setNodeVerticalAlignment: (node_id, verticalAlignment) => {
    },

    getNodeStorageCount: () => {
        console.warn("API missing")
        return 0
    },
    getColorStorageCount: () => {
        console.warn("API missing")
        return 0
    },
    getPathStorageCount: () => {
        console.warn("API missing")
        return 0
    },
    getStrokeStorageCount: () => {
        console.warn("API missing")
        return 0
    },
}

/**
 * @param {Record<string, Function>} out
 * @param {Record<string, Function>} obj
 * @param {string} prefix
 */
function extract(out, obj, prefix) {
    const len = prefix.length
    for (const key in obj) {
        if (typeof obj[key] === 'function' && key.startsWith(prefix)) {
            out[key.substring(len)] = obj[key]
        }
    }
}
