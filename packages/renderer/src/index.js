import Stats from '@phase-software/data-utils/src/Stats'
import {DirectionType,  DocumentMode, Events, UpdateLevel, ChangeEventName, TextResizeMode, EventFlag }  from '@phase-software/types'
import IS from '@phase-software/input-system'
// eslint-disable-next-line import/no-extraneous-dependencies
import { ContentType } from '@phase-software/data-store/src/Clipboard'
import { VisualServer } from './visual_server/VisualServer'
import { initPanes, updatePanes } from './panes'
import { Vector2 } from './math'
import * as actions from './actions'
import {
    findTopMostElementAt,
    findVisualTouchedNodeAt,
    addScreenNameToHitTest,
    updateScreenNameHitTest,
    clearNotSceneNodeHitTest,
    updateHitTest,
    updateGuidelinesHitTest
} from './actions/selection'
import {
    clearAllControllers
} from './update-controller'
import { clearCaptureCallbacks, onCapture, takeSnapshotAsBlob } from './utils/snapshot'
import Status from './status'
import { MP4Encoder } from './actions/export_media/mp4'
import { GIFEncoder } from './actions/export_media/gif'
import { init as initDino, dino, getDinoLib, allocU8Arena } from './dino'
import { updateTextFromBBox } from './utils/textUtils'

/** @typedef {import('@phase-software/data-store/src/Workspace').Workspace} Workspace */
/** @typedef {import('@phase-software/data-store/src/DataStore').DataStore} DataStore */
/** @typedef {import('./visual_server/RenderItem').RenderItem} RenderItem */
/** @typedef {import('@phase-software/data-store/src/layer/ComputedLayer').ComputedLayer} ComputedLayer */
/** @typedef {import('@phase-software/data-store/src/Element').Element} Element */
/** @typedef {import('./overlay/Overlay').Overlay} Overlay */
/** @typedef {import('./visual_server/VisualStorage').VisualStorage} VisualStorage */
/** @typedef {import('./visual_server/SpatialCache').NodeID} NodeID */
/** @typedef {import('./dino').DinoID} DinoID */

let frameCounter = 0
let visible = false

/** @type {DataStore | null} */
let dataStore = null
/** @type {HTMLCanvasElement | null} */
let canvas = null

const api = dino()

/**
 * There will be only one instance of `VisualServer` during
 * the whole app life time
 * @type {VisualServer}
 */
let VS = null
/**
 * @param {HTMLCanvasElement} c
 * @param {DataStore} ds
 * @param {object} options
 * @param {Function} options.onPaint
 */
export async function init(c, ds, { onPaint }) {
    dataStore = ds
    canvas = c
    await initDino(c, { onPaint })
}

function loadStart() {
    Status.pause()
}

async function load() {
    Status.updateRootDirty(true)

    Status.resume()

    visible = true

    window.dispatchEvent(new Event("resize"))
    api.resumeApp()
}

export function resize() {
    Status.updateResizeDirty(true)
}

/**
 * Moves viewport to center on selection
 */
export function centerSelection() {
    actions.centerSelection()
}

/**
 * @param {number} x
 * @param {number} y
 * @returns {Vector2}
 */
export const toWorld = (x, y) => {
    if (VS) {
        const v = new Vector2(x, y)
        const w = VS.viewport.toWorld(v)
        return w
    } else {
        return new Vector2(x, y)
    }
}

export function cleanupRenderer() {
    clearNotSceneNodeHitTest()
    clearAllControllers()
    if (VS) VS.clear()

    // in case the dino wasm is not loaded yet
    if (api.initialized) {
        api.purge()

        Status.pause()

        visible = false

        api.pauseApp()
    }

    clearCaptureCallbacks()
}

export const TEXT_RESIZE_MODE_MAP = Object.freeze({
    [TextResizeMode.AUTO_WIDTH]: 'auto_width',
    [TextResizeMode.AUTO_HEIGHT]: 'auto_height',
    [TextResizeMode.FIXED]: 'fixed',
})

export const TEXT_RESIZE_MODE_MAP_REVERSE = Object.freeze({
    'auto_width': TextResizeMode.AUTO_WIDTH,
    'auto_height': TextResizeMode.AUTO_HEIGHT,
    'fixed': TextResizeMode.FIXED,
})

/* Implementation */

window.__ph__ = {
    // Dino might not be ready yet, so add a onInited callback
    onReady: () => {
        // dino wasm inited here instead of script loaded
        api.initialized = true

        VS = new VisualServer(canvas, dataStore)
        // canvas bg color is 0x0c0c0c
        api.setBackgroundColor(12/255, 12/255, 12/255, 1)

        initPanes(VS)

        IS.watch(canvas)
        actions.init(VS)

        initRootListeners()

        VS.indexer.connectDataStore(dataStore)
        VS.selection.watchDSSelection()

        // in case the data store is not loaded yet
        if (dataStore.isLoaded) {
            load()
        } else {
            dataStore.once(ChangeEventName.LOAD, load)
        }
    },
    onFrameBegin: () => {
        // TOFIX: this is a hack to fix the cursor issue
        // better solution might be to handle cursor in editor.zig (dino)
        canvas.style.cursor = 'inherit'

        checkRendererReadyState()

        frameCounter++
    },
    onUpdateSceneTree: () => {
        if (Status.paused) return null

        const focusContent = setRoot()

        Stats.begin("node update")
        VS.updateNodes()

        switch (VS._updateLevel) {
            case UpdateLevel.UPDATE_NEED_BBOX_RECALC:
                VS.view.update()
                break
            case UpdateLevel.UPDATE_ALL_NEED_BBOX_RECALC:
                // console.log('props update all need bbox recalc')
                VS.view.updateAll(true)
                break
            case UpdateLevel.UPDATE_ALL:
                // console.log('props update')
                VS.view.updateAll(false)
                break
            case UpdateLevel.UPDATE:
            case UpdateLevel.NONE:
                break
            default:
                break
        }

        VS._updateLevel = UpdateLevel.NONE

        Stats.end("node update")

        Stats.begin("hit test")
        updateHitTest()
        Stats.end("hit test")

        VS.view.layout()

        Stats.begin("misc update")
        VS.selection.updateBounds()

        if (focusContent) focusContent()
    },
    /**
     * @param {DinoID} node_id
     * @param {number} flag
     */
    onNodeUpdate: (node_id, flag) => {
        if(VS.view.hasParent(node_id)) {
            const nodeId = VS.view.getParent(node_id)
            VS.indexer.getNode(nodeId).item.update(flag)
            VS.nodeFlagsList.set(nodeId, flag)
        } else {
            console.log(`cannot get phase node by dino rid ${node_id}`)
        }
    },
    onTextUpdate: (text_ptr, text_len, isComposing, start, end, cursor) => {
        const singleSelection = VS.selection.single
        if (!singleSelection) {
            console.log('no selection during text update')
            return
        }

        let text = ''
        if (text_len !== 0)  {
            const ptr = text_ptr >>> 0
            const textArray = new Uint8Array(getDinoLib().HEAPU8.buffer, ptr, text_len)
            // Use TextDecoder to convert the Uint8Array to a string
            const decoder = new TextDecoder('utf-8')
            text = decoder.decode(textArray)
        }
        // if the text is '\\0', it means the text is empty
        if (text === '\0') text = ''
        // update text selection
        dataStore.selection.updateTextRange(start, end, cursor, { commit: false })
        // update text content
        dataStore.editor.setProps({
            content: text
        }, { commit: false })

        if (!isComposing) {
            dataStore.debounceCommitUndo()
        }
    },
    // * Mouse events already handled by IS at renderer/src/actions/index.js
    onExitTextEditingMode: () => {},
    onCopyText: (text_ptr, text_len) => {
        let text = ''
        if (text_len !== 0)  {
            const ptr = text_ptr >>> 0
            const textArray = new Uint8Array(getDinoLib().HEAPU8.buffer, ptr, text_len)
            // Use TextDecoder to convert the Uint8Array to a string
            const decoder = new TextDecoder('utf-8')
            // eslint-disable-next-line no-control-regex
            text = decoder.decode(textArray)
        }
        // set the copied text to clipboard
        dataStore.clipboard.copy(ContentType.TEXT, { text })
    },
    onPasteText: () => {
        // paste the text from clipboard
        dataStore.clipboard.paste()
    },
    onUndoComb: () => {
        dataStore.eam.undo()
    },
    onRedoComb: () => {
        dataStore.eam.redo()
    },
    onSelectionUpdate: (text_node_id, start_block, start_char, end_block, end_char, cursor, active_block_index) => {
        const nodeId = VS.view.getParent(text_node_id)
        const node = VS.indexer.getNode(nodeId)
        const content = node.item.content
        const {start, end, cursor_idx} = getTextRange(content, start_block, start_char, end_block, end_char, cursor, active_block_index)
        dataStore.selection.updateTextRange(start, end, cursor_idx, { commit: false })
        dataStore.debounceCommitUndo()
        // update text selection
    },
    onUpdatePageOffset: (text_node_id) => {
        const nodeId = VS.view.getParent(text_node_id)
        const bbox = VS.indexer.getNode(nodeId).boundsWorldAABB
        const worldBbox = VS.viewport.toScreenRect(bbox)
        dino().updatePageOffset(worldBbox.x, worldBbox.y)
    },
    // we call this function when we have font file is loaded and the text node needs to be updated its size
    onUpdateElementSize: (text_node_id, width, height) => {
        const nodeId = VS.view.getParent(text_node_id)
        const node = VS.indexer.getNode(nodeId)
        node.item.transform.setSize({x: width, y: height})
        const element = VS.dataStore.getById(nodeId)
        updateTextFromBBox(VS, element, width, height, {flags: EventFlag.FROM_TEXT_EDIT, commit: false })
    },
    onGetTextPathId: (text_node_id) => {
        const nodeId = VS.view.getParent(text_node_id)
        const dinoTree = VS.view.getOrCreateDinoTree(nodeId)
        return dinoTree.path_id
    },
    onESC: () => {
        // exit text editing mode
        dataStore.eam.escape()
    },
    onTab: () => {
        dataStore.eam.editSiblingTextElement(DirectionType.UP)
    },
    onShiftTab: () => {
        dataStore.eam.editSiblingTextElement(DirectionType.DOWN)
    },
    onPrepareCamera: () => {
        if (!Status.paused && visible) {
            VS.viewport.resize()
            const { x, y, scale } = VS.viewport
            api.setCamera(-x, -y, scale)

            actions.update()
            VS.storage.update()
            Stats.end("misc update")
            return true
        } else {
            Stats.end("misc update")
            return false
        }
    },
    onOverlay: () => {
        if (Status.paused || !visible) return null

        if (VS.dataStore.isEditingState && !VS.dataStore.isTableView) {
            const node = VS.indexer.getActiveScreenNode()
            addScreenNameToHitTest(node)
            updateScreenNameHitTest()
            updateGuidelinesHitTest()
        }
        updatePanes()
    },
    onFrameEnd: () => {
        // console.log(`${dino().numNodesRender()} nodes, ${dino().numTilesRender()} tiles rendered`)
    },
    onCapture: (ptr, len) => {
        onCapture(ptr, len)
    },
    onFontRequest: (font_family_ptr, font_family_len, font_style, font_weight) => {
        const font_family_slice = new Uint8Array(getDinoLib().HEAPU8.buffer, font_family_ptr, font_family_len)
        const font_family = new TextDecoder('utf-8').decode(font_family_slice)
        const FONT_STYLES = {
            0: 'Regular',
            1: 'Italic',
            2: 'Oblique'
        }
        const style = FONT_STYLES[font_style] || 'Regular'
        let url = VS.dataStore.fontManager.getFontUrl(font_family, style, font_weight)
        if (!url) {
            console.warn(`cannot found the URL for ${font_family}-${font_style}-${font_weight}`)
            url = VS.dataStore.fontManager.getFontUrl(font_family, "Regular", 400)
        }
        dino().fetchFont(font_family, url, style, font_weight)
    },
    onZoom: (zoom) => {
        dataStore.eam.changeZoom(zoom)
    },
    onZoomToFit: () => {
        dataStore.eam.zoomFitContent()
    },
    onZoomToSelection: () => {
        dataStore.eam.zoomFitSelection()
    },
    onZoomOut: () => {
        dataStore.eam.zoomOut()
    },
    onZoomIn: () => {
        dataStore.eam.zoomIn()
    },
}


function checkRendererReadyState() {
    if (frameCounter === 10) {
        // create a div with ready state on a page
        const g = document.createElement('div')
        g.setAttribute('data-test-id', 'rendererReady')
        document.getElementById('modal').appendChild(g)
    }
}

function initRootListeners() {
    const { dataStore, viewport } = VS

    dataStore.on(ChangeEventName.LOAD_START, loadStart)
    dataStore.on(ChangeEventName.LOAD, load)

    dataStore.eam.on(Events.SWITCH_DOCUMENT_MODE, () => {
        if (dataStore.isPrototypeMode) {
            Status.updateRootDirty(true)
        }
    })

    dataStore.workspace.on(ChangeEventName.WORKSPACE_CHANGE_WATCH, () => {
        Status.updateRootDirty(true)
    })

    // listen to zoom changes
    dataStore.workspace.on('scale', scale => {
        viewport.setZoom(scale)
    })

    // set workspace viewport zoom and position
    viewport.on('update', () => {
        dataStore.workspace.sets({
            scale: viewport.scale,
            panX: viewport.x,
            panY: viewport.y
        })
    })
}

/**
 * @returns {() => void} focusContent function
 */
function setRoot() {
    if (!Status.dirty.root) return
    if (!VS.dataStore.isLoaded) return
    Status.updateRootDirty(false)
    // force a resize check before focusing content
    Status.updateResizeDirty(true)

    /** @type {import('@phase-software/data-store/src/DataStore').State} */
    const state = VS.dataStore.get('state')
    switch (state) {
        case DocumentMode.EDITING:
        case DocumentMode.VIEWING:
        case DocumentMode.VERSIONING:
        case DocumentMode.PROTOTYPING:
        {
            VS.viewport.resize()
            return () => actions.focusContent()
        }
    }
}

/**
 * @param {string} content
 * @param {number} start_block
 * @param {number} start_char
 * @param {number} end_block
 * @param {number} end_char
 * @param {number} cursor
 * @param {number} active_block_index
 * @returns {{start: number, end: number, cursor_idx: number}}
 */
function getTextRange(content, start_block, start_char, end_block, end_char, cursor, active_block_index) {
    let index = 0
    let block_index = 0
    let start = null
    let end = null
    let cursor_index = null

    for (let i = 0; i <= content.length; i++) {
        if (cursor_index === null && block_index === active_block_index) {
            cursor_index = index + cursor
        }
        if (block_index === start_block && start === null) {
            start = index + start_char
        }
        if (block_index === end_block && end === null) {
            end = index + end_char
        }
        // the index could be at the end of the content then we need to go through the loop again to get start and end
        if (i < content.length && content[i] === '\n') {
            block_index++
        }
        index++
        if (start !== null && end !== null) {
            break
        }
    }

    // Handle case where indices are not found
    if (start === null || end === null) {
        console.warn("Start or end index not found in the content.")
        return null
    }

    return {
        start: start,
        end: end,
        cursor_idx: cursor_index
    }
}

export {
    findTopMostElementAt,
    findVisualTouchedNodeAt,
    takeSnapshotAsBlob,
    MP4Encoder,
    GIFEncoder
}
