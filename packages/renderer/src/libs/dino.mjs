var Module = (() => {
  var _scriptName = import.meta.url;
  
  return (
async function(moduleArg = {}) {
  var moduleRtn;

var Module=moduleArg;var readyPromiseResolve,readyPromiseReject;var readyPromise=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof WorkerGlobalScope!="undefined";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string"&&process.type!="renderer";if(ENVIRONMENT_IS_NODE){const{createRequire}=await import("module");var require=createRequire(import.meta.url)}var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readAsync,readBinary;if(ENVIRONMENT_IS_NODE){var fs=require("fs");var nodePath=require("path");if(!import.meta.url.startsWith("data:")){scriptDirectory=nodePath.dirname(require("url").fileURLToPath(import.meta.url))+"/"}readBinary=filename=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename);return ret};readAsync=async(filename,binary=true)=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename,binary?undefined:"utf8");return ret};if(!Module["thisProgram"]&&process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptName){scriptDirectory=_scriptName}if(scriptDirectory.startsWith("blob:")){scriptDirectory=""}else{scriptDirectory=scriptDirectory.slice(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}{if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=async url=>{if(isFileURI(url)){return new Promise((resolve,reject)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){resolve(xhr.response);return}reject(xhr.status)};xhr.onerror=reject;xhr.send(null)})}var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];var wasmBinary=Module["wasmBinary"];var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAP64,HEAPU64,HEAPF64;var runtimeInitialized=false;var isFileURI=filename=>filename.startsWith("file://");function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b);Module["HEAP64"]=HEAP64=new BigInt64Array(b);Module["HEAPU64"]=HEAPU64=new BigUint64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;wasmExports["Wc"]()}function preMain(){}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var wasmBinaryFile;function findWasmBinary(){if(Module["locateFile"]){return locateFile("dino.wasm")}return new URL("dino.wasm",import.meta.url).href}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}async function getWasmBinary(binaryFile){if(!wasmBinary){try{var response=await readAsync(binaryFile);return new Uint8Array(response)}catch{}}return getBinarySync(binaryFile)}async function instantiateArrayBuffer(binaryFile,imports){try{var binary=await getWasmBinary(binaryFile);var instance=await WebAssembly.instantiate(binary,imports);return instance}catch(reason){err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)}}async function instantiateAsync(binary,binaryFile,imports){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE){try{var response=fetch(binaryFile,{credentials:"same-origin"});var instantiationResult=await WebAssembly.instantiateStreaming(response,imports);return instantiationResult}catch(reason){err(`wasm streaming compile failed: ${reason}`);err("falling back to ArrayBuffer instantiation")}}return instantiateArrayBuffer(binaryFile,imports)}function getWasmImports(){return{a:wasmImports}}async function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmExports=applySignatureConversions(wasmExports);wasmMemory=wasmExports["Vc"];updateMemoryViews();wasmTable=wasmExports["qf"];removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){return receiveInstance(result["instance"])}var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{receiveInstance(mod,inst);resolve(mod.exports)})})}wasmBinaryFile??=findWasmBinary();try{var result=await instantiateAsync(wasmBinary,wasmBinaryFile,info);var exports=receiveInstantiationResult(result);return exports}catch(e){readyPromiseReject(e);return Promise.reject(e)}}function sapp_js_add_beforeunload_listener(){Module.sokol_beforeunload=event=>{if(__sapp_html5_get_ask_leave_site()!=0){event.preventDefault();event.returnValue=" "}};window.addEventListener("beforeunload",Module.sokol_beforeunload)}function sapp_js_remove_beforeunload_listener(){window.removeEventListener("beforeunload",Module.sokol_beforeunload)}function sapp_js_add_clipboard_listener(){Module.sokol_paste=event=>{const pasted_str=event.clipboardData.getData("text");withStackSave(()=>{const cstr=stringToUTF8OnStack(pasted_str);__sapp_emsc_onpaste(cstr)})};window.addEventListener("paste",Module.sokol_paste)}function sapp_js_remove_clipboard_listener(){window.removeEventListener("paste",Module.sokol_paste)}function sapp_js_write_clipboard(c_str){const str=UTF8ToString(c_str);const ta=document.createElement("textarea");ta.setAttribute("autocomplete","off");ta.setAttribute("autocorrect","off");ta.setAttribute("autocapitalize","off");ta.setAttribute("spellcheck","false");ta.style.left=-100+"px";ta.style.top=-100+"px";ta.style.height=1;ta.style.width=1;ta.value=str;document.body.appendChild(ta);ta.select();document.execCommand("copy");document.body.removeChild(ta)}function sapp_js_add_dragndrop_listeners(canvas_name_cstr){Module.sokol_drop_files=[];const canvas_name=UTF8ToString(canvas_name_cstr);const canvas=document.getElementById(canvas_name);Module.sokol_dragenter=event=>{event.stopPropagation();event.preventDefault()};Module.sokol_dragleave=event=>{event.stopPropagation();event.preventDefault()};Module.sokol_dragover=event=>{event.stopPropagation();event.preventDefault()};Module.sokol_drop=event=>{event.stopPropagation();event.preventDefault();const files=event.dataTransfer.files;Module.sokol_dropped_files=files;__sapp_emsc_begin_drop(files.length);for(let i=0;i<files.length;i++){withStackSave(()=>{const cstr=stringToUTF8OnStack(files[i].name);__sapp_emsc_drop(i,cstr)})}let mods=0;if(event.shiftKey){mods|=1}if(event.ctrlKey){mods|=2}if(event.altKey){mods|=4}if(event.metaKey){mods|=8}__sapp_emsc_end_drop(event.clientX,event.clientY,mods)};canvas.addEventListener("dragenter",Module.sokol_dragenter,false);canvas.addEventListener("dragleave",Module.sokol_dragleave,false);canvas.addEventListener("dragover",Module.sokol_dragover,false);canvas.addEventListener("drop",Module.sokol_drop,false)}function sapp_js_remove_dragndrop_listeners(canvas_name_cstr){const canvas_name=UTF8ToString(canvas_name_cstr);const canvas=document.getElementById(canvas_name);canvas.removeEventListener("dragenter",Module.sokol_dragenter);canvas.removeEventListener("dragleave",Module.sokol_dragleave);canvas.removeEventListener("dragover",Module.sokol_dragover);canvas.removeEventListener("drop",Module.sokol_drop)}function _sapp_emsc_set_input_rect(x,y,w,h){const fix_virtual_keyboard_offset=false;const input=Module.sapp_text;if(!input.pos){input.pos=[x,y]}input.pos[0]=x;input.pos[1]=y;input.style.pointerEvents="auto";if(!input.tick){input.tick=()=>{const user_agent=window.navigator.userAgent;const is_ios=user_agent.includes("iPhone")||user_agent.includes("iPad")||user_agent.includes("iPod");const is_safari=user_agent.includes("Safari");const canvas=Module.sapp_emsc_target;const box=canvas.getBoundingClientRect();const x=input.pos[0]+box.left;const y=input.pos[1]+box.top;input.style.left=`${x}px`;if(fix_virtual_keyboard_offset){input.style.top=`${y+canvas.offsetTop}px`}else{input.style.top=`${y}px`}requestAnimationFrame(input.tick)};input.tick()}}function sapp_js_init(c_str_target){const target_str=UTF8ToString(c_str_target);Module.sapp_emsc_target=document.getElementById(target_str);if(!Module.sapp_emsc_target){console.log("sokol_app.h: invalid target:"+target_str)}if(!Module.sapp_emsc_target.requestPointerLock){console.log("sokol_app.h: target doesn't support requestPointerLock:"+target_str)}const set_input_text=Module.cwrap("_sapp_emsc_input_text","void",["string"]);const set_input_text_composition=Module.cwrap("_sapp_emsc_input_text_composition","void",["number"]);const set_editing_text=Module.cwrap("_set_editing_text","void",["string","number","number"]);const input=document.createElement("input");input.setAttribute("id","input");input.setAttribute("type","text");input.setAttribute("autocapitalize","off");input.setAttribute("autocomplete","off");input.setAttribute("autocorrect","off");input.setAttribute("spellcheck","false");input.style.backgroundColor="transparent";input.style.border="none";input.style.outline="none";input.style.width="1px";input.style.height="1px";input.style.caretColor="transparent";input.style.position="fixed";input.style.left="0";input.style.top="0";input.style.zIndex="987654321";input.addEventListener("compositionstart",()=>{Module.sapp_is_comp=true;set_input_text_composition(1)});input.addEventListener("compositionupdate",ev=>{Module.sapp_comp_data=ev.data;set_editing_text(Module.sapp_comp_data,0,ev.data.length)});input.addEventListener("compositionend",()=>{Module.sapp_is_comp=false;set_input_text(Module.sapp_comp_data);Module.sapp_comp_data="";input.value="";set_input_text_composition(0)});input.addEventListener("beforeinput",ev=>{if(!Module.sapp_is_comp){if(ev.data){set_input_text(ev.data)}}});input.addEventListener("keydown",ev=>{if((ev.ctrlKey||ev.metaKey)&&["-","+","=","0","1","2"].includes(ev.key)){ev.preventDefault()}});document.body.appendChild(input);Module.sapp_text=input}function sapp_js_request_pointerlock(){if(Module.sapp_emsc_target){if(Module.sapp_emsc_target.requestPointerLock){Module.sapp_emsc_target.requestPointerLock()}}}function sapp_js_set_cursor(cursor_type,shown){if(Module.sapp_emsc_target){let cursor;if(shown===0){cursor="none"}else switch(cursor_type){case 0:cursor="auto";break;case 1:cursor="default";break;case 2:cursor="text";break;case 3:cursor="crosshair";break;case 4:cursor="pointer";break;case 5:cursor="ew-resize";break;case 6:cursor="ns-resize";break;case 7:cursor="nwse-resize";break;case 8:cursor="nesw-resize";break;case 9:cursor="all-scroll";break;case 10:cursor="not-allowed";break;default:cursor="auto";break}Module.sapp_emsc_target.style.cursor=cursor}}function sapp_js_clear_favicon(){const link=document.getElementById("sokol-app-favicon");if(link){document.head.removeChild(link)}}function sapp_js_set_favicon(w,h,pixels){const canvas=document.createElement("canvas");canvas.width=w;canvas.height=h;const ctx=canvas.getContext("2d");const img_data=ctx.createImageData(w,h);img_data.data.set(HEAPU8.subarray(pixels>>>0,pixels+w*h*4>>>0));ctx.putImageData(img_data,0,0);const new_link=document.createElement("link");new_link.id="sokol-app-favicon";new_link.rel="shortcut icon";new_link.href=canvas.toDataURL();document.head.appendChild(new_link)}function sapp_html_set_window_size(width,height){const w=Math.min(window.innerWidth,width);const h=Math.min(window.innerHeight,height);const canvas=Module.sapp_emsc_target;canvas.width=w*window.devicePixelRatio;canvas.height=h*window.devicePixelRatio;canvas.style.width=`${w}px`;canvas.style.height=`${h}px`}function html_is_osx(){if(navigator.userAgent.includes("Macintosh")){return 1}else{return 0}}function slog_js_log(level,c_str){const str=UTF8ToString(c_str);switch(level){case 0:console.error(str);break;case 1:console.error(str);break;case 2:console.warn(str);break;default:console.info(str);break}}function sfetch_js_send_head_request(slot_id,path_cstr){const path_str=UTF8ToString(path_cstr);const req=new XMLHttpRequest;req.open("HEAD",path_str);req.onreadystatechange=function(){if(req.readyState==XMLHttpRequest.DONE){if(req.status==200){const content_length=req.getResponseHeader("Content-Length");__sfetch_emsc_head_response(slot_id,content_length)}else{__sfetch_emsc_failed_http_status(slot_id,req.status)}}};req.send()}function sfetch_js_send_get_request(slot_id,path_cstr,offset,bytes_to_read,buf_ptr,buf_size){const path_str=UTF8ToString(path_cstr);const req=new XMLHttpRequest;req.open("GET",path_str);req.responseType="arraybuffer";const need_range_request=bytes_to_read>0;if(need_range_request){req.setRequestHeader("Range","bytes="+offset+"-"+(offset+bytes_to_read-1))}req.onreadystatechange=function(){if(req.readyState==XMLHttpRequest.DONE){if(req.status==206||req.status==200&&!need_range_request){const u8_array=new Uint8Array(req.response);const content_fetched_size=u8_array.length;if(content_fetched_size<=buf_size){HEAPU8.set(u8_array,buf_ptr>>>0);__sfetch_emsc_get_response(slot_id,bytes_to_read,content_fetched_size)}else{__sfetch_emsc_failed_buffer_too_small(slot_id)}}else{__sfetch_emsc_failed_http_status(slot_id,req.status)}}};req.send()}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.unshift(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.unshift(cb);var noExitRuntime=Module["noExitRuntime"]||true;var stackRestore=val=>__emscripten_stack_restore(val);var stackSave=()=>_emscripten_stack_get_current();var exceptionCaught=[];var uncaughtExceptionCount=0;function ___cxa_begin_catch(ptr){ptr>>>=0;var info=new ExceptionInfo(ptr);if(!info.get_caught()){info.set_caught(true);uncaughtExceptionCount--}info.set_rethrown(false);exceptionCaught.push(info);___cxa_increment_exception_refcount(ptr);return ___cxa_get_exception_ptr(ptr)}var exceptionLast=0;var ___cxa_end_catch=()=>{_setThrew(0,0);var info=exceptionCaught.pop();___cxa_decrement_exception_refcount(info.excPtr);exceptionLast=0};class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>>2>>>0]=type}get_type(){return HEAPU32[this.ptr+4>>>2>>>0]}set_destructor(destructor){HEAPU32[this.ptr+8>>>2>>>0]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>>2>>>0]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12>>>0]=caught}get_caught(){return HEAP8[this.ptr+12>>>0]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>>0]=rethrown}get_rethrown(){return HEAP8[this.ptr+13>>>0]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>>2>>>0]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>>2>>>0]}}function ___resumeException(ptr){ptr>>>=0;if(!exceptionLast){exceptionLast=ptr}throw exceptionLast}var setTempRet0=val=>__emscripten_tempret_set(val);var findMatchingCatch=args=>{var thrown=exceptionLast;if(!thrown){setTempRet0(0);return 0}var info=new ExceptionInfo(thrown);info.set_adjusted_ptr(thrown);var thrownType=info.get_type();if(!thrownType){setTempRet0(0);return thrown}for(var caughtType of args){if(caughtType===0||caughtType===thrownType){break}var adjusted_ptr_addr=info.ptr+16;if(___cxa_can_catch(caughtType,thrownType,adjusted_ptr_addr)){setTempRet0(caughtType);return thrown}}setTempRet0(thrownType);return thrown};function ___cxa_find_matching_catch_2(){return findMatchingCatch([])}function ___cxa_find_matching_catch_4(arg0,arg1){arg0>>>=0;arg1>>>=0;return findMatchingCatch([arg0,arg1])}function ___cxa_throw(ptr,type,destructor){ptr>>>=0;type>>>=0;destructor>>>=0;var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast}var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{outIdx>>>=0;if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++>>>0]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++>>>0]=192|u>>6;heap[outIdx++>>>0]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++>>>0]=224|u>>12;heap[outIdx++>>>0]=128|u>>6&63;heap[outIdx++>>>0]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++>>>0]=240|u>>18;heap[outIdx++>>>0]=128|u>>12&63;heap[outIdx++>>>0]=128|u>>6&63;heap[outIdx++>>>0]=128|u&63}}heap[outIdx>>>0]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);function ___syscall_getcwd(buf,size){buf>>>=0;size>>>=0}var __abort_js=()=>abort("");var _emscripten_set_main_loop_timing=(mode,value)=>{MainLoop.timingMode=mode;MainLoop.timingValue=value;if(!MainLoop.func){return 1}if(!MainLoop.running){MainLoop.running=true}if(mode==0){MainLoop.scheduler=function MainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,MainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(MainLoop.runner,timeUntilNextTick)};MainLoop.method="timeout"}else if(mode==1){MainLoop.scheduler=function MainLoop_scheduler_rAF(){MainLoop.requestAnimationFrame(MainLoop.runner)};MainLoop.method="rAF"}else if(mode==2){if(typeof MainLoop.setImmediate=="undefined"){if(typeof setImmediate=="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var MainLoop_setImmediate_messageHandler=event=>{if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",MainLoop_setImmediate_messageHandler,true);MainLoop.setImmediate=func=>{setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){Module["setImmediates"]??=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}else{MainLoop.setImmediate=setImmediate}}MainLoop.scheduler=function MainLoop_scheduler_setImmediate(){MainLoop.setImmediate(MainLoop.runner)};MainLoop.method="immediate"}return 0};var _emscripten_get_now=()=>performance.now();var runtimeKeepaliveCounter=0;var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var handleException=e=>{if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)};var maybeExit=()=>{if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}};var setMainLoop=(iterFunc,fps,simulateInfiniteLoop,arg,noSetTiming)=>{MainLoop.func=iterFunc;MainLoop.arg=arg;var thisMainLoopId=MainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<MainLoop.currentlyRunningMainloop){maybeExit();return false}return true}MainLoop.running=false;MainLoop.runner=function MainLoop_runner(){if(ABORT)return;if(MainLoop.queue.length>0){var start=Date.now();var blocker=MainLoop.queue.shift();blocker.func(blocker.arg);if(MainLoop.remainingBlockers){var remaining=MainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){MainLoop.remainingBlockers=next}else{next=next+.5;MainLoop.remainingBlockers=(8*remaining+next)/9}}MainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(MainLoop.runner,0);return}if(!checkIsRunning())return;MainLoop.currentFrameNumber=MainLoop.currentFrameNumber+1|0;if(MainLoop.timingMode==1&&MainLoop.timingValue>1&&MainLoop.currentFrameNumber%MainLoop.timingValue!=0){MainLoop.scheduler();return}else if(MainLoop.timingMode==0){MainLoop.tickStartTime=_emscripten_get_now()}MainLoop.runIter(iterFunc);if(!checkIsRunning())return;MainLoop.scheduler()};if(!noSetTiming){if(fps>0){_emscripten_set_main_loop_timing(0,1e3/fps)}else{_emscripten_set_main_loop_timing(1,1)}MainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}};var callUserCallback=func=>{if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}};var MainLoop={running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],preMainLoop:[],postMainLoop:[],pause(){MainLoop.scheduler=null;MainLoop.currentlyRunningMainloop++},resume(){MainLoop.currentlyRunningMainloop++;var timingMode=MainLoop.timingMode;var timingValue=MainLoop.timingValue;var func=MainLoop.func;MainLoop.func=null;setMainLoop(func,0,false,MainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);MainLoop.scheduler()},updateStatus(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=MainLoop.remainingBlockers??0;var expected=MainLoop.expectedBlockers??0;if(remaining){if(remaining<expected){Module["setStatus"](`{message} ({expected - remaining}/{expected})`)}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},init(){Module["preMainLoop"]&&MainLoop.preMainLoop.push(Module["preMainLoop"]);Module["postMainLoop"]&&MainLoop.postMainLoop.push(Module["postMainLoop"])},runIter(func){if(ABORT)return;for(var pre of MainLoop.preMainLoop){if(pre()===false){return}}callUserCallback(func);for(var post of MainLoop.postMainLoop){post()}},nextRAF:0,fakeRequestAnimationFrame(func){var now=Date.now();if(MainLoop.nextRAF===0){MainLoop.nextRAF=now+1e3/60}else{while(now+2>=MainLoop.nextRAF){MainLoop.nextRAF+=1e3/60}}var delay=Math.max(MainLoop.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame(func){if(typeof requestAnimationFrame=="function"){requestAnimationFrame(func);return}var RAF=MainLoop.fakeRequestAnimationFrame;RAF(func)}};var _emscripten_cancel_main_loop=()=>{MainLoop.pause();MainLoop.func=null};var JSEvents={memcpy(target,src,size){HEAP8.set(HEAP8.subarray(src>>>0,src+size>>>0),target>>>0)},removeAllEventListeners(){while(JSEvents.eventHandlers.length){JSEvents._removeHandler(JSEvents.eventHandlers.length-1)}JSEvents.deferredCalls=[]},inEventHandler:0,deferredCalls:[],deferCall(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var call of JSEvents.deferredCalls){if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction,precedence,argsList});JSEvents.deferredCalls.sort((x,y)=>x.precedence<y.precedence)},removeDeferredCalls(targetFunction){JSEvents.deferredCalls=JSEvents.deferredCalls.filter(call=>call.targetFunction!=targetFunction)},canPerformEventHandlerRequests(){if(navigator.userActivation){return navigator.userActivation.isActive}return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls(){if(!JSEvents.canPerformEventHandlerRequests()){return}var deferredCalls=JSEvents.deferredCalls;JSEvents.deferredCalls=[];for(var call of deferredCalls){call.targetFunction(...call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:(target,eventTypeString)=>{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler(eventHandler){if(!eventHandler.target){return-4}if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=function(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};eventHandler.target.addEventListener(eventHandler.eventTypeString,eventHandler.eventListenerFunc,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler)}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}return 0},getNodeNameForTarget(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target?.nodeName||""},fullscreenEnabled(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead=NaN)=>{idx>>>=0;var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>{ptr>>>=0;return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""};var maybeCStringToJsString=cString=>cString>2?UTF8ToString(cString):cString;var specialHTMLTargets=[0,typeof document!="undefined"?document:0,typeof window!="undefined"?window:0];var findEventTarget=target=>{target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||(typeof document!="undefined"?document.querySelector(target):null);return domElement};var findCanvasEventTarget=findEventTarget;function _emscripten_get_canvas_element_size(target,width,height){target>>>=0;width>>>=0;height>>>=0;var canvas=findCanvasEventTarget(target);if(!canvas)return-4;HEAP32[width>>>2>>>0]=canvas.width;HEAP32[height>>>2>>>0]=canvas.height}var _emscripten_get_device_pixel_ratio=()=>typeof devicePixelRatio=="number"&&devicePixelRatio||1;var getBoundingClientRect=e=>specialHTMLTargets.indexOf(e)<0?e.getBoundingClientRect():{left:0,top:0};function _emscripten_get_element_css_size(target,width,height){target>>>=0;width>>>=0;height>>>=0;target=findEventTarget(target);if(!target)return-4;var rect=getBoundingClientRect(target);HEAPF64[width>>>3>>>0]=rect.width;HEAPF64[height>>>3>>>0]=rect.height;return 0}var _emscripten_performance_now=()=>performance.now();var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};function _emscripten_request_animation_frame_loop(cb,userData){cb>>>=0;userData>>>=0;function tick(timeStamp){if(getWasmTableEntry(cb)(timeStamp,userData)){requestAnimationFrame(tick)}}return requestAnimationFrame(tick)}var getHeapMax=()=>4294901760;var alignMemory=(size,alignment)=>Math.ceil(size/alignment)*alignment;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536|0;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};function _emscripten_resize_heap(requestedSize){requestedSize>>>=0;var oldSize=HEAPU8.length;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignMemory(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false}var convertFrameToPC=frame=>{return 0};var jsStackTrace=()=>(new Error).stack.toString();function _emscripten_return_address(level){return ''}var registerFocusEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.focusEvent||=_malloc(256);var focusEventHandlerFunc=(e=event)=>{var nodeName=JSEvents.getNodeNameForTarget(e.target);var id=e.target.id?e.target.id:"";var focusEvent=JSEvents.focusEvent;stringToUTF8(nodeName,focusEvent+0,128);stringToUTF8(id,focusEvent+128,128);if(getWasmTableEntry(callbackfunc)(eventTypeId,focusEvent,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),eventTypeString,callbackfunc,handlerFunc:focusEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_blur_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerFocusEventCallback(target,userData,useCapture,callbackfunc,12,"blur",targetThread)}function _emscripten_set_canvas_element_size(target,width,height){target>>>=0;var canvas=findCanvasEventTarget(target);if(!canvas)return-4;canvas.width=width;canvas.height=height;return 0}function _emscripten_set_focus_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerFocusEventCallback(target,userData,useCapture,callbackfunc,13,"focus",targetThread)}var registerKeyEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.keyEvent||=_malloc(160);var keyEventHandlerFunc=e=>{var keyEventData=JSEvents.keyEvent;HEAPF64[keyEventData>>>3>>>0]=e.timeStamp;var idx=keyEventData>>>2;HEAP32[idx+2>>>0]=e.location;HEAP8[keyEventData+12>>>0]=e.ctrlKey;HEAP8[keyEventData+13>>>0]=e.shiftKey;HEAP8[keyEventData+14>>>0]=e.altKey;HEAP8[keyEventData+15>>>0]=e.metaKey;HEAP8[keyEventData+16>>>0]=e.repeat;HEAP32[idx+5>>>0]=e.charCode;HEAP32[idx+6>>>0]=e.keyCode;HEAP32[idx+7>>>0]=e.which;stringToUTF8(e.key||"",keyEventData+32,32);stringToUTF8(e.code||"",keyEventData+64,32);stringToUTF8(e.char||"",keyEventData+96,32);stringToUTF8(e.locale||"",keyEventData+128,32);if(getWasmTableEntry(callbackfunc)(eventTypeId,keyEventData,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),eventTypeString,callbackfunc,handlerFunc:keyEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_keydown_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerKeyEventCallback(target,userData,useCapture,callbackfunc,2,"keydown",targetThread)}function _emscripten_set_keypress_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerKeyEventCallback(target,userData,useCapture,callbackfunc,1,"keypress",targetThread)}function _emscripten_set_keyup_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerKeyEventCallback(target,userData,useCapture,callbackfunc,3,"keyup",targetThread)}function _emscripten_set_main_loop(func,fps,simulateInfiniteLoop){func>>>=0;var iterFunc=getWasmTableEntry(func);setMainLoop(iterFunc,fps,simulateInfiniteLoop)}var fillMouseEventData=(eventStruct,e,target)=>{HEAPF64[eventStruct>>>3>>>0]=e.timeStamp;var idx=eventStruct>>>2;HEAP32[idx+2>>>0]=e.screenX;HEAP32[idx+3>>>0]=e.screenY;HEAP32[idx+4>>>0]=e.clientX;HEAP32[idx+5>>>0]=e.clientY;HEAP8[eventStruct+24>>>0]=e.ctrlKey;HEAP8[eventStruct+25>>>0]=e.shiftKey;HEAP8[eventStruct+26>>>0]=e.altKey;HEAP8[eventStruct+27>>>0]=e.metaKey;HEAP16[idx*2+14>>>0]=e.button;HEAP16[idx*2+15>>>0]=e.buttons;HEAP32[idx+8>>>0]=e["movementX"];HEAP32[idx+9>>>0]=e["movementY"];var rect=getBoundingClientRect(target);HEAP32[idx+10>>>0]=e.clientX-(rect.left|0);HEAP32[idx+11>>>0]=e.clientY-(rect.top|0)};var registerMouseEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.mouseEvent||=_malloc(64);target=findEventTarget(target);var mouseEventHandlerFunc=(e=event)=>{fillMouseEventData(JSEvents.mouseEvent,e,target);if(getWasmTableEntry(callbackfunc)(eventTypeId,JSEvents.mouseEvent,userData))e.preventDefault()};var eventHandler={target,allowsDeferredCalls:eventTypeString!="mousemove"&&eventTypeString!="mouseenter"&&eventTypeString!="mouseleave",eventTypeString,callbackfunc,handlerFunc:mouseEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_mousedown_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerMouseEventCallback(target,userData,useCapture,callbackfunc,5,"mousedown",targetThread)}function _emscripten_set_mouseenter_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerMouseEventCallback(target,userData,useCapture,callbackfunc,33,"mouseenter",targetThread)}function _emscripten_set_mouseleave_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerMouseEventCallback(target,userData,useCapture,callbackfunc,34,"mouseleave",targetThread)}function _emscripten_set_mousemove_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerMouseEventCallback(target,userData,useCapture,callbackfunc,8,"mousemove",targetThread)}function _emscripten_set_mouseup_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerMouseEventCallback(target,userData,useCapture,callbackfunc,6,"mouseup",targetThread)}var fillPointerlockChangeEventData=eventStruct=>{var pointerLockElement=document.pointerLockElement||document.mozPointerLockElement||document.webkitPointerLockElement||document.msPointerLockElement;var isPointerlocked=!!pointerLockElement;HEAP8[eventStruct>>>0]=isPointerlocked;var nodeName=JSEvents.getNodeNameForTarget(pointerLockElement);var id=pointerLockElement?.id||"";stringToUTF8(nodeName,eventStruct+1,128);stringToUTF8(id,eventStruct+129,128)};var registerPointerlockChangeEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.pointerlockChangeEvent||=_malloc(257);var pointerlockChangeEventHandlerFunc=(e=event)=>{var pointerlockChangeEvent=JSEvents.pointerlockChangeEvent;fillPointerlockChangeEventData(pointerlockChangeEvent);if(getWasmTableEntry(callbackfunc)(eventTypeId,pointerlockChangeEvent,userData))e.preventDefault()};var eventHandler={target,eventTypeString,callbackfunc,handlerFunc:pointerlockChangeEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_pointerlockchange_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;if(!document||!document.body||!document.body.requestPointerLock&&!document.body.mozRequestPointerLock&&!document.body.webkitRequestPointerLock&&!document.body.msRequestPointerLock){return-1}target=findEventTarget(target);if(!target)return-4;registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"mozpointerlockchange",targetThread);registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"webkitpointerlockchange",targetThread);registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"mspointerlockchange",targetThread);return registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"pointerlockchange",targetThread)}var registerPointerlockErrorEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{var pointerlockErrorEventHandlerFunc=(e=event)=>{if(getWasmTableEntry(callbackfunc)(eventTypeId,0,userData))e.preventDefault()};var eventHandler={target,eventTypeString,callbackfunc,handlerFunc:pointerlockErrorEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_pointerlockerror_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;if(!document||!document.body.requestPointerLock&&!document.body.mozRequestPointerLock&&!document.body.webkitRequestPointerLock&&!document.body.msRequestPointerLock){return-1}target=findEventTarget(target);if(!target)return-4;registerPointerlockErrorEventCallback(target,userData,useCapture,callbackfunc,38,"mozpointerlockerror",targetThread);registerPointerlockErrorEventCallback(target,userData,useCapture,callbackfunc,38,"webkitpointerlockerror",targetThread);registerPointerlockErrorEventCallback(target,userData,useCapture,callbackfunc,38,"mspointerlockerror",targetThread);return registerPointerlockErrorEventCallback(target,userData,useCapture,callbackfunc,38,"pointerlockerror",targetThread)}var registerUiEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.uiEvent||=_malloc(36);target=findEventTarget(target);var uiEventHandlerFunc=(e=event)=>{if(e.target!=target){return}var b=document.body;if(!b){return}var uiEvent=JSEvents.uiEvent;HEAP32[uiEvent>>>2>>>0]=0;HEAP32[uiEvent+4>>>2>>>0]=b.clientWidth;HEAP32[uiEvent+8>>>2>>>0]=b.clientHeight;HEAP32[uiEvent+12>>>2>>>0]=innerWidth;HEAP32[uiEvent+16>>>2>>>0]=innerHeight;HEAP32[uiEvent+20>>>2>>>0]=outerWidth;HEAP32[uiEvent+24>>>2>>>0]=outerHeight;HEAP32[uiEvent+28>>>2>>>0]=pageXOffset|0;HEAP32[uiEvent+32>>>2>>>0]=pageYOffset|0;if(getWasmTableEntry(callbackfunc)(eventTypeId,uiEvent,userData))e.preventDefault()};var eventHandler={target,eventTypeString,callbackfunc,handlerFunc:uiEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_resize_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerUiEventCallback(target,userData,useCapture,callbackfunc,10,"resize",targetThread)}var registerTouchEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.touchEvent||=_malloc(1552);target=findEventTarget(target);var touchEventHandlerFunc=e=>{var t,touches={},et=e.touches;for(let t of et){t.isChanged=t.onTarget=0;touches[t.identifier]=t}for(let t of e.changedTouches){t.isChanged=1;touches[t.identifier]=t}for(let t of e.targetTouches){touches[t.identifier].onTarget=1}var touchEvent=JSEvents.touchEvent;HEAPF64[touchEvent>>>3>>>0]=e.timeStamp;HEAP8[touchEvent+12>>>0]=e.ctrlKey;HEAP8[touchEvent+13>>>0]=e.shiftKey;HEAP8[touchEvent+14>>>0]=e.altKey;HEAP8[touchEvent+15>>>0]=e.metaKey;var idx=touchEvent+16;var targetRect=getBoundingClientRect(target);var numTouches=0;for(let t of Object.values(touches)){var idx32=idx>>>2;HEAP32[idx32+0>>>0]=t.identifier;HEAP32[idx32+1>>>0]=t.screenX;HEAP32[idx32+2>>>0]=t.screenY;HEAP32[idx32+3>>>0]=t.clientX;HEAP32[idx32+4>>>0]=t.clientY;HEAP32[idx32+5>>>0]=t.pageX;HEAP32[idx32+6>>>0]=t.pageY;HEAP8[idx+28>>>0]=t.isChanged;HEAP8[idx+29>>>0]=t.onTarget;HEAP32[idx32+8>>>0]=t.clientX-(targetRect.left|0);HEAP32[idx32+9>>>0]=t.clientY-(targetRect.top|0);idx+=48;if(++numTouches>31){break}}HEAP32[touchEvent+8>>>2>>>0]=numTouches;if(getWasmTableEntry(callbackfunc)(eventTypeId,touchEvent,userData))e.preventDefault()};var eventHandler={target,allowsDeferredCalls:eventTypeString=="touchstart"||eventTypeString=="touchend",eventTypeString,callbackfunc,handlerFunc:touchEventHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_touchcancel_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerTouchEventCallback(target,userData,useCapture,callbackfunc,25,"touchcancel",targetThread)}function _emscripten_set_touchend_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerTouchEventCallback(target,userData,useCapture,callbackfunc,23,"touchend",targetThread)}function _emscripten_set_touchmove_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerTouchEventCallback(target,userData,useCapture,callbackfunc,24,"touchmove",targetThread)}function _emscripten_set_touchstart_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;return registerTouchEventCallback(target,userData,useCapture,callbackfunc,22,"touchstart",targetThread)}var GLctx;var webgl_enable_ANGLE_instanced_arrays=ctx=>{var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=(index,divisor)=>ext["vertexAttribDivisorANGLE"](index,divisor);ctx["drawArraysInstanced"]=(mode,first,count,primcount)=>ext["drawArraysInstancedANGLE"](mode,first,count,primcount);ctx["drawElementsInstanced"]=(mode,count,type,indices,primcount)=>ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount);return 1}};var webgl_enable_OES_vertex_array_object=ctx=>{var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=()=>ext["createVertexArrayOES"]();ctx["deleteVertexArray"]=vao=>ext["deleteVertexArrayOES"](vao);ctx["bindVertexArray"]=vao=>ext["bindVertexArrayOES"](vao);ctx["isVertexArray"]=vao=>ext["isVertexArrayOES"](vao);return 1}};var webgl_enable_WEBGL_draw_buffers=ctx=>{var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=(n,bufs)=>ext["drawBuffersWEBGL"](n,bufs);return 1}};var webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance=ctx=>!!(ctx.dibvbi=ctx.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"));var webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance=ctx=>!!(ctx.mdibvbi=ctx.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"));var webgl_enable_EXT_polygon_offset_clamp=ctx=>!!(ctx.extPolygonOffsetClamp=ctx.getExtension("EXT_polygon_offset_clamp"));var webgl_enable_EXT_clip_control=ctx=>!!(ctx.extClipControl=ctx.getExtension("EXT_clip_control"));var webgl_enable_WEBGL_polygon_mode=ctx=>!!(ctx.webglPolygonMode=ctx.getExtension("WEBGL_polygon_mode"));var webgl_enable_WEBGL_multi_draw=ctx=>!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"));var getEmscriptenSupportedExtensions=ctx=>{var supportedExtensions=["ANGLE_instanced_arrays","EXT_blend_minmax","EXT_disjoint_timer_query","EXT_frag_depth","EXT_shader_texture_lod","EXT_sRGB","OES_element_index_uint","OES_fbo_render_mipmap","OES_standard_derivatives","OES_texture_float","OES_texture_half_float","OES_texture_half_float_linear","OES_vertex_array_object","WEBGL_color_buffer_float","WEBGL_depth_texture","WEBGL_draw_buffers","EXT_color_buffer_float","EXT_conservative_depth","EXT_disjoint_timer_query_webgl2","EXT_texture_norm16","NV_shader_noperspective_interpolation","WEBGL_clip_cull_distance","EXT_clip_control","EXT_color_buffer_half_float","EXT_depth_clamp","EXT_float_blend","EXT_polygon_offset_clamp","EXT_texture_compression_bptc","EXT_texture_compression_rgtc","EXT_texture_filter_anisotropic","KHR_parallel_shader_compile","OES_texture_float_linear","WEBGL_blend_func_extended","WEBGL_compressed_texture_astc","WEBGL_compressed_texture_etc","WEBGL_compressed_texture_etc1","WEBGL_compressed_texture_s3tc","WEBGL_compressed_texture_s3tc_srgb","WEBGL_debug_renderer_info","WEBGL_debug_shaders","WEBGL_lose_context","WEBGL_multi_draw","WEBGL_polygon_mode"];return(ctx.getSupportedExtensions()||[]).filter(ext=>supportedExtensions.includes(ext))};var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],stringCache:{},stringiCache:{},unpackAlignment:4,unpackRowLength:0,recordError:errorCode=>{if(!GL.lastError){GL.lastError=errorCode}},getNewId:table=>{var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},genObject:(n,buffers,createFunction,objectTable)=>{for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}HEAP32[buffers+i*4>>>2>>>0]=id}},getSource:(shader,count,string,length)=>{var source="";for(var i=0;i<count;++i){var len=length?HEAPU32[length+i*4>>>2>>>0]:undefined;source+=UTF8ToString(HEAPU32[string+i*4>>>2>>>0],len)}return source},createContext:(canvas,webGLContextAttributes)=>{if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;function fixedGetContext(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}canvas.getContext=fixedGetContext}var ctx=webGLContextAttributes.majorVersion>1?canvas.getContext("webgl2",webGLContextAttributes):canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:(ctx,webGLContextAttributes)=>{var handle=GL.getNewId(GL.contexts);var context={handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault=="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:contextHandle=>{GL.currentContext=GL.contexts[contextHandle];Module["ctx"]=GLctx=GL.currentContext?.GLctx;return!(contextHandle&&!GLctx)},getContext:contextHandle=>GL.contexts[contextHandle],deleteContext:contextHandle=>{if(GL.currentContext===GL.contexts[contextHandle]){GL.currentContext=null}if(typeof JSEvents=="object"){JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas)}if(GL.contexts[contextHandle]?.GLctx.canvas){GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined}GL.contexts[contextHandle]=null},initExtensions:context=>{context||=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;webgl_enable_WEBGL_multi_draw(GLctx);webgl_enable_EXT_polygon_offset_clamp(GLctx);webgl_enable_EXT_clip_control(GLctx);webgl_enable_WEBGL_polygon_mode(GLctx);webgl_enable_ANGLE_instanced_arrays(GLctx);webgl_enable_OES_vertex_array_object(GLctx);webgl_enable_WEBGL_draw_buffers(GLctx);webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx);webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx);if(context.version>=2){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query_webgl2")}if(context.version<2||!GLctx.disjointTimerQueryExt){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}getEmscriptenSupportedExtensions(GLctx).forEach(ext=>{if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};var registerWebGlEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{var webGlEventHandlerFunc=(e=event)=>{if(getWasmTableEntry(callbackfunc)(eventTypeId,0,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),eventTypeString,callbackfunc,handlerFunc:webGlEventHandlerFunc,useCapture};JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_webglcontextlost_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;registerWebGlEventCallback(target,userData,useCapture,callbackfunc,31,"webglcontextlost",targetThread);return 0}function _emscripten_set_webglcontextrestored_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;registerWebGlEventCallback(target,userData,useCapture,callbackfunc,32,"webglcontextrestored",targetThread);return 0}var registerWheelEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{JSEvents.wheelEvent||=_malloc(96);var wheelHandlerFunc=(e=event)=>{var wheelEvent=JSEvents.wheelEvent;fillMouseEventData(wheelEvent,e,target);HEAPF64[wheelEvent+64>>>3>>>0]=e["deltaX"];HEAPF64[wheelEvent+72>>>3>>>0]=e["deltaY"];HEAPF64[wheelEvent+80>>>3>>>0]=e["deltaZ"];HEAP32[wheelEvent+88>>>2>>>0]=e["deltaMode"];if(getWasmTableEntry(callbackfunc)(eventTypeId,wheelEvent,userData))e.preventDefault()};var eventHandler={target,allowsDeferredCalls:true,eventTypeString,callbackfunc,handlerFunc:wheelHandlerFunc,useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};function _emscripten_set_wheel_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){target>>>=0;userData>>>=0;callbackfunc>>>=0;targetThread>>>=0;target=findEventTarget(target);if(!target)return-4;if(typeof target.onwheel!="undefined"){return registerWheelEventCallback(target,userData,useCapture,callbackfunc,9,"wheel",targetThread)}else{return-1}}var webglPowerPreferences=["default","low-power","high-performance"];function _emscripten_webgl_do_create_context(target,attributes){target>>>=0;attributes>>>=0;var attr32=attributes>>>2;var powerPreference=HEAP32[attr32+(8>>2)>>>0];var contextAttributes={alpha:!!HEAP8[attributes+0>>>0],depth:!!HEAP8[attributes+1>>>0],stencil:!!HEAP8[attributes+2>>>0],antialias:!!HEAP8[attributes+3>>>0],premultipliedAlpha:!!HEAP8[attributes+4>>>0],preserveDrawingBuffer:!!HEAP8[attributes+5>>>0],powerPreference:webglPowerPreferences[powerPreference],failIfMajorPerformanceCaveat:!!HEAP8[attributes+12>>>0],majorVersion:HEAP32[attr32+(16>>2)>>>0],minorVersion:HEAP32[attr32+(20>>2)>>>0],enableExtensionsByDefault:HEAP8[attributes+24>>>0],explicitSwapControl:HEAP8[attributes+25>>>0],proxyContextToMainThread:HEAP32[attr32+(28>>2)>>>0],renderViaOffscreenBackBuffer:HEAP8[attributes+32>>>0]};var canvas=findCanvasEventTarget(target);if(!canvas){return 0}if(contextAttributes.explicitSwapControl){return 0}var contextHandle=GL.createContext(canvas,contextAttributes);return contextHandle}var _emscripten_webgl_create_context=_emscripten_webgl_do_create_context;function _emscripten_webgl_enable_extension(contextHandle,extension){contextHandle>>>=0;extension>>>=0;var context=GL.getContext(contextHandle);var extString=UTF8ToString(extension);if(extString.startsWith("GL_"))extString=extString.slice(3);if(extString=="ANGLE_instanced_arrays")webgl_enable_ANGLE_instanced_arrays(GLctx);if(extString=="OES_vertex_array_object")webgl_enable_OES_vertex_array_object(GLctx);if(extString=="WEBGL_draw_buffers")webgl_enable_WEBGL_draw_buffers(GLctx);if(extString=="WEBGL_draw_instanced_base_vertex_base_instance")webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx);if(extString=="WEBGL_multi_draw_instanced_base_vertex_base_instance")webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx);if(extString=="WEBGL_multi_draw")webgl_enable_WEBGL_multi_draw(GLctx);if(extString=="EXT_polygon_offset_clamp")webgl_enable_EXT_polygon_offset_clamp(GLctx);if(extString=="EXT_clip_control")webgl_enable_EXT_clip_control(GLctx);if(extString=="WEBGL_polygon_mode")webgl_enable_WEBGL_polygon_mode(GLctx);var ext=context.GLctx.getExtension(extString);return!!ext}function _emscripten_webgl_make_context_current(contextHandle){contextHandle>>>=0;var success=GL.makeContextCurrent(contextHandle);return success?0:-5}var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>>0]=str.charCodeAt(i)}HEAP8[buffer>>>0]=0};var _environ_get=function(__environ,environ_buf){__environ>>>=0;environ_buf>>>=0;var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>>2>>>0]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=function(penviron_count,penviron_buf_size){penviron_count>>>=0;penviron_buf_size>>>=0;var strings=getEnvStrings();HEAPU32[penviron_count>>>2>>>0]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>>2>>>0]=bufSize;return 0};var printCharBuffers=[null,[],[]];var printChar=(stream,curr)=>{var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer));buffer.length=0}else{buffer.push(curr)}};function _fd_write(fd,iov,iovcnt,pnum){iov>>>=0;iovcnt>>>=0;pnum>>>=0;var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>>2>>>0];var len=HEAPU32[iov+4>>>2>>>0];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j>>>0])}num+=len}HEAPU32[pnum>>>2>>>0]=num;return 0}var _glActiveTexture=x0=>GLctx.activeTexture(x0);var _glAttachShader=(program,shader)=>{GLctx.attachShader(GL.programs[program],GL.shaders[shader])};var _glBindBuffer=(target,buffer)=>{if(target==35051){GLctx.currentPixelPackBufferBinding=buffer}else if(target==35052){GLctx.currentPixelUnpackBufferBinding=buffer}GLctx.bindBuffer(target,GL.buffers[buffer])};var _glBindBufferBase=(target,index,buffer)=>{GLctx.bindBufferBase(target,index,GL.buffers[buffer])};var _glBindFramebuffer=(target,framebuffer)=>{GLctx.bindFramebuffer(target,GL.framebuffers[framebuffer])};var _glBindRenderbuffer=(target,renderbuffer)=>{GLctx.bindRenderbuffer(target,GL.renderbuffers[renderbuffer])};var _glBindSampler=(unit,sampler)=>{GLctx.bindSampler(unit,GL.samplers[sampler])};var _glBindTexture=(target,texture)=>{GLctx.bindTexture(target,GL.textures[texture])};var _glBindVertexArray=vao=>{GLctx.bindVertexArray(GL.vaos[vao])};var _glBlendColor=(x0,x1,x2,x3)=>GLctx.blendColor(x0,x1,x2,x3);var _glBlendEquationSeparate=(x0,x1)=>GLctx.blendEquationSeparate(x0,x1);var _glBlendFuncSeparate=(x0,x1,x2,x3)=>GLctx.blendFuncSeparate(x0,x1,x2,x3);var _glBlitFramebuffer=(x0,x1,x2,x3,x4,x5,x6,x7,x8,x9)=>GLctx.blitFramebuffer(x0,x1,x2,x3,x4,x5,x6,x7,x8,x9);function _glBufferData(target,size,data,usage){size>>>=0;data>>>=0;GLctx.bufferData(target,data?HEAPU8.subarray(data>>>0,data+size>>>0):size,usage)}function _glBufferSubData(target,offset,size,data){offset>>>=0;size>>>=0;data>>>=0;GLctx.bufferSubData(target,offset,HEAPU8.subarray(data>>>0,data+size>>>0))}var _glCheckFramebufferStatus=x0=>GLctx.checkFramebufferStatus(x0);var _glClearBufferfi=(x0,x1,x2,x3)=>GLctx.clearBufferfi(x0,x1,x2,x3);function _glClearBufferfv(buffer,drawbuffer,value){value>>>=0;GLctx.clearBufferfv(buffer,drawbuffer,HEAPF32,value>>>2)}function _glClearBufferiv(buffer,drawbuffer,value){value>>>=0;GLctx.clearBufferiv(buffer,drawbuffer,HEAP32,value>>>2)}var _glColorMask=(red,green,blue,alpha)=>{GLctx.colorMask(!!red,!!green,!!blue,!!alpha)};var _glCompileShader=shader=>{GLctx.compileShader(GL.shaders[shader])};function _glCompressedTexImage2D(target,level,internalFormat,width,height,border,imageSize,data){data>>>=0;if(GL.currentContext.version>=2){if(GLctx.currentPixelUnpackBufferBinding||!imageSize){GLctx.compressedTexImage2D(target,level,internalFormat,width,height,border,imageSize,data);return}}GLctx.compressedTexImage2D(target,level,internalFormat,width,height,border,HEAPU8.subarray(data>>>0,data+imageSize>>>0))}function _glCompressedTexImage3D(target,level,internalFormat,width,height,depth,border,imageSize,data){data>>>=0;if(GLctx.currentPixelUnpackBufferBinding){GLctx.compressedTexImage3D(target,level,internalFormat,width,height,depth,border,imageSize,data)}else{GLctx.compressedTexImage3D(target,level,internalFormat,width,height,depth,border,HEAPU8,data,imageSize)}}var _glCreateProgram=()=>{var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id};var _glCreateShader=shaderType=>{var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id};var _glCullFace=x0=>GLctx.cullFace(x0);function _glDeleteBuffers(n,buffers){buffers>>>=0;for(var i=0;i<n;i++){var id=HEAP32[buffers+i*4>>>2>>>0];var buffer=GL.buffers[id];if(!buffer)continue;GLctx.deleteBuffer(buffer);buffer.name=0;GL.buffers[id]=null;if(id==GLctx.currentPixelPackBufferBinding)GLctx.currentPixelPackBufferBinding=0;if(id==GLctx.currentPixelUnpackBufferBinding)GLctx.currentPixelUnpackBufferBinding=0}}function _glDeleteFramebuffers(n,framebuffers){framebuffers>>>=0;for(var i=0;i<n;++i){var id=HEAP32[framebuffers+i*4>>>2>>>0];var framebuffer=GL.framebuffers[id];if(!framebuffer)continue;GLctx.deleteFramebuffer(framebuffer);framebuffer.name=0;GL.framebuffers[id]=null}}var _glDeleteProgram=id=>{if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null};function _glDeleteRenderbuffers(n,renderbuffers){renderbuffers>>>=0;for(var i=0;i<n;i++){var id=HEAP32[renderbuffers+i*4>>>2>>>0];var renderbuffer=GL.renderbuffers[id];if(!renderbuffer)continue;GLctx.deleteRenderbuffer(renderbuffer);renderbuffer.name=0;GL.renderbuffers[id]=null}}function _glDeleteSamplers(n,samplers){samplers>>>=0;for(var i=0;i<n;i++){var id=HEAP32[samplers+i*4>>>2>>>0];var sampler=GL.samplers[id];if(!sampler)continue;GLctx.deleteSampler(sampler);sampler.name=0;GL.samplers[id]=null}}var _glDeleteShader=id=>{if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null};function _glDeleteTextures(n,textures){textures>>>=0;for(var i=0;i<n;i++){var id=HEAP32[textures+i*4>>>2>>>0];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}}function _glDeleteVertexArrays(n,vaos){vaos>>>=0;for(var i=0;i<n;i++){var id=HEAP32[vaos+i*4>>>2>>>0];GLctx.deleteVertexArray(GL.vaos[id]);GL.vaos[id]=null}}var _glDepthFunc=x0=>GLctx.depthFunc(x0);var _glDepthMask=flag=>{GLctx.depthMask(!!flag)};var _glDisable=x0=>GLctx.disable(x0);var _glDisableVertexAttribArray=index=>{GLctx.disableVertexAttribArray(index)};var _glDrawArrays=(mode,first,count)=>{GLctx.drawArrays(mode,first,count)};var _glDrawArraysInstanced=(mode,first,count,primcount)=>{GLctx.drawArraysInstanced(mode,first,count,primcount)};var tempFixedLengthArray=[];function _glDrawBuffers(n,bufs){bufs>>>=0;var bufArray=tempFixedLengthArray[n];for(var i=0;i<n;i++){bufArray[i]=HEAP32[bufs+i*4>>>2>>>0]}GLctx.drawBuffers(bufArray)}function _glDrawElements(mode,count,type,indices){indices>>>=0;GLctx.drawElements(mode,count,type,indices)}function _glDrawElementsInstanced(mode,count,type,indices,primcount){indices>>>=0;GLctx.drawElementsInstanced(mode,count,type,indices,primcount)}var _glEnable=x0=>GLctx.enable(x0);var _glEnableVertexAttribArray=index=>{GLctx.enableVertexAttribArray(index)};var _glFramebufferRenderbuffer=(target,attachment,renderbuffertarget,renderbuffer)=>{GLctx.framebufferRenderbuffer(target,attachment,renderbuffertarget,GL.renderbuffers[renderbuffer])};var _glFramebufferTexture2D=(target,attachment,textarget,texture,level)=>{GLctx.framebufferTexture2D(target,attachment,textarget,GL.textures[texture],level)};var _glFramebufferTextureLayer=(target,attachment,texture,level,layer)=>{GLctx.framebufferTextureLayer(target,attachment,GL.textures[texture],level,layer)};var _glFrontFace=x0=>GLctx.frontFace(x0);function _glGenBuffers(n,buffers){buffers>>>=0;GL.genObject(n,buffers,"createBuffer",GL.buffers)}function _glGenFramebuffers(n,ids){ids>>>=0;GL.genObject(n,ids,"createFramebuffer",GL.framebuffers)}function _glGenRenderbuffers(n,renderbuffers){renderbuffers>>>=0;GL.genObject(n,renderbuffers,"createRenderbuffer",GL.renderbuffers)}function _glGenSamplers(n,samplers){samplers>>>=0;GL.genObject(n,samplers,"createSampler",GL.samplers)}function _glGenTextures(n,textures){textures>>>=0;GL.genObject(n,textures,"createTexture",GL.textures)}function _glGenVertexArrays(n,arrays){arrays>>>=0;GL.genObject(n,arrays,"createVertexArray",GL.vaos)}function _glGetAttribLocation(program,name){name>>>=0;return GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name))}var writeI53ToI64=(ptr,num)=>{HEAPU32[ptr>>>2>>>0]=num;var lower=HEAPU32[ptr>>>2>>>0];HEAPU32[ptr+4>>>2>>>0]=(num-lower)/4294967296};var webglGetExtensions=()=>{var exts=getEmscriptenSupportedExtensions(GLctx);exts=exts.concat(exts.map(e=>"GL_"+e));return exts};var emscriptenWebGLGet=(name_,p,type)=>{if(!p){GL.recordError(1281);return}var ret=undefined;switch(name_){case 36346:ret=1;break;case 36344:if(type!=0&&type!=1){GL.recordError(1280)}return;case 34814:case 36345:ret=0;break;case 34466:var formats=GLctx.getParameter(34467);ret=formats?formats.length:0;break;case 33309:if(GL.currentContext.version<2){GL.recordError(1282);return}ret=webglGetExtensions().length;break;case 33307:case 33308:if(GL.currentContext.version<2){GL.recordError(1280);return}ret=name_==33307?3:0;break}if(ret===undefined){var result=GLctx.getParameter(name_);switch(typeof result){case"number":ret=result;break;case"boolean":ret=result?1:0;break;case"string":GL.recordError(1280);return;case"object":if(result===null){switch(name_){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:{ret=0;break}default:{GL.recordError(1280);return}}}else if(result instanceof Float32Array||result instanceof Uint32Array||result instanceof Int32Array||result instanceof Array){for(var i=0;i<result.length;++i){switch(type){case 0:HEAP32[p+i*4>>>2>>>0]=result[i];break;case 2:HEAPF32[p+i*4>>>2>>>0]=result[i];break;case 4:HEAP8[p+i>>>0]=result[i]?1:0;break}}return}else{try{ret=result.name|0}catch(e){GL.recordError(1280);err(`GL_INVALID_ENUM in glGet${type}v: Unknown object returned from WebGL getParameter(${name_})! (error: ${e})`);return}}break;default:GL.recordError(1280);err(`GL_INVALID_ENUM in glGet${type}v: Native code calling glGet${type}v(${name_}) and it returns ${result} of type ${typeof result}!`);return}}switch(type){case 1:writeI53ToI64(p,ret);break;case 0:HEAP32[p>>>2>>>0]=ret;break;case 2:HEAPF32[p>>>2>>>0]=ret;break;case 4:HEAP8[p>>>0]=ret?1:0;break}};function _glGetIntegerv(name_,p){p>>>=0;return emscriptenWebGLGet(name_,p,0)}function _glGetProgramInfoLog(program,maxLength,length,infoLog){length>>>=0;infoLog>>>=0;var log=GLctx.getProgramInfoLog(GL.programs[program]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>>2>>>0]=numBytesWrittenExclNull}function _glGetProgramiv(program,pname,p){p>>>=0;if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";HEAP32[p>>>2>>>0]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){var numActiveUniforms=GLctx.getProgramParameter(program,35718);for(var i=0;i<numActiveUniforms;++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}HEAP32[p>>>2>>>0]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){var numActiveAttributes=GLctx.getProgramParameter(program,35721);for(var i=0;i<numActiveAttributes;++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}HEAP32[p>>>2>>>0]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){var numActiveUniformBlocks=GLctx.getProgramParameter(program,35382);for(var i=0;i<numActiveUniformBlocks;++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}HEAP32[p>>>2>>>0]=program.maxUniformBlockNameLength}else{HEAP32[p>>>2>>>0]=GLctx.getProgramParameter(program,pname)}}function _glGetShaderInfoLog(shader,maxLength,length,infoLog){length>>>=0;infoLog>>>=0;var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>>2>>>0]=numBytesWrittenExclNull}function _glGetShaderiv(shader,pname,p){p>>>=0;if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;HEAP32[p>>>2>>>0]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;HEAP32[p>>>2>>>0]=sourceLength}else{HEAP32[p>>>2>>>0]=GLctx.getShaderParameter(GL.shaders[shader],pname)}}var stringToNewUTF8=str=>{var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret};function _glGetStringi(name,index){if(GL.currentContext.version<2){GL.recordError(1282);return 0}var stringiCache=GL.stringiCache[name];if(stringiCache){if(index<0||index>=stringiCache.length){GL.recordError(1281);return 0}return stringiCache[index]}switch(name){case 7939:var exts=webglGetExtensions().map(stringToNewUTF8);stringiCache=GL.stringiCache[name]=exts;if(index<0||index>=stringiCache.length){GL.recordError(1281);return 0}return stringiCache[index];default:GL.recordError(1280);return 0}}var jstoi_q=str=>parseInt(str);var webglGetLeftBracePos=name=>name.slice(-1)=="]"&&name.lastIndexOf("[");var webglPrepareUniformLocationsBeforeFirstUse=program=>{var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};var numActiveUniforms=GLctx.getProgramParameter(program,35718);for(i=0;i<numActiveUniforms;++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}};function _glGetUniformLocation(program,name){name>>>=0;name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1}function _glInvalidateFramebuffer(target,numAttachments,attachments){attachments>>>=0;var list=tempFixedLengthArray[numAttachments];for(var i=0;i<numAttachments;i++){list[i]=HEAP32[attachments+i*4>>>2>>>0]}GLctx.invalidateFramebuffer(target,list)}var _glLinkProgram=program=>{program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}};var _glPixelStorei=(pname,param)=>{if(pname==3317){GL.unpackAlignment=param}else if(pname==3314){GL.unpackRowLength=param}GLctx.pixelStorei(pname,param)};var _glPolygonOffset=(x0,x1)=>GLctx.polygonOffset(x0,x1);var _glReadBuffer=x0=>GLctx.readBuffer(x0);var computeUnpackAlignedImageSize=(width,height,sizePerPixel)=>{function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=(GL.unpackRowLength||width)*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,GL.unpackAlignment);return height*alignedRowSize};var colorChannelsInGlTextureFormat=format=>{var colorChannels={5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4};return colorChannels[format-6402]||1};var heapObjectForWebGLType=type=>{type-=5120;if(type==0)return HEAP8;if(type==1)return HEAPU8;if(type==2)return HEAP16;if(type==4)return HEAP32;if(type==6)return HEAPF32;if(type==5||type==28922||type==28520||type==30779||type==30782)return HEAPU32;return HEAPU16};var toTypedArrayIndex=(pointer,heap)=>pointer>>>31-Math.clz32(heap.BYTES_PER_ELEMENT);var emscriptenWebGLGetTexPixelData=(type,format,width,height,pixels,internalFormat)=>{var heap=heapObjectForWebGLType(type);var sizePerPixel=colorChannelsInGlTextureFormat(format)*heap.BYTES_PER_ELEMENT;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel);return heap.subarray(toTypedArrayIndex(pixels,heap)>>>0,toTypedArrayIndex(pixels+bytes,heap)>>>0)};function _glReadPixels(x,y,width,height,format,type,pixels){pixels>>>=0;if(GL.currentContext.version>=2){if(GLctx.currentPixelPackBufferBinding){GLctx.readPixels(x,y,width,height,format,type,pixels);return}}var pixelData=emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,format);if(!pixelData){GL.recordError(1280);return}GLctx.readPixels(x,y,width,height,format,type,pixelData)}var _glRenderbufferStorageMultisample=(x0,x1,x2,x3,x4)=>GLctx.renderbufferStorageMultisample(x0,x1,x2,x3,x4);var _glSamplerParameterf=(sampler,pname,param)=>{GLctx.samplerParameterf(GL.samplers[sampler],pname,param)};var _glSamplerParameteri=(sampler,pname,param)=>{GLctx.samplerParameteri(GL.samplers[sampler],pname,param)};var _glScissor=(x0,x1,x2,x3)=>GLctx.scissor(x0,x1,x2,x3);function _glShaderSource(shader,count,string,length){string>>>=0;length>>>=0;var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)}var _glStencilFunc=(x0,x1,x2)=>GLctx.stencilFunc(x0,x1,x2);var _glStencilFuncSeparate=(x0,x1,x2,x3)=>GLctx.stencilFuncSeparate(x0,x1,x2,x3);var _glStencilMask=x0=>GLctx.stencilMask(x0);var _glStencilOp=(x0,x1,x2)=>GLctx.stencilOp(x0,x1,x2);var _glStencilOpSeparate=(x0,x1,x2,x3)=>GLctx.stencilOpSeparate(x0,x1,x2,x3);function _glTexImage2D(target,level,internalFormat,width,height,border,format,type,pixels){pixels>>>=0;if(GL.currentContext.version>=2){if(GLctx.currentPixelUnpackBufferBinding){GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels);return}}var pixelData=pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null;GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixelData)}function _glTexImage3D(target,level,internalFormat,width,height,depth,border,format,type,pixels){pixels>>>=0;if(GLctx.currentPixelUnpackBufferBinding){GLctx.texImage3D(target,level,internalFormat,width,height,depth,border,format,type,pixels)}else if(pixels){var heap=heapObjectForWebGLType(type);var pixelData=emscriptenWebGLGetTexPixelData(type,format,width,height*depth,pixels,internalFormat);GLctx.texImage3D(target,level,internalFormat,width,height,depth,border,format,type,pixelData)}else{GLctx.texImage3D(target,level,internalFormat,width,height,depth,border,format,type,null)}}var _glTexParameteri=(x0,x1,x2)=>GLctx.texParameteri(x0,x1,x2);var _glTexStorage2D=(x0,x1,x2,x3,x4)=>GLctx.texStorage2D(x0,x1,x2,x3,x4);var _glTexStorage3D=(x0,x1,x2,x3,x4,x5)=>GLctx.texStorage3D(x0,x1,x2,x3,x4,x5);function _glTexSubImage2D(target,level,xoffset,yoffset,width,height,format,type,pixels){pixels>>>=0;if(GL.currentContext.version>=2){if(GLctx.currentPixelUnpackBufferBinding){GLctx.texSubImage2D(target,level,xoffset,yoffset,width,height,format,type,pixels);return}}var pixelData=pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,0):null;GLctx.texSubImage2D(target,level,xoffset,yoffset,width,height,format,type,pixelData)}function _glTexSubImage3D(target,level,xoffset,yoffset,zoffset,width,height,depth,format,type,pixels){pixels>>>=0;if(GLctx.currentPixelUnpackBufferBinding){GLctx.texSubImage3D(target,level,xoffset,yoffset,zoffset,width,height,depth,format,type,pixels)}else if(pixels){var heap=heapObjectForWebGLType(type);GLctx.texSubImage3D(target,level,xoffset,yoffset,zoffset,width,height,depth,format,type,heap,toTypedArrayIndex(pixels,heap))}else{GLctx.texSubImage3D(target,level,xoffset,yoffset,zoffset,width,height,depth,format,type,null)}}var webglGetUniformLocation=location=>{var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc=="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?`[${webglLoc}]`:""))}return webglLoc}else{GL.recordError(1282)}};var miniTempWebGLFloatBuffers=[];function _glUniform1fv(location,count,value){value>>>=0;if(count<=288){var view=miniTempWebGLFloatBuffers[count];for(var i=0;i<count;++i){view[i]=HEAPF32[value+4*i>>>2>>>0]}}else{var view=HEAPF32.subarray(value>>>2>>>0,value+count*4>>>2>>>0)}GLctx.uniform1fv(webglGetUniformLocation(location),view)}var _glUniform1i=(location,v0)=>{GLctx.uniform1i(webglGetUniformLocation(location),v0)};var miniTempWebGLIntBuffers=[];function _glUniform1iv(location,count,value){value>>>=0;if(count<=288){var view=miniTempWebGLIntBuffers[count];for(var i=0;i<count;++i){view[i]=HEAP32[value+4*i>>>2>>>0]}}else{var view=HEAP32.subarray(value>>>2>>>0,value+count*4>>>2>>>0)}GLctx.uniform1iv(webglGetUniformLocation(location),view)}function _glUniform2fv(location,count,value){value>>>=0;if(count<=144){count*=2;var view=miniTempWebGLFloatBuffers[count];for(var i=0;i<count;i+=2){view[i]=HEAPF32[value+4*i>>>2>>>0];view[i+1]=HEAPF32[value+(4*i+4)>>>2>>>0]}}else{var view=HEAPF32.subarray(value>>>2>>>0,value+count*8>>>2>>>0)}GLctx.uniform2fv(webglGetUniformLocation(location),view)}function _glUniform2iv(location,count,value){value>>>=0;if(count<=144){count*=2;var view=miniTempWebGLIntBuffers[count];for(var i=0;i<count;i+=2){view[i]=HEAP32[value+4*i>>>2>>>0];view[i+1]=HEAP32[value+(4*i+4)>>>2>>>0]}}else{var view=HEAP32.subarray(value>>>2>>>0,value+count*8>>>2>>>0)}GLctx.uniform2iv(webglGetUniformLocation(location),view)}function _glUniform3fv(location,count,value){value>>>=0;if(count<=96){count*=3;var view=miniTempWebGLFloatBuffers[count];for(var i=0;i<count;i+=3){view[i]=HEAPF32[value+4*i>>>2>>>0];view[i+1]=HEAPF32[value+(4*i+4)>>>2>>>0];view[i+2]=HEAPF32[value+(4*i+8)>>>2>>>0]}}else{var view=HEAPF32.subarray(value>>>2>>>0,value+count*12>>>2>>>0)}GLctx.uniform3fv(webglGetUniformLocation(location),view)}function _glUniform3iv(location,count,value){value>>>=0;if(count<=96){count*=3;var view=miniTempWebGLIntBuffers[count];for(var i=0;i<count;i+=3){view[i]=HEAP32[value+4*i>>>2>>>0];view[i+1]=HEAP32[value+(4*i+4)>>>2>>>0];view[i+2]=HEAP32[value+(4*i+8)>>>2>>>0]}}else{var view=HEAP32.subarray(value>>>2>>>0,value+count*12>>>2>>>0)}GLctx.uniform3iv(webglGetUniformLocation(location),view)}function _glUniform4fv(location,count,value){value>>>=0;if(count<=72){var view=miniTempWebGLFloatBuffers[4*count];var heap=HEAPF32;value=value>>>2;count*=4;for(var i=0;i<count;i+=4){var dst=value+i;view[i]=heap[dst>>>0];view[i+1]=heap[dst+1>>>0];view[i+2]=heap[dst+2>>>0];view[i+3]=heap[dst+3>>>0]}}else{var view=HEAPF32.subarray(value>>>2>>>0,value+count*16>>>2>>>0)}GLctx.uniform4fv(webglGetUniformLocation(location),view)}function _glUniform4iv(location,count,value){value>>>=0;if(count<=72){count*=4;var view=miniTempWebGLIntBuffers[count];for(var i=0;i<count;i+=4){view[i]=HEAP32[value+4*i>>>2>>>0];view[i+1]=HEAP32[value+(4*i+4)>>>2>>>0];view[i+2]=HEAP32[value+(4*i+8)>>>2>>>0];view[i+3]=HEAP32[value+(4*i+12)>>>2>>>0]}}else{var view=HEAP32.subarray(value>>>2>>>0,value+count*16>>>2>>>0)}GLctx.uniform4iv(webglGetUniformLocation(location),view)}function _glUniformMatrix4fv(location,count,transpose,value){value>>>=0;if(count<=18){var view=miniTempWebGLFloatBuffers[16*count];var heap=HEAPF32;value=value>>>2;count*=16;for(var i=0;i<count;i+=16){var dst=value+i;view[i]=heap[dst>>>0];view[i+1]=heap[dst+1>>>0];view[i+2]=heap[dst+2>>>0];view[i+3]=heap[dst+3>>>0];view[i+4]=heap[dst+4>>>0];view[i+5]=heap[dst+5>>>0];view[i+6]=heap[dst+6>>>0];view[i+7]=heap[dst+7>>>0];view[i+8]=heap[dst+8>>>0];view[i+9]=heap[dst+9>>>0];view[i+10]=heap[dst+10>>>0];view[i+11]=heap[dst+11>>>0];view[i+12]=heap[dst+12>>>0];view[i+13]=heap[dst+13>>>0];view[i+14]=heap[dst+14>>>0];view[i+15]=heap[dst+15>>>0]}}else{var view=HEAPF32.subarray(value>>>2>>>0,value+count*64>>>2>>>0)}GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,view)}var _glUseProgram=program=>{program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program};var _glVertexAttribDivisor=(index,divisor)=>{GLctx.vertexAttribDivisor(index,divisor)};function _glVertexAttribPointer(index,size,type,normalized,stride,ptr){ptr>>>=0;GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)}var _glViewport=(x0,x1,x2,x3)=>GLctx.viewport(x0,x1,x2,x3);function _llvm_eh_typeid_for(type){type>>>=0;return type}var _onCapture=(ptr,len)=>{window.__ph__.onCapture(ptr,len)};var _onCopyText=(text,len)=>{window.__ph__.onCopyText(text,len)};var _onESC=()=>{window.__ph__.onESC()};var _onFontRequest=(font_family_ptr,font_family_len,font_style,font_weight)=>{window.__ph__.onFontRequest(font_family_ptr,font_family_len,font_style,font_weight)};var _onFrameBegin=()=>{window.__ph__.onFrameBegin()};var _onFrameEnd=()=>{window.__ph__.onFrameEnd()};var _onGetTextPathId=node_id=>window.__ph__.onGetTextPathId(node_id);var _onNodeUpdate=(node_id,flag)=>{window.__ph__.onNodeUpdate(node_id,flag)};var _onOverlay=()=>{window.__ph__.onOverlay()};var _onPasteText=()=>{window.__ph__.onPasteText()};var _onPrepareCamera=()=>window.__ph__.onPrepareCamera();var _onReady=()=>{window.__ph__.onReady()};var _onRedoComb=()=>{window.__ph__.onRedoComb()};var _onSelectionUpdate=(node_id,start_block,start_char,end_block,end_char,cursor,active_block_index)=>{window.__ph__.onSelectionUpdate(node_id,start_block,start_char,end_block,end_char,cursor,active_block_index)};var _onShiftTab=()=>{window.__ph__.onShiftTab()};var _onTab=()=>{window.__ph__.onTab()};var _onTextUpdate=(text_ptr,text_len,isComposing,start,end,cursor)=>{window.__ph__.onTextUpdate(text_ptr,text_len,isComposing,start,end,cursor)};var _onUndoComb=()=>{window.__ph__.onUndoComb()};var _onUpdateElementSize=(node_id,width,height)=>{window.__ph__.onUpdateElementSize(node_id,width,height)};var _onUpdatePageOffset=node_id=>{window.__ph__.onUpdatePageOffset(node_id)};var _onUpdateSceneTree=()=>{window.__ph__.onUpdateSceneTree()};var _onZoom=zoom=>{window.__ph__.onZoom(zoom)};var _onZoomIn=()=>{window.__ph__.onZoomIn()};var _onZoomOut=()=>{window.__ph__.onZoomOut()};var _onZoomToFit=()=>{window.__ph__.onZoomToFit()};var _onZoomToSelection=()=>{window.__ph__.onZoomToSelection()};var stackAlloc=sz=>__emscripten_stack_alloc(sz);var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var withStackSave=f=>{var stack=stackSave();var ret=f();stackRestore(stack);return ret};var getCFunc=ident=>{var func=Module["_"+ident];return func};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer>>>0)};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={string:str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},array:arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func(...cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};var cwrap=(ident,returnType,argTypes,opts)=>{var numericArgs=!argTypes||argTypes.every(type=>type==="number"||type==="boolean");var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return(...args)=>ccall(ident,returnType,argTypes,args,opts)};Module["requestAnimationFrame"]=MainLoop.requestAnimationFrame;Module["pauseMainLoop"]=MainLoop.pause;Module["resumeMainLoop"]=MainLoop.resume;MainLoop.init();for(var i=0;i<32;++i)tempFixedLengthArray.push(new Array(i));var miniTempWebGLFloatBuffersStorage=new Float32Array(288);for(var i=0;i<=288;++i){miniTempWebGLFloatBuffers[i]=miniTempWebGLFloatBuffersStorage.subarray(0,i)}var miniTempWebGLIntBuffersStorage=new Int32Array(288);for(var i=0;i<=288;++i){miniTempWebGLIntBuffers[i]=miniTempWebGLIntBuffersStorage.subarray(0,i)}var wasmImports={Bc:___cxa_begin_catch,xc:___cxa_end_catch,b:___cxa_find_matching_catch_2,H:___cxa_find_matching_catch_4,vc:___cxa_throw,e:___resumeException,Ib:___syscall_getcwd,Kb:__abort_js,Xa:_sapp_emsc_set_input_rect,Qa:_emscripten_cancel_main_loop,Sa:_emscripten_get_canvas_element_size,z:_emscripten_get_device_pixel_ratio,A:_emscripten_get_element_css_size,na:_emscripten_get_now,Ra:_emscripten_performance_now,Za:_emscripten_request_animation_frame_loop,Hb:_emscripten_resize_heap,a:_emscripten_return_address,S:_emscripten_set_blur_callback_on_thread,ha:_emscripten_set_canvas_element_size,T:_emscripten_set_focus_callback_on_thread,aa:_emscripten_set_keydown_callback_on_thread,_:_emscripten_set_keypress_callback_on_thread,$:_emscripten_set_keyup_callback_on_thread,_a:_emscripten_set_main_loop,ga:_emscripten_set_mousedown_callback_on_thread,da:_emscripten_set_mouseenter_callback_on_thread,ca:_emscripten_set_mouseleave_callback_on_thread,ea:_emscripten_set_mousemove_callback_on_thread,fa:_emscripten_set_mouseup_callback_on_thread,V:_emscripten_set_pointerlockchange_callback_on_thread,U:_emscripten_set_pointerlockerror_callback_on_thread,B:_emscripten_set_resize_callback_on_thread,W:_emscripten_set_touchcancel_callback_on_thread,X:_emscripten_set_touchend_callback_on_thread,Y:_emscripten_set_touchmove_callback_on_thread,Z:_emscripten_set_touchstart_callback_on_thread,R:_emscripten_set_webglcontextlost_callback_on_thread,Q:_emscripten_set_webglcontextrestored_callback_on_thread,ba:_emscripten_set_wheel_callback_on_thread,eb:_emscripten_webgl_create_context,cb:_emscripten_webgl_enable_extension,db:_emscripten_webgl_make_context_current,Lb:_environ_get,Mb:_environ_sizes_get,Jb:_fd_write,E:_glActiveTexture,Fa:_glAttachShader,j:_glBindBuffer,k:_glBindBufferBase,n:_glBindFramebuffer,Nc:_glBindRenderbuffer,D:_glBindSampler,s:_glBindTexture,Ma:_glBindVertexArray,ua:_glBlendColor,va:_glBlendEquationSeparate,wa:_glBlendFuncSeparate,$b:_glBlitFramebuffer,Pc:_glBufferData,Ia:_glBufferSubData,Ba:_glCheckFramebufferStatus,wc:_glClearBufferfi,ya:_glClearBufferfv,uc:_glClearBufferiv,v:_glColorMask,Rb:_glCompileShader,Gc:_glCompressedTexImage2D,Ic:_glCompressedTexImage3D,Cc:_glCreateProgram,Tb:_glCreateShader,ta:_glCullFace,Ub:_glDeleteBuffers,m:_glDeleteFramebuffers,P:_glDeleteProgram,Ka:_glDeleteRenderbuffers,Ja:_glDeleteSamplers,O:_glDeleteShader,La:_glDeleteTextures,Rc:_glDeleteVertexArrays,J:_glDepthFunc,K:_glDepthMask,l:_glDisable,ra:_glDisableVertexAttribArray,cc:_glDrawArrays,dc:_glDrawArraysInstanced,Aa:_glDrawBuffers,fc:_glDrawElements,ec:_glDrawElementsInstanced,p:_glEnable,pc:_glEnableVertexAttribArray,Ca:_glFramebufferRenderbuffer,F:_glFramebufferTexture2D,Pb:_glFramebufferTextureLayer,sa:_glFrontFace,Qc:_glGenBuffers,M:_glGenFramebuffers,Oc:_glGenRenderbuffers,Dc:_glGenSamplers,Lc:_glGenTextures,Tc:_glGenVertexArrays,yc:_glGetAttribLocation,i:_glGetIntegerv,zc:_glGetProgramInfoLog,Ea:_glGetProgramiv,Qb:_glGetShaderInfoLog,qa:_glGetShaderiv,Uc:_glGetStringi,N:_glGetUniformLocation,_b:_glInvalidateFramebuffer,Ac:_glLinkProgram,Sc:_glPixelStorei,xa:_glPolygonOffset,ac:_glReadBuffer,Xb:_glReadPixels,Mc:_glRenderbufferStorageMultisample,Ga:_glSamplerParameterf,o:_glSamplerParameteri,L:_glScissor,Sb:_glShaderSource,Wb:_glStencilFunc,tc:_glStencilFuncSeparate,I:_glStencilMask,Vb:_glStencilOp,sc:_glStencilOpSeparate,Fc:_glTexImage2D,Hc:_glTexImage3D,Kc:_glTexParameteri,Ec:_glTexStorage2D,Jc:_glTexStorage3D,Yb:_glTexSubImage2D,Zb:_glTexSubImage3D,oc:_glUniform1fv,Da:_glUniform1i,kc:_glUniform1iv,nc:_glUniform2fv,jc:_glUniform2iv,mc:_glUniform3fv,ic:_glUniform3iv,lc:_glUniform4fv,hc:_glUniform4iv,gc:_glUniformMatrix4fv,w:_glUseProgram,qc:_glVertexAttribDivisor,rc:_glVertexAttribPointer,za:_glViewport,g:html_is_osx,Eb:invoke_fifffffffffiiif,Fb:invoke_fifffffffiiif,q:invoke_ii,r:invoke_iiii,bc:invoke_iiiiii,Ha:invoke_v,c:invoke_vi,la:invoke_viff,Cb:invoke_viffff,Bb:invoke_viffffff,C:invoke_viffii,d:invoke_vii,Ab:invoke_viif,f:invoke_viii,zb:invoke_viiif,y:invoke_viiii,Db:invoke_viiiif,h:invoke_viiiii,t:invoke_vijjjj,G:_llvm_eh_typeid_for,tb:_onCapture,ja:_onCopyText,nb:_onESC,hb:_onFontRequest,yb:_onFrameBegin,ub:_onFrameEnd,Gb:_onGetTextPathId,oa:_onNodeUpdate,vb:_onOverlay,jb:_onPasteText,wb:_onPrepareCamera,Ob:_onReady,lb:_onRedoComb,u:_onSelectionUpdate,ib:_onShiftTab,kb:_onTab,ia:_onTextUpdate,mb:_onUndoComb,gb:_onUpdateElementSize,ka:_onUpdatePageOffset,xb:_onUpdateSceneTree,sb:_onZoom,ob:_onZoomIn,pb:_onZoomOut,rb:_onZoomToFit,qb:_onZoomToSelection,Ta:sapp_html_set_window_size,bb:sapp_js_add_beforeunload_listener,ab:sapp_js_add_clipboard_listener,$a:sapp_js_add_dragndrop_listeners,Va:sapp_js_clear_favicon,fb:sapp_js_init,Pa:sapp_js_remove_beforeunload_listener,Oa:sapp_js_remove_clipboard_listener,Na:sapp_js_remove_dragndrop_listeners,x:sapp_js_request_pointerlock,Ya:sapp_js_set_cursor,Ua:sapp_js_set_favicon,Wa:sapp_js_write_clipboard,ma:sfetch_js_send_get_request,Nb:sfetch_js_send_head_request,pa:slog_js_log};var wasmExports=await createWasm();var ___wasm_call_ctors=wasmExports["Wc"];var _ph_allocPerm=Module["_ph_allocPerm"]=wasmExports["Xc"];var _ph_allocPerm32=Module["_ph_allocPerm32"]=wasmExports["Yc"];var _ph_allocArena=Module["_ph_allocArena"]=wasmExports["Zc"];var _ph_allocArena32=Module["_ph_allocArena32"]=wasmExports["_c"];var _ph_freePerm=Module["_ph_freePerm"]=wasmExports["$c"];var _ph_setTransform=Module["_ph_setTransform"]=wasmExports["ad"];var _ph_fillStyle=Module["_ph_fillStyle"]=wasmExports["bd"];var _ph_strokeStyle=Module["_ph_strokeStyle"]=wasmExports["cd"];var _ph_drawPathEx=Module["_ph_drawPathEx"]=wasmExports["dd"];var _ph_drawLine=Module["_ph_drawLine"]=wasmExports["ed"];var _ph_drawLineShadow=Module["_ph_drawLineShadow"]=wasmExports["fd"];var _ph_drawRect=Module["_ph_drawRect"]=wasmExports["gd"];var _ph_drawRoundedRect=Module["_ph_drawRoundedRect"]=wasmExports["hd"];var _ph_drawSolidRect=Module["_ph_drawSolidRect"]=wasmExports["id"];var _ph_drawSolidRoundedRect=Module["_ph_drawSolidRoundedRect"]=wasmExports["jd"];var _ph_drawCircle=Module["_ph_drawCircle"]=wasmExports["kd"];var _ph_drawSolidCircle=Module["_ph_drawSolidCircle"]=wasmExports["ld"];var _ph_drawCircleShadow=Module["_ph_drawCircleShadow"]=wasmExports["md"];var _ph_drawEllipse=Module["_ph_drawEllipse"]=wasmExports["nd"];var _ph_drawSolidEllipse=Module["_ph_drawSolidEllipse"]=wasmExports["od"];var _ph_drawEllipseShadow=Module["_ph_drawEllipseShadow"]=wasmExports["pd"];var _ph_drawTextEx=Module["_ph_drawTextEx"]=wasmExports["qd"];var _ph_measureTextEx=Module["_ph_measureTextEx"]=wasmExports["rd"];var _ph_uploadImageEx=Module["_ph_uploadImageEx"]=wasmExports["sd"];var _ph_destroyCanvasImage=Module["_ph_destroyCanvasImage"]=wasmExports["td"];var _ph_drawImage=Module["_ph_drawImage"]=wasmExports["ud"];var _ph_setCamera=Module["_ph_setCamera"]=wasmExports["vd"];var _ph_getPathBBoxEx=Module["_ph_getPathBBoxEx"]=wasmExports["wd"];var _ph_getTextPathBBoxEx=Module["_ph_getTextPathBBoxEx"]=wasmExports["xd"];var _ph_getTextLineLength=Module["_ph_getTextLineLength"]=wasmExports["yd"];var _ph_getTextBaseline=Module["_ph_getTextBaseline"]=wasmExports["zd"];var _ph_getTextSelectionBlockEx=Module["_ph_getTextSelectionBlockEx"]=wasmExports["Ad"];var _ph_getTextEditingNodeIdEx=Module["_ph_getTextEditingNodeIdEx"]=wasmExports["Bd"];var _ph_getTextCaretPosEx=Module["_ph_getTextCaretPosEx"]=wasmExports["Cd"];var _ph_getStrokeBBox=Module["_ph_getStrokeBBox"]=wasmExports["Dd"];var _ph_isPointInPath=Module["_ph_isPointInPath"]=wasmExports["Ed"];var _ph_isPointInStroke=Module["_ph_isPointInStroke"]=wasmExports["Fd"];var _ph_isPointInText=Module["_ph_isPointInText"]=wasmExports["Gd"];var _ph_markNodeChanged=Module["_ph_markNodeChanged"]=wasmExports["Hd"];var _ph_makeNode=Module["_ph_makeNode"]=wasmExports["Id"];var _ph_destroyNode=Module["_ph_destroyNode"]=wasmExports["Jd"];var _ph_addNodeChild=Module["_ph_addNodeChild"]=wasmExports["Kd"];var _ph_getNodeChildCount=Module["_ph_getNodeChildCount"]=wasmExports["Ld"];var _ph_insertNodeChild=Module["_ph_insertNodeChild"]=wasmExports["Md"];var _ph_addNodeToRoot=Module["_ph_addNodeToRoot"]=wasmExports["Nd"];var _ph_reorderNode=Module["_ph_reorderNode"]=wasmExports["Od"];var _ph_removeNode=Module["_ph_removeNode"]=wasmExports["Pd"];var _ph_setNodeTransform=Module["_ph_setNodeTransform"]=wasmExports["Qd"];var _ph_setNodeVisible=Module["_ph_setNodeVisible"]=wasmExports["Rd"];var _ph_setNodeOpacity=Module["_ph_setNodeOpacity"]=wasmExports["Sd"];var _ph_setNodeBlend=Module["_ph_setNodeBlend"]=wasmExports["Td"];var _ph_setNodeCompose=Module["_ph_setNodeCompose"]=wasmExports["Ud"];var _ph_removeNodeCompose=Module["_ph_removeNodeCompose"]=wasmExports["Vd"];var _ph_destroyNodeCompose=Module["_ph_destroyNodeCompose"]=wasmExports["Wd"];var _ph_setNodePath=Module["_ph_setNodePath"]=wasmExports["Xd"];var _ph_enterTextEditingMode=Module["_ph_enterTextEditingMode"]=wasmExports["Yd"];var _ph_changeTextEditingNode=Module["_ph_changeTextEditingNode"]=wasmExports["Zd"];var _ph_exitTextEditingMode=Module["_ph_exitTextEditingMode"]=wasmExports["_d"];var _ph_setForceDefaultCursor=Module["_ph_setForceDefaultCursor"]=wasmExports["$d"];var _ph_setTextSelection=Module["_ph_setTextSelection"]=wasmExports["ae"];var _ph_updatePageOffset=Module["_ph_updatePageOffset"]=wasmExports["be"];var _ph_setNodeTextStyle=Module["_ph_setNodeTextStyle"]=wasmExports["ce"];var _ph_setNodeFillPaint=Module["_ph_setNodeFillPaint"]=wasmExports["de"];var _ph_setNodeFillOpacity=Module["_ph_setNodeFillOpacity"]=wasmExports["ee"];var _ph_setNodeStrokePaint=Module["_ph_setNodeStrokePaint"]=wasmExports["fe"];var _ph_setNodeStrokeOpacity=Module["_ph_setNodeStrokeOpacity"]=wasmExports["ge"];var _ph_setNodeStrokeData=Module["_ph_setNodeStrokeData"]=wasmExports["he"];var _ph_setAssetsPathEx=Module["_ph_setAssetsPathEx"]=wasmExports["ie"];var _ph_makePathEx=Module["_ph_makePathEx"]=wasmExports["je"];var _ph_makeEmptyPath=Module["_ph_makeEmptyPath"]=wasmExports["ke"];var _ph_setPathEx=Module["_ph_setPathEx"]=wasmExports["le"];var _ph_setEmptyPath=Module["_ph_setEmptyPath"]=wasmExports["me"];var _ph_destroyPath=Module["_ph_destroyPath"]=wasmExports["ne"];var _ph_getTextDocIDEx=Module["_ph_getTextDocIDEx"]=wasmExports["oe"];var _ph_setTextDocEx=Module["_ph_setTextDocEx"]=wasmExports["pe"];var _ph_destroyTextDoc=Module["_ph_destroyTextDoc"]=wasmExports["qe"];var _ph_destoryTextPath=Module["_ph_destoryTextPath"]=wasmExports["re"];var _ph_fetchFontEx=Module["_ph_fetchFontEx"]=wasmExports["se"];var _ph_setTextStyleEx=Module["_ph_setTextStyleEx"]=wasmExports["te"];var _ph_buildLayoutAndPathEx=Module["_ph_buildLayoutAndPathEx"]=wasmExports["ue"];var _ph_getTextOffsetEx=Module["_ph_getTextOffsetEx"]=wasmExports["ve"];var _ph_getTextLayoutBoxEx=Module["_ph_getTextLayoutBoxEx"]=wasmExports["we"];var _ph_makeColor=Module["_ph_makeColor"]=wasmExports["xe"];var _ph_setColor=Module["_ph_setColor"]=wasmExports["ye"];var _ph_destroyColor=Module["_ph_destroyColor"]=wasmExports["ze"];var _ph_makeGradientEx=Module["_ph_makeGradientEx"]=wasmExports["Ae"];var _ph_setGradientMatrixEx=Module["_ph_setGradientMatrixEx"]=wasmExports["Be"];var _ph_setGradientTag=Module["_ph_setGradientTag"]=wasmExports["Ce"];var _ph_setGradientStopLen=Module["_ph_setGradientStopLen"]=wasmExports["De"];var _ph_setGradientStop=Module["_ph_setGradientStop"]=wasmExports["Ee"];var _ph_updateGradientPixels=Module["_ph_updateGradientPixels"]=wasmExports["Fe"];var _ph_destroyGradient=Module["_ph_destroyGradient"]=wasmExports["Ge"];var _ph_makeStroke=Module["_ph_makeStroke"]=wasmExports["He"];var _ph_destroyStrokePathCacheEx=Module["_ph_destroyStrokePathCacheEx"]=wasmExports["Ie"];var _ph_setStroke=Module["_ph_setStroke"]=wasmExports["Je"];var _ph_destroyStroke=Module["_ph_destroyStroke"]=wasmExports["Ke"];var _ph_getTextPathEx=Module["_ph_getTextPathEx"]=wasmExports["Le"];var _ph_getPathCmdEx=Module["_ph_getPathCmdEx"]=wasmExports["Me"];var _ph_getPathVtxEx=Module["_ph_getPathVtxEx"]=wasmExports["Ne"];var _ph_getStrokePathCmdEx=Module["_ph_getStrokePathCmdEx"]=wasmExports["Oe"];var _ph_getStrokePathVtxEx=Module["_ph_getStrokePathVtxEx"]=wasmExports["Pe"];var _ph_allocImage=Module["_ph_allocImage"]=wasmExports["Qe"];var _ph_makeImageWithURLEx=Module["_ph_makeImageWithURLEx"]=wasmExports["Re"];var _ph_makeImageWithFileDataEx=Module["_ph_makeImageWithFileDataEx"]=wasmExports["Se"];var _ph_makeImageWithPixelsEx=Module["_ph_makeImageWithPixelsEx"]=wasmExports["Te"];var _ph_setImageWithURLEx=Module["_ph_setImageWithURLEx"]=wasmExports["Ue"];var _ph_setImageWithFileDataEx=Module["_ph_setImageWithFileDataEx"]=wasmExports["Ve"];var _ph_setImageWithPixelsEx=Module["_ph_setImageWithPixelsEx"]=wasmExports["We"];var _ph_setImageFillMode=Module["_ph_setImageFillMode"]=wasmExports["Xe"];var _ph_destroyImage=Module["_ph_destroyImage"]=wasmExports["Ye"];var _ph_makeCompose=Module["_ph_makeCompose"]=wasmExports["Ze"];var _ph_setComposeRoot=Module["_ph_setComposeRoot"]=wasmExports["_e"];var _ph_destroyCompose=Module["_ph_destroyCompose"]=wasmExports["$e"];var _ph_setBackgroundColor=Module["_ph_setBackgroundColor"]=wasmExports["af"];var _ph_numNodesRender=Module["_ph_numNodesRender"]=wasmExports["bf"];var _ph_numTilesRender=Module["_ph_numTilesRender"]=wasmExports["cf"];var _ph_purge=Module["_ph_purge"]=wasmExports["df"];var _ph_setWindowSize=Module["_ph_setWindowSize"]=wasmExports["ef"];var _ph_getCanvasWidth=Module["_ph_getCanvasWidth"]=wasmExports["ff"];var _ph_getCanvasHeight=Module["_ph_getCanvasHeight"]=wasmExports["gf"];var _ph_setCaptureBackgroundColor=Module["_ph_setCaptureBackgroundColor"]=wasmExports["hf"];var _ph_capture=Module["_ph_capture"]=wasmExports["jf"];var _ph_pauseApp=Module["_ph_pauseApp"]=wasmExports["kf"];var _ph_resumeApp=Module["_ph_resumeApp"]=wasmExports["lf"];var _ph_getNodeStorageCount=Module["_ph_getNodeStorageCount"]=wasmExports["mf"];var _ph_getColorStorageCount=Module["_ph_getColorStorageCount"]=wasmExports["nf"];var _ph_getPathStorageCount=Module["_ph_getPathStorageCount"]=wasmExports["of"];var _ph_getStrokeStorageCount=Module["_ph_getStrokeStorageCount"]=wasmExports["pf"];var _main=Module["_main"]=wasmExports["rf"];var _malloc=wasmExports["sf"];var __sapp_emsc_onpaste=Module["__sapp_emsc_onpaste"]=wasmExports["tf"];var __sapp_html5_get_ask_leave_site=Module["__sapp_html5_get_ask_leave_site"]=wasmExports["uf"];var __sapp_emsc_begin_drop=Module["__sapp_emsc_begin_drop"]=wasmExports["vf"];var __sapp_emsc_drop=Module["__sapp_emsc_drop"]=wasmExports["wf"];var __sapp_emsc_end_drop=Module["__sapp_emsc_end_drop"]=wasmExports["xf"];var __sapp_emsc_invoke_fetch_cb=Module["__sapp_emsc_invoke_fetch_cb"]=wasmExports["yf"];var __sapp_emsc_input_text=Module["__sapp_emsc_input_text"]=wasmExports["zf"];var __sapp_emsc_input_text_composition=Module["__sapp_emsc_input_text_composition"]=wasmExports["Af"];var __set_editing_text=Module["__set_editing_text"]=wasmExports["Bf"];var __sfetch_emsc_head_response=Module["__sfetch_emsc_head_response"]=wasmExports["Cf"];var __sfetch_emsc_get_response=Module["__sfetch_emsc_get_response"]=wasmExports["Df"];var __sfetch_emsc_failed_http_status=Module["__sfetch_emsc_failed_http_status"]=wasmExports["Ef"];var __sfetch_emsc_failed_buffer_too_small=Module["__sfetch_emsc_failed_buffer_too_small"]=wasmExports["Ff"];var _setThrew=wasmExports["Gf"];var __emscripten_tempret_set=wasmExports["Hf"];var __emscripten_stack_restore=wasmExports["If"];var __emscripten_stack_alloc=wasmExports["Jf"];var _emscripten_stack_get_current=wasmExports["Kf"];var ___cxa_increment_exception_refcount=wasmExports["Lf"];var ___cxa_decrement_exception_refcount=wasmExports["Mf"];var ___cxa_can_catch=wasmExports["Nf"];var ___cxa_get_exception_ptr=wasmExports["Of"];function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_v(index){var sp=stackSave();try{getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vijjjj(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viffii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_fifffffffiiif(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_fifffffffffiiif(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiif(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viff(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viffff(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viffffff(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viif(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiif(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function applySignatureConversions(wasmExports){wasmExports=Object.assign({},wasmExports);var makeWrapper_pp=f=>a0=>f(a0)>>>0;var makeWrapper_p=f=>()=>f()>>>0;wasmExports["sf"]=makeWrapper_pp(wasmExports["sf"]);wasmExports["Jf"]=makeWrapper_pp(wasmExports["Jf"]);wasmExports["Kf"]=makeWrapper_p(wasmExports["Kf"]);wasmExports["Of"]=makeWrapper_pp(wasmExports["Of"]);return wasmExports}Module["ccall"]=ccall;Module["cwrap"]=cwrap;function callMain(args=[]){var entryFunction=_main;args.unshift(thisProgram);var argc=args.length;var argv=stackAlloc((argc+1)*4);var argv_ptr=argv;args.forEach(arg=>{HEAPU32[argv_ptr>>>2>>>0]=stringToUTF8OnStack(arg);argv_ptr+=4});HEAPU32[argv_ptr>>>2>>>0]=0;try{var ret=entryFunction(argc,argv);exitJS(ret,true);return ret}catch(e){return handleException(e)}}function run(args=arguments_){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();readyPromiseResolve(Module);Module["onRuntimeInitialized"]?.();var noInitialRun=Module["noInitialRun"];if(!noInitialRun)callMain(args);postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();moduleRtn=readyPromise;


  return moduleRtn;
}
);
})();
export default Module;
