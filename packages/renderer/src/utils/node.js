import { BlendM<PERSON>, BooleanOperation, ElementType, EntityType, ImageMode, PaintType } from '@phase-software/types'
import { setupBaseGeometry, watchElementChanges } from '../update-controller'
import { DinoSubTree, MaskType, UpdateType } from '../visual_server/RenderItem'
import { dino } from '../dino'
import { fixInfiniteSkew } from '../visual_server/Transform'
import { addNewNodeHitTest } from '../actions/selection'
import { cleanupDinoTreePaths } from './textUtils'

/** @typedef {import('../visual_server/VisualServer').VisualServer} VisualServer */
/** @typedef {import('../visual_server/SpatialCache').SceneNode} SceneNode */
/** @typedef {import('../visual_server/RenderItem').RenderItem} RenderItem */
/** @typedef {import('../visual_server/RenderItem').DinoTree} DinoTree */
/** @typedef {import('../visual_server/Transform').Transform} Transform */
/** @typedef {import('../visual_server/RenderItem').NodeTypes} NodeTypes */
/** @typedef {import('../visual_server/Layer').Layer} Layer */

const api = dino()

/**
 * Initialize scene node with element, and setup base geometry, controller and hit test
 * @param {VisualServer} _
 * @param {SceneNode} node
 * @param {Element} element
 */
export function initSceneNodeWithElement(_, node, element) {
    // Name
    node.item.name = element.get('name')

    // Type
    const entityType = element.get('type')
    const elementType = element.get('elementType')
    node.item.booleanType = BooleanOperation.NONE
    if (entityType !== EntityType.WORKSPACE && elementType === ElementType.CONTAINER) {
        if (element.get('isMask')) {
            node.item.maskType = MaskType.ALPHA
        }
        node.item.booleanType = element.get('booleanType') || BooleanOperation.NONE
    }
    node.item.type = _determineType(node.item, element)

    // Setup base geometry
    if (entityType === EntityType.ELEMENT) {
        setupBaseGeometry(element, node)
    }

    // Subscribe to change events
    watchElementChanges(element, node.item)

    // Add node to hit test
    addNewNodeHitTest(node)
}

/**
 * Create dino tree into view
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const createDinoTreeFromSceneNode = (VS, node) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)
    const type = node.item.type
    createDinoTree(type, dinoTree)

    if (node.item.type === 'text') {
        VS.view.setParent(dinoTree.text_node_id, node.id)
    }
}

/**
 * Create dino tree by Phase element type
 * @param {string} type
 * @param {DinoTree} dinoTree
 */
export const createDinoTree = (type, dinoTree) => {
    // each item at least has double hierarchy for represeting transforms
    switch (type) {
        case "screen": {
            dinoTree.root_id = api.makeNode(api.GROUP)
            // fill/stroke holder
            dinoTree.fills.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.fills.id)
            // child container
            dinoTree.children.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.children.id)
            break
        }
        case "workspace":
        case "group": {
            dinoTree.root_id = api.makeNode(api.GROUP)
            dinoTree.children.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.children.id)
            break
        }
        case "container": {
            dinoTree.root_id = api.makeNode(api.GROUP)
            // fill/stroke holder
            dinoTree.fills.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.fills.id)
            dinoTree.strokes.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.strokes.id)
            // child container
            dinoTree.children.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.children.id)
            break
        }
        case "text": {
            dinoTree.text_node_id = api.makeNode(api.TEXT)
            dinoTree.text_doc_id = api.getTextDocID(dinoTree.text_node_id)
            dinoTree.root_id = api.makeNode(api.GROUP)
            // fill/stroke holder
            dinoTree.fills.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.fills.id)
            dinoTree.strokes.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.strokes.id)
            break
        }
        case "path": {
            dinoTree.root_id = api.makeNode(api.GROUP)
            // fill/stroke holder
            dinoTree.fills.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.fills.id)
            dinoTree.strokes.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.strokes.id)
            break
        }
        case "mask": {
            dinoTree.root_id = api.makeNode(api.GROUP)
            // child container
            dinoTree.children.id = api.makeNode(api.GROUP)
            api.addNodeChild(dinoTree.root_id, dinoTree.children.id)
            break
        }
        default: {
            throw new Error(`Unknown node type: ${type}`)
        }
    }

    // console.log('createDinoTree', dinoTree.root_id)
    // console.log(`n: ${api.getNodeStorageCount()} c: ${api.getColorStorageCount()} p: ${api.getPathStorageCount()} s: ${api.getStrokeStorageCount()}`)
}

/**
 * RenderItem should be already adjusted
 * Original dino tree should be removed from relationships of parent/sibling already
 * @param {VisualServer} VS
 * @param {SceneNode} node
 * @param {SceneNode} parent
 * @param {number} index
 */
export const insertDinoNode = (VS, node, parent, index) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)
    const parentDinoTree = VS.view.getOrCreateDinoTree(parent.id)

    const hasMask = (parentDinoTree.children.compose[1] !== null)
    const childCount = api.getNodeChildCount(parentDinoTree.children.id) + (hasMask ? 1 : 0)

    // If is mask group, need to handle last child related cases
    if (parent.item.isMaskGroup()) {
        const isFirstExistChild = (childCount === 0)
        if (isFirstExistChild) {
            // Directly put into compose
            parentDinoTree.children.compose[1] = new DinoSubTree()
            parentDinoTree.children.compose[1].id = dinoTree.root_id
            const maskType = 2
            parentDinoTree.children.compose[1].comp_id = api.makeCompose(maskType, parentDinoTree.children.compose[1].id)
            api.setNodeCompose(parentDinoTree.children.id, 1, parentDinoTree.children.compose[1].comp_id)
        } else {
            const isInsertAsMaskEl = (childCount === index)
            if (isInsertAsMaskEl) {
                // Set it as compose
                api.setComposeRoot(parentDinoTree.children.compose[1].comp_id, dinoTree.root_id)
                // Insert previous compose into children
                api.insertNodeChild(parentDinoTree.children.id, parentDinoTree.children.compose[1].id, index - 1)
                parentDinoTree.children.compose[1].id = dinoTree.root_id
                api.markNodeChanged(parentDinoTree.children.id)
            } else {
                // Directly insert into children
                api.insertNodeChild(parentDinoTree.children.id, dinoTree.root_id, index)
            }
        }
    } else {
        api.insertNodeChild(parentDinoTree.children.id, dinoTree.root_id, index)
    }
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 * @param {SceneNode} parent
 * @param {number} index - new index of the node
 */
export const reorderDinoNode = (VS, node, parent, index) => {

    const nodeDinoTree = VS.view.getOrCreateDinoTree(node.id)
    const parentDinoTree = VS.view.getOrCreateDinoTree(parent.id)

    const hasMask = (parentDinoTree.children.compose[1] !== null)
    const childCount = api.getNodeChildCount(parentDinoTree.children.id) + (hasMask ? 1 : 0)

    if (parent.item.isMaskGroup()) {
        const originallyIsMaskEl = (nodeDinoTree.root_id === parentDinoTree.children.compose[1].id)
        if (originallyIsMaskEl) {
            // Move new mask element from child to compose
            const newMaskEl = parent.getLastChild()
            const newMaskElDinoTree = VS.view.getOrCreateDinoTree(newMaskEl.id)
            newMaskEl.item.update(UpdateType.GEOMETRY | UpdateType.TRANSFORM)
            api.removeNode(newMaskElDinoTree.root_id)
            api.setComposeRoot(parentDinoTree.children.compose[1].comp_id, newMaskElDinoTree.root_id)
            // Insert previous compose to children
            api.insertNodeChild(parentDinoTree.children.id, parentDinoTree.children.compose[1].id, index)
            parentDinoTree.children.compose[1].id = newMaskElDinoTree.root_id
            api.markNodeChanged(parentDinoTree.root_id)
        }

        const isBecomingMaskEl = (index === childCount - 1)
        if (isBecomingMaskEl) {
            // Move node to compose
            api.removeNode(nodeDinoTree.root_id)
            api.setComposeRoot(parentDinoTree.children.compose[1].comp_id, nodeDinoTree.root_id)
            // Insert previous compose to children
            api.insertNodeChild(parentDinoTree.children.id, parentDinoTree.children.compose[1].id, index)
            parentDinoTree.children.compose[1].id = nodeDinoTree.root_id
            api.markNodeChanged(parentDinoTree.root_id)
        } else {
            api.reorderNode(nodeDinoTree.root_id, index)
        }
    } else {
        // Directly reorder
        api.reorderNode(nodeDinoTree.root_id, index)
    }
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const addDinoNodeToRenderRoot = (VS, node) => {
    const renderRootDinoTree = VS.view.getRootDinoTree()
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)
    api.insertNodeChild(renderRootDinoTree.children.id, dinoTree.root_id, 0)
}

/**
 * Remove node from current relationships
 * @param {VisualServer} VS
 * @param {SceneNode} node
 * @param {SceneNode} parent
 * @param {boolean} originalyIsMaskElement
 */
export const removeDinoNode = (VS, node, parent, originalyIsMaskElement) => {

    const nodeDinoTree = VS.view.getOrCreateDinoTree(node.id)
    const parentDinoTree = VS.view.getOrCreateDinoTree(parent.id)

    if (originalyIsMaskElement) {
        const newMaskEl = parent.getLastChild()
        // Move new last child to compose
        if (newMaskEl) {
            const newMaskElDinoTree = VS.view.getOrCreateDinoTree(newMaskEl.id)
            newMaskEl.item.update(UpdateType.GEOMETRY | UpdateType.TRANSFORM)
            // Move new mask element from children to compose
            api.removeNode(newMaskElDinoTree.root_id)
            api.setComposeRoot(parentDinoTree.children.compose[1].comp_id, newMaskElDinoTree.root_id)
            parentDinoTree.children.compose[1].id = newMaskElDinoTree.root_id
            api.markNodeChanged(parentDinoTree.children.id)
        } else {
            // clear compose since there is no child
            api.setComposeRoot(parentDinoTree.children.compose[1].comp_id, 0)
        }
    } else {
        // Directly remove from children
        api.removeNode(nodeDinoTree.root_id)
    }
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const destroyDinoTreeFromSceneNode = (VS, node) => {
    const dinoTree = VS.view.getAndDeleteDinoTree(node.id)
    destroyDinoTree(dinoTree)
}

/**
 * Destroy dino tree
 * @param {DinoTree} dinoTree
 */
export const destroyDinoTree = (dinoTree) => {
    // console.log('destroyDinoTree', dinoTree.root_id)

    // Destroy or remove compose
    if (dinoTree.children.compose[0] !== null) api.destroyNodeCompose(dinoTree.children.id, 0)
    if (dinoTree.children.compose[1] !== null) api.removeNodeCompose(dinoTree.children.id, 1)
    dinoTree.children.compose[0] = null
    dinoTree.children.compose[1] = null

    // Clean up all paths in the DinoTree
    cleanupDinoTreePaths(dinoTree)

    // Destroy text doc
    if (dinoTree.text_doc_id !== 0) api.destroyTextDoc(dinoTree.text_doc_id)
    dinoTree.text_doc_id = 0
    // Destroy layers
    if (dinoTree.fills.children.length > 0) {
        for (let i = 0; i < dinoTree.fills.children.length; i++) {
            const dinoSubTree = dinoTree.fills.children[i]
            destroyDinoSubTreeFill(dinoSubTree)
            if (dinoSubTree.text_doc_id !== 0) api.destroyTextDoc(dinoSubTree.text_doc_id)
            api.destroyNode(dinoSubTree.id)
            dinoSubTree.id = 0
        }
    }
    if (dinoTree.strokes.children.length > 0) {
        for (let i = 0; i < dinoTree.strokes.children.length; i++) {
            const dinoSubTree = dinoTree.strokes.children[i]
            destroyDinoSubTreeStroke(dinoSubTree)
            if (dinoSubTree.text_doc_id !== 0) api.destroyTextDoc(dinoSubTree.text_doc_id)
            api.destroyNode(dinoSubTree.id)
            dinoSubTree.id = 0
            if (dinoSubTree.stroke_data_id !== 0) {
                api.destroyStroke(dinoSubTree.stroke_data_id)
                dinoSubTree.stroke_data_id = 0
            }
        }
    }
    // Destroy itself
    if (dinoTree.root_id !== 0) api.destroyNode(dinoTree.root_id)
    if (dinoTree.children.id !== 0) api.destroyNode(dinoTree.children.id)
    if (dinoTree.fills.id !== 0) api.destroyNode(dinoTree.fills.id)
    if (dinoTree.strokes.id !== 0) api.destroyNode(dinoTree.strokes.id)
    if (dinoTree.text_node_id !== 0) api.destroyNode(dinoTree.text_node_id)
    dinoTree.root_id = 0
    dinoTree.children.id = 0
    dinoTree.fills.id = 0
    dinoTree.strokes.id = 0

    // console.log(`n: ${api.getNodeStorageCount()} c: ${api.getColorStorageCount()} p: ${api.getPathStorageCount()} s: ${api.getStrokeStorageCount()}`)
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const updateDinoTreeTransformFromSceneNode = (VS, node) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)

    const isMaskElement = node.parent && node.parent.item.isMaskGroup() && node.parent.getLastChild().id === node.id
    const transform = node.item.transform

    if (isMaskElement) {
        // Mask element is a compose using world transform
        const { scale, skew, rotation, translation } = transform.world.decompose()
        api.setNodeTransform(
            dinoTree.root_id,
            translation.x,
            translation.y,
            rotation,
            scale.x,
            scale.y,
            fixInfiniteSkew(skew.x),
            fixInfiniteSkew(skew.y)
        )
        api.setNodeTransform(
            dinoTree.fills.id,
            0, 0, 0, 1, 1, 0, 0
        )
        api.setNodeTransform(
            dinoTree.strokes.id,
            0, 0, 0, 1, 1, 0, 0
        )
        api.setNodeTransform(
            dinoTree.children.id,
            0, 0, 0, 1, 1, 0, 0
        )
    } else {
        api.setNodeTransform(
            dinoTree.root_id,
            transform.translate.x,
            transform.translate.y,
            transform.rotation,
            transform.scale.x,
            transform.scale.y,
            fixInfiniteSkew(transform.skew.x),
            fixInfiniteSkew(transform.skew.y)
        )

        const pivotOffset = transform.getPivotOffset()
        api.setNodeTransform(
            dinoTree.fills.id,
            -pivotOffset.x, -pivotOffset.y,
            0, 1, 1, 0, 0
        )
        api.setNodeTransform(
            dinoTree.strokes.id,
            -pivotOffset.x, -pivotOffset.y,
            0, 1, 1, 0, 0
        )
        api.setNodeTransform(
            dinoTree.children.id,
            -pivotOffset.x, -pivotOffset.y,
            0, 1, 1, 0, 0
        )
    }

    if (dinoTree.children.compose[0] !== null) {
        const { scale, skew, rotation, translation } = transform.world.decompose()
        api.setNodeTransform(
            dinoTree.children.compose[0].id,
            translation.x,
            translation.y,
            rotation,
            scale.x,
            scale.y,
            fixInfiniteSkew(skew.x),
            fixInfiniteSkew(skew.y)
        )
    }
}

/**
 * Update dino tree fills base on fill layers on scene node
 * Will create or update dino sub tree fill for each fill layer
 * Will destroy all existing dino sub tree fill if there is no fill layer
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const updateDinoTreeFillsFromSceneNode = (VS, node) => {
    // Update or create dino sub tree fill for each fill layer
    const fillLayers = node.item.fillLayers
    for (let i = 0; i < fillLayers.length; i++) {
        createOrUpdateDinoSubTreeFillFromLayer(VS, node, i, fillLayers[i])
    }

    // Destroy all existing dino sub tree fill if there is no fill layer
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)
    for (let i = node.item.fillLayers.length; i < dinoTree.fills.children.length; i++) {
        const dinoSubTree = dinoTree.fills.children[i]
        destroyDinoSubTreeFill(dinoSubTree)
        api.destroyNode(dinoSubTree.id)
        dinoSubTree.id = 0
    }

    dinoTree.fills.children.length = fillLayers.length
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 * @param {number} i
 * @param {Layer} layer
 */
export const createOrUpdateDinoSubTreeFillFromLayer = (VS, node, i, layer) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)

    switch (layer.paint.type) {
        case PaintType.SOLID: {
            const color = layer.paint.params.fill_color
            const opacity = layer.paint.params.opacity[0]

            if (dinoTree.fills.children[i] && dinoTree.fills.children[i].fill_id > 0 && dinoTree.fills.children[i].fill_tag === api.COLOR) {
                // update existing paint
                api.setColor(dinoTree.fills.children[i].fill_id, color[0], color[1], color[2], opacity)
                api.markNodeChanged(dinoTree.fills.children[i].id)
            } else {
                if (dinoTree.fills.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeFill(dinoTree.fills.children[i])
                } else {
                    // create path node for new paint
                    dinoTree.fills.children[i] = new DinoSubTree()
                    if (node.item.type === "text") {
                        dinoTree.fills.children[i].id = api.makeNode(api.TEXT)
                        dinoTree.fills.children[i].text_doc_id = api.getTextDocID(dinoTree.fills.children[i].id)
                    } else {
                        dinoTree.fills.children[i].id = api.makeNode(api.PATH)
                    }
                    VS.view.setParent(dinoTree.fills.children[i].id, node.id)

                    api.addNodeChild(dinoTree.fills.id, dinoTree.fills.children[i].id)
                }

                const paint_id = api.makeColor(color[0], color[1], color[2], opacity)
                dinoTree.fills.children[i].fill_id = paint_id
                dinoTree.fills.children[i].fill_tag = api.COLOR
                api.setNodeFillPaint(dinoTree.fills.children[i].id, api.COLOR, paint_id)
            }
            break
        }
        case PaintType.GRADIENT_LINEAR:
        case PaintType.GRADIENT_RADIAL:
        case PaintType.GRADIENT_ANGULAR:
        case PaintType.GRADIENT_DIAMOND: {
            const gradient = layer.paint.gradient
            const opacity = layer.paint.params.opacity[0]
            const gradientXform = layer.paint.transform
            const gradientType = getDinoGradientType(layer.paint.type)

            const isGradientPaintExist = dinoTree.fills.children[i]?.fill_id > 0 && (dinoTree.fills.children[i]?.fill_tag === api.GRADIENT)
            if (isGradientPaintExist) {
                // update existing paint
                const gradient_id = dinoTree.fills.children[i].fill_id
                api.setGradientMatrix(gradient_id, gradientXform)
                api.setGradientTag(gradient_id, gradientType)
                api.setGradientStopLen(gradient_id, gradient.colorStops.length)
                for (let i = 0; i < gradient.colorStops.length; i++) {
                    const stop = gradient.colorStops[i]
                    api.setGradientStop(gradient_id, i, stop.pos, stop.color[0], stop.color[1], stop.color[2], stop.color[3])
                }
                api.updateGradientPixels(gradient_id)
                api.setNodeFillOpacity(dinoTree.fills.children[i].id, opacity)
                api.markNodeChanged(dinoTree.fills.children[i].id)
            } else {
                if (dinoTree.fills.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeFill(dinoTree.fills.children[i])
                } else {
                    // create a path node for new paint
                    dinoTree.fills.children[i] = new DinoSubTree()
                    dinoTree.fills.children[i].id = api.makeNode(api.PATH)
                    VS.view.setParent(dinoTree.fills.children[i].id, node.id)
                    api.addNodeChild(dinoTree.fills.id, dinoTree.fills.children[i].id)
                }

                const gradient_id = api.makeGradient(gradientType, gradientXform)
                dinoTree.fills.children[i].fill_id = gradient_id
                dinoTree.fills.children[i].fill_tag = api.GRADIENT
                api.setGradientStopLen(gradient_id, gradient.colorStops.length)
                for (let i = 0; i < gradient.colorStops.length; i++) {
                    const stop = gradient.colorStops[i]
                    api.setGradientStop(gradient_id, i, stop.pos, stop.color[0], stop.color[1], stop.color[2], stop.color[3])
                }
                api.updateGradientPixels(gradient_id)
                api.setNodeFillOpacity(dinoTree.fills.children[i].id, opacity)
                api.setNodeFillPaint(dinoTree.fills.children[i].id, api.GRADIENT, gradient_id)
            }
            break
        }
        case PaintType.IMAGE: {
            const image = layer.paint.image
            const opacity = layer.paint.params.opacity[0]
            const fillMode = getDinoImageFillMode(layer.paint.imageOptions.mode)

            if (dinoTree.fills.children[i] && dinoTree.fills.children[i].fill_id > 0 && dinoTree.fills.children[i].fill_tag === api.IMAGE) {
                // update existing paint
                const image_id = image.dino_image_id
                dinoTree.fills.children[i].fill_id = image_id
                api.setImageFillMode(image_id, fillMode)
                api.setNodeFillOpacity(dinoTree.fills.children[i].id, opacity)
                api.setNodeFillPaint(dinoTree.fills.children[i].id, api.IMAGE, image_id)
            } else {
                if (dinoTree.fills.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeFill(dinoTree.fills.children[i])
                } else {
                    // create a path node for new paint
                    dinoTree.fills.children[i] = new DinoSubTree()
                    dinoTree.fills.children[i].id = api.makeNode(api.PATH)
                    VS.view.setParent(dinoTree.fills.children[i].id, node.id)
                    api.addNodeChild(dinoTree.fills.id, dinoTree.fills.children[i].id)
                }

                const image_id = image.dino_image_id
                dinoTree.fills.children[i].fill_id = image_id
                dinoTree.fills.children[i].fill_tag = api.IMAGE
                api.setImageFillMode(image_id, fillMode)
                api.setNodeFillOpacity(dinoTree.fills.children[i].id, opacity)
                api.setNodeFillPaint(dinoTree.fills.children[i].id, api.IMAGE, image_id)
            }
            break
        }
        default: {
            throw new Error(`Unsupported fill type: ${layer.paint.type}`)
        }
    }
}

/**
 * @param {DinoSubTree} dinoSubTree
 */
export const destroyDinoSubTreeFill = (dinoSubTree) => {
    switch (dinoSubTree.fill_tag) {
        case api.COLOR: {
            api.destroyColor(dinoSubTree.fill_id)
            dinoSubTree.fill_id = 0
            dinoSubTree.fill_tag = 0
            break
        }
        case api.GRADIENT: {
            api.destroyGradient(dinoSubTree.fill_id)
            dinoSubTree.fill_id = 0
            dinoSubTree.fill_tag = 0
            break
        }
        case api.IMAGE: {
            // Since image is shared resource, we don't destroy it here
            // api.destroyImage(dinoSubTree.fill_id)
            dinoSubTree.fill_id = 0
            dinoSubTree.fill_tag = 0
            break
        }
    }
}

/**
 * @param {DinoSubTree} dinoSubTree
 */
export const destroyDinoSubTreeStroke = (dinoSubTree) => {
    switch (dinoSubTree.stroke_tag) {
        case api.COLOR: {
            api.destroyColor(dinoSubTree.stroke_id)
            dinoSubTree.stroke_id = 0
            dinoSubTree.stroke_tag = 0
            break
        }
        case api.GRADIENT: {
            api.destroyGradient(dinoSubTree.stroke_id)
            dinoSubTree.stroke_id = 0
            dinoSubTree.stroke_tag = 0
            break
        }
        case api.IMAGE: {
            // Since image is shared resource, we don't destroy it here
            // api.destroyImage(dinoSubTree.stroke_id)
            dinoSubTree.stroke_id = 0
            dinoSubTree.stroke_tag = 0
            break
        }
    }
}

/**
 * Update dino tree strokes base on stroke layers on scene node
 * Will create or update dino sub tree stroke for each stroke layer
 * Will destroy all existing dino sub tree stroke if there is no stroke layer
 * @param {VisualServer} VS
 * @param {SceneNode} node
 */
export const updateDinoTreeStrokesFromSceneNode = (VS, node) => {
    // Update or create dino sub tree stroke for each stroke layer
    const strokeLayers = node.item.strokeLayers
    for (let i = 0; i < strokeLayers.length; i++) {
        createOrUpdateDinoSubTreeStrokeFromLayer(VS, node, i, strokeLayers[i])
    }

    // Destroy all existing dino sub tree stroke if there is no stroke layer
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)
    for (let i = strokeLayers.length; i < dinoTree.strokes.children.length; i++) {
        const dinoSubTree = dinoTree.strokes.children[i]
        destroyDinoSubTreeStroke(dinoSubTree)
        api.destroyNode(dinoSubTree.id)
        dinoSubTree.id = 0

        // stroke_data_id could be 0 when switch from action to design mode
        // computedstyle.reload() will create strokes in _applyBaseProps and destroy strokes in forceFireLayerListChanges immediately
        if (dinoSubTree.stroke_data_id !== 0) {
            api.destroyStroke(dinoSubTree.stroke_data_id)
            dinoSubTree.stroke_data_id = 0
        }
    }

    dinoTree.strokes.children.length = strokeLayers.length
}

/**
 * @param {VisualServer} VS
 * @param {SceneNode} node
 * @param {number} i
 * @param {Layer} layer
 */
export const createOrUpdateDinoSubTreeStrokeFromLayer = (VS, node, i, layer) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)

    switch (layer.paint.type) {
        case PaintType.SOLID: {
            const color = layer.paint.params.fill_color
            const opacity = layer.paint.params.opacity[0]

            if (dinoTree.strokes.children[i] && dinoTree.strokes.children[i].stroke_id > 0 && dinoTree.strokes.children[i].stroke_tag === api.COLOR) {
                // update existing paint
                api.setColor(dinoTree.strokes.children[i].stroke_id, color[0], color[1], color[2], opacity)
                api.markNodeChanged(dinoTree.strokes.children[i].id)
            } else {
                if (dinoTree.strokes.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeStroke(dinoTree.strokes.children[i])
                } else {
                    // create path node for new paint
                    dinoTree.strokes.children[i] = new DinoSubTree()
                    if (node.item.type === "text") {
                        dinoTree.strokes.children[i].id = api.makeNode(api.TEXT)
                        dinoTree.strokes.children[i].text_doc_id = api.getTextDocID(dinoTree.strokes.children[i].id)
                    } else {
                        dinoTree.strokes.children[i].id = api.makeNode(api.PATH)
                    }
                    VS.view.setParent(dinoTree.strokes.children[i].id, node.id)
                    api.addNodeChild(dinoTree.strokes.id, dinoTree.strokes.children[i].id)
                }

                const paint_id = api.makeColor(color[0], color[1], color[2], opacity)
                dinoTree.strokes.children[i].stroke_id = paint_id
                dinoTree.strokes.children[i].stroke_tag = api.COLOR
                api.setNodeStrokePaint(dinoTree.strokes.children[i].id, api.COLOR, paint_id)
            }
            break
        }
        case PaintType.GRADIENT_LINEAR:
        case PaintType.GRADIENT_RADIAL:
        case PaintType.GRADIENT_ANGULAR:
        case PaintType.GRADIENT_DIAMOND: {
            const gradient = layer.paint.gradient
            const opacity = layer.paint.params.opacity[0]
            const gradientXform = layer.paint.transform
            const gradientType = getDinoGradientType(layer.paint.type)

            const isGradientPaintExist = dinoTree.strokes.children[i]?.stroke_id > 0 && (dinoTree.strokes.children[i]?.stroke_tag === api.GRADIENT)
            if (isGradientPaintExist) {
                // update existing paint
                const gradient_id = dinoTree.strokes.children[i].stroke_id
                api.setGradientMatrix(gradient_id, gradientXform)
                api.setGradientTag(gradient_id, gradientType)
                api.setGradientStopLen(gradient_id, gradient.colorStops.length)
                for (let i = 0; i < gradient.colorStops.length; i++) {
                    const stop = gradient.colorStops[i]
                    api.setGradientStop(gradient_id, i, stop.pos, stop.color[0], stop.color[1], stop.color[2], stop.color[3])
                }
                api.updateGradientPixels(gradient_id)
                api.setNodeStrokeOpacity(dinoTree.strokes.children[i].id, opacity)
                api.markNodeChanged(dinoTree.strokes.children[i].id)
            } else {
                if (dinoTree.strokes.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeStroke(dinoTree.strokes.children[i])
                } else {
                    // create path node for new paint
                    dinoTree.strokes.children[i] = new DinoSubTree()
                    dinoTree.strokes.children[i].id = api.makeNode(api.PATH)
                    VS.view.setParent(dinoTree.strokes.children[i].id, node.id)
                    api.addNodeChild(dinoTree.strokes.id, dinoTree.strokes.children[i].id)
                }

                const gradient_id = api.makeGradient(gradientType, gradientXform)
                dinoTree.strokes.children[i].stroke_id = gradient_id
                dinoTree.strokes.children[i].stroke_tag = api.GRADIENT
                api.setGradientStopLen(gradient_id, gradient.colorStops.length)
                for (let i = 0; i < gradient.colorStops.length; i++) {
                    const stop = gradient.colorStops[i]
                    api.setGradientStop(gradient_id, i, stop.pos, stop.color[0], stop.color[1], stop.color[2], stop.color[3])
                }
                api.updateGradientPixels(gradient_id)
                api.setNodeStrokeOpacity(dinoTree.strokes.children[i].id, opacity)
                api.setNodeStrokePaint(dinoTree.strokes.children[i].id, api.GRADIENT, gradient_id)
            }
            break
        }
        case PaintType.IMAGE: {
            const image = layer.paint.image
            const opacity = layer.paint.params.opacity[0]
            const fillMode = getDinoImageFillMode(layer.paint.imageOptions.mode)

            if (dinoTree.strokes.children[i] && dinoTree.strokes.children[i].stroke_id > 0 && dinoTree.strokes.children[i].stroke_tag === api.IMAGE) {
                // update existing paint
                const image_id = image.dino_image_id
                dinoTree.strokes.children[i].stroke_id = image_id
                api.setImageFillMode(image_id, fillMode)
                api.setNodeStrokeOpacity(dinoTree.strokes.children[i].id, opacity)
                api.setNodeStrokePaint(dinoTree.strokes.children[i].id, api.IMAGE, image_id)
            } else {
                if (dinoTree.strokes.children[i]) {
                    // if there is existing paint, destroy it first
                    destroyDinoSubTreeStroke(dinoTree.strokes.children[i])
                } else {
                    // create path node for new paint
                    dinoTree.strokes.children[i] = new DinoSubTree()
                    dinoTree.strokes.children[i].id = api.makeNode(api.PATH)
                    VS.view.setParent(dinoTree.strokes.children[i].id, node.id)
                    api.addNodeChild(dinoTree.strokes.id, dinoTree.strokes.children[i].id)
                }

                const image_id = image.dino_image_id
                dinoTree.strokes.children[i].stroke_id = image_id
                dinoTree.strokes.children[i].stroke_tag = api.IMAGE
                api.setImageFillMode(image_id, fillMode)
                api.setNodeStrokeOpacity(dinoTree.strokes.children[i].id, opacity)
                api.setNodeStrokePaint(dinoTree.strokes.children[i].id, api.IMAGE, image_id)
            }
            break
        }
        default: {
            throw new Error(`Unsupported stroke type: ${layer.paint.type}`)
        }
    }
}

/**
 * @param {PaintType} type
 * @returns {number}
 */
const getDinoGradientType = (type) => {
    switch (type) {
        case PaintType.GRADIENT_LINEAR: return 0
        case PaintType.GRADIENT_RADIAL: return 1
        case PaintType.GRADIENT_ANGULAR: return 2
        case PaintType.GRADIENT_DIAMOND: return 3
    }
    return 0
}

/**
 * @param {ImageMode} mode
 * @returns {number}
 */
const getDinoImageFillMode = (mode) => {
    switch (mode) {
        case ImageMode.STRETCH: return 0
        case ImageMode.FILL: return 1
        case ImageMode.FIT: return 2
    }
}

/**
 * @param {BlendMode} blendMode
 * @returns {number}
 */
export const getDinoBlendMode = (blendMode) => {
    switch (blendMode) {
        case BlendMode.NORMAL:
        case BlendMode.PASS_THROUGH: {
            return 0
        }
        case BlendMode.DARKEN: return 1
        case BlendMode.MULTIPLY: return 2
        case BlendMode.COLOR_BURN: return 3
        case BlendMode.LIGHTEN: return 4
        case BlendMode.SCREEN: return 5
        case BlendMode.COLOR_DODGE: return 6
        case BlendMode.OVERLAY: return 7
        case BlendMode.SOFT_LIGHT: return 8
        case BlendMode.HARD_LIGHT: return 9
        case BlendMode.DIFFERENCE: return 10
        case BlendMode.EXCLUSION: return 11
        case BlendMode.HUE: return 12
        case BlendMode.SATURATION: return 13
        case BlendMode.COLOR: return 14
        case BlendMode.LUMINOSITY: return 15
    }
    return 0
}

export function updateBooleanParent(node) {
    let booleanParentNode = node.parent
    while (booleanParentNode) {
        if (booleanParentNode.item.isBooleanGroup()) {
            booleanParentNode.item.update(UpdateType.GEOMETRY)
        }
        booleanParentNode = booleanParentNode.parent
    }
}

/**
 * @param {RenderItem} item
 * @param {Element} element
 * @returns {NodeTypes}
 */
function _determineType(_, element) {
    const entityType = element.get('type')
    const elementType = element.get('elementType')
    if (entityType === EntityType.WORKSPACE) {
        return 'workspace'
    }
    switch (elementType) {
        case ElementType.CONTAINER: {
            if (element.isNormalGroup) {
                return "group"
            } else if (element.isBooleanType()) {
                return "path"
            } else if (element.isMaskGroup()) {
                return "mask"
            } else {
                return "container"
            }
        }
        case ElementType.SCREEN: return "screen"
        case ElementType.PATH: return "path"
        case ElementType.TEXT: return "text"
    }
    return "group"
}
