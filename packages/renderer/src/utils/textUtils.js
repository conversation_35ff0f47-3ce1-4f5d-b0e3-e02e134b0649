import { TextAlignHorizontalType, TextAlignVerticalType, TextResizeMode } from '@phase-software/types'
import { NO_INTERACTION } from '@phase-software/data-utils'
import { dino } from '../dino'
import { UpdateType } from '../visual_server/RenderItem'
import { TEXT_RESIZE_MODE_MAP } from "../index"
import { CMP_EPSILON, Transform2D, Vector2 } from '../math'
import { MIN_SIZE_THRESHOLD } from '../constants'
import { fixInfiniteSkew } from '../visual_server/Transform'

/** @typedef {import('../visual_server/VisualServer').VisualServer} VisualServer */

/**
 * Checks if there's a significant change in dimensions
 * @param {Vector2} originalSize - Original element size
 * @param {number} width - New width
 * @param {number} height - New height
 * @returns {boolean} - True if there's a significant change
 */
function hasSignificantDimensionChange(originalSize, width, height) {
    return Math.abs(originalSize[0] - width) > CMP_EPSILON ||
        Math.abs(originalSize[1] - height) > CMP_EPSILON
}

/**
 * Checks if there's a significant change in height
 * @param {Vector2} originalSize - Original element size
 * @param {number} height - New height
 * @returns {boolean} - True if there's a significant change
 */
function hasSignificantHeightChange(originalSize, height) {
    return Math.abs(originalSize[1] - height) > CMP_EPSILON
}
let v = 1;
/**
 * @param {VisualServer} VS
 * @param {import('../dino').default} node
 */
export const updateDinoTextPath = (VS, node) => {
    const dinoTree = VS.view.getOrCreateDinoTree(node.id)

    const fontFamily = node.fontFamily || "Noto Sans"
    const fontSize = node.fontSize
    const fontStyle = node.fontStyle
    const fontWeight = node.fontWeight
    const lineHeight = node.lineHeight
    const content = node.content
    const letterSpacing = node.letterSpacing
    const paragraphSpacing = 0
    const horizontalAlignment = node.horizontalAlignment
    const verticalAlignment = node.verticalAlignment

    if (node.updateFlags & UpdateType.STYLE) {
        let url = VS.dataStore.fontManager.getFontUrl(fontFamily, fontStyle, fontWeight)
        if (!url) {
            console.warn(`cannot found the URL for ${fontFamily}-${fontStyle}-${fontWeight}`)
            url = VS.dataStore.fontManager.getFontUrl(fontFamily, "Regular", 400)
        }
        dino().fetchFont(fontFamily, url, fontStyle, fontWeight)
    }

    let maxWidth = -1
    if (node.resizingMode !== TEXT_RESIZE_MODE_MAP[TextResizeMode.AUTO_WIDTH]) {
        maxWidth = node.transform.size.x
    }
    let maxHeight = -1
    if (node.resizingMode === TEXT_RESIZE_MODE_MAP[TextResizeMode.FIXED]) {
        maxHeight = node.transform.size.y
    }

    // using text node id to create/update text path data
    const textNodeId = dinoTree.text_node_id
    let start = 0
    let end = 0
    let text_cursor_idx = 0

    const textRange = VS.dataStore.selection.get('textRange')
    if (textRange) {
        start = textRange.start
        end = textRange.end
        text_cursor_idx = textRange.cursor
    } else {
        end = content.length
        text_cursor_idx = content.length
    }
    const { start_block, start_char, end_block, end_char, cursor_index: block_cursor_idx, active_block_index } = getTextSelection(content, start, end, text_cursor_idx)

    // Clean up old paths to prevent memory leaks
    // cleanupDinoTreePaths(dinoTree)

    // set all fill layers to the same text doc and style, if there is no any fill layers, the text path is empty path
    for (let i = 0; i < dinoTree.fills.children.length; i++) {
        dino().setTextDoc(dinoTree.fills.children[i].text_doc_id, content, block_cursor_idx, active_block_index)
        dino().setTextStyle(dinoTree.fills.children[i].text_doc_id, fontFamily, fontStyle, fontWeight, fontSize, letterSpacing, lineHeight, paragraphSpacing, horizontalAlignment, verticalAlignment)
        dino().buildLayoutAndPath(dinoTree.fills.children[i].id, dinoTree.fills.children[i].text_doc_id, maxWidth, maxHeight)
        dino().markNodeChanged(dinoTree.fills.children[i].id)
    }

    // set all stroke layers to the same text doc and style
    for (let i = 0; i < dinoTree.strokes.children.length; i++) {
        dino().setTextDoc(dinoTree.strokes.children[i].text_doc_id, content, block_cursor_idx, active_block_index)
        dino().setTextStyle(
            dinoTree.strokes.children[i].text_doc_id,
            fontFamily,
            fontStyle,
            fontWeight,
            fontSize,
            letterSpacing,
            lineHeight,
            paragraphSpacing,
            horizontalAlignment,
            verticalAlignment)
        dino().buildLayoutAndPath(dinoTree.strokes.children[i].id, dinoTree.strokes.children[i].text_doc_id, maxWidth, maxHeight)
        dino().markNodeChanged(dinoTree.strokes.children[i].id)

        // Set text selection for this stroke layer
        if (textRange && start !== end) {
            dino().setTextSelection(
                dinoTree.strokes.children[i].text_doc_id,
                start_block,
                start_char,
                end_block,
                end_char,
                block_cursor_idx,
                active_block_index
            )
        }
    }

    dino().setTextDoc(dinoTree.text_doc_id, content, block_cursor_idx, active_block_index)
    dino().setTextStyle(dinoTree.text_doc_id, fontFamily, fontStyle, fontWeight, fontSize, letterSpacing, lineHeight, paragraphSpacing, horizontalAlignment, verticalAlignment)
    dino().buildLayoutAndPath(textNodeId, dinoTree.text_doc_id, maxWidth, maxHeight)
    dino().markNodeChanged(textNodeId)
    // because the Dino has updated the text right after the edit
    // we need to set the selection again, when we update the content as giving a full content
    dino().setTextSelection(dinoTree.text_doc_id, start_block, start_char, end_block, end_char, block_cursor_idx, active_block_index)

    const pathData = dino().getTextPathData(textNodeId, dinoTree.text_doc_id)
    if (dinoTree.path_id) {
        // Update existing path
        dino().setPath(dinoTree.path_id, pathData)
    } else {
        // Create new path
        dinoTree.path_id = dino().makePath(pathData)
    }

}


const getTextSelection = (text, start, end, cursor) => {
    let start_block = 0
    let end_block = 0
    let start_char = 0
    let end_char = 0
    let active_block_index = 0
    let cursor_index = 0

    let block_index = 0
    let current_char_index = 0
    let len = -1
    for (let i = 0; i <= text.length; i++) {
        if (i === cursor) {
            active_block_index = block_index
            cursor_index = current_char_index
        }
        if (i === start) {
            start_block = block_index
            start_char = current_char_index
            len = end - start
        }
        if (len > 0) {
            len--
        } else if (len === 0) {
            end_block = block_index
            end_char = current_char_index
            break // Exit loop once end position is found
        }
        // eslint-disable-next-line eqeqeq
        if (i < text.length && text[i] == '\n') {
            block_index++
            current_char_index = 0
        } else {
            current_char_index++
        }
    }

    // Adjust end_char for the case when end is at the end of a block
    if (end > 0 && text[end - 1] === '\n') {
        end_char = 0
    }

    return { start_block, start_char, end_block, end_char, cursor_index, active_block_index }
}

/**
 * Updates text element properties based on the calculated bounding box
 * Handles different resizing modes (auto-width, auto-height) appropriately
 *
 * @param {VisualServer} VS - Visual server instance
 * @param {Element} element - The text element to update
 * @param {number} width - New calculated width from text bounding box
 * @param {number} height - New calculated height from text bounding box
 * @param {object} options - Additional options for the update
 */
export const updateTextFromBBox = (VS, element, width, height, options) => {
    // Get current element properties
    const resizingMode = element.get('resizingMode')
    const originalSize = element.get('size')
    const horizontalAlignment = element.get('horizontalAlignment')
    const verticalAlignment = element.get('verticalAlignment')

    // Apply updates based on resizing mode
    if (resizingMode === TextResizeMode.AUTO_WIDTH) {
        // Auto-width mode: adjust both width and height
        _updateAutoWidthText(
            VS,
            originalSize,
            width,
            height,
            element,
            horizontalAlignment,
            verticalAlignment,
            options
        )
    } else if (resizingMode === TextResizeMode.AUTO_HEIGHT) {
        // Auto-height mode: maintain width, adjust height only
        _updateAutoHeightText(
            VS,
            originalSize,
            height,
            element,
            horizontalAlignment,
            verticalAlignment,
            options
        )
    }
    // Fixed mode: no updates needed (handled by early return in caller)
}



/**
 * Updates text element properties for auto-width resizing mode
 * In this mode, both width and height are adjusted based on text content
 *
 * @param {VisualServer} VS - Visual server instance
 * @param {Vector2} originalSize - Original element size
 * @param {number} width - New calculated width from text bounding box
 * @param {number} height - New calculated height from text bounding box
 * @param {Element} element - The text element to update
 * @param {TextAlignHorizontalType} horizontalAlignment - Horizontal alignment of text
 * @param {TextAlignVerticalType} verticalAlignment - Vertical alignment of text
 * @param {object} options - Additional options for the update
 */
function _updateAutoWidthText(VS, originalSize, width, height, element, horizontalAlignment, verticalAlignment, options) {
    const elementId = element.get('id')

    // Handle design mode updates
    if (VS.dataStore.isDesignMode) {
        // Skip update if dimensions haven't changed significantly
        if (!hasSignificantDimensionChange(originalSize, width, height)) {
            return
        }

        // Prepare changes with new dimensions and centered reference point
        const changes = {
            width,
            height,
            referencePointX: 0.5 * width,
            referencePointY: 0.5 * height
        }
        const contentAnchor = element.get('contentAnchor')
        if (originalSize.x >= MIN_SIZE_THRESHOLD){
            changes.contentAnchorX = contentAnchor.x * width / originalSize.x
        }
        if (originalSize.y >= MIN_SIZE_THRESHOLD){
            changes.contentAnchorY = contentAnchor.y * height / originalSize.y
        }

        // Calculate position adjustments based on alignment
        const newTranslate = _getFixedPositionByChanges(
            VS,
            elementId,
            changes,
            horizontalAlignment,
            verticalAlignment
        )

        // Apply position adjustments if available
        if (newTranslate) {
            changes.translateX = newTranslate.x
            changes.translateY = newTranslate.y
        }

        // Apply all changes to the element
        element.sets(changes, options)
    }
    // Handle non-design mode updates
    else {
        // Prepare changes with new dimensions
        const changes = { width, height }

        // Get base values for reference point calculations
        const baseRefPoint = element.getBaseValue('referencePoint')
        const baseDimensions = element.getBaseValue('dimensions')

        // Update reference points based on alignment
        fillRefPointToChangeByAlignment(
            horizontalAlignment,
            verticalAlignment,
            changes,
            width,
            height,
            baseRefPoint,
            baseDimensions
        )

        // Apply changes without interaction flags
        element.sets(changes, NO_INTERACTION)
    }
}

/**
 * @param horizontalAlignment
 * @param verticalAlignment
 * @param changes
 * @param width
 * @param height
 * @param baseRefPoint
 * @param baseDimensions
 */
function fillRefPointToChangeByAlignment(horizontalAlignment, verticalAlignment, changes, width, height, baseRefPoint, baseDimensions) {
    const baseInvertRefPointX = baseDimensions.width - baseRefPoint.referencePointX
    const baseInvertRefPointY = baseDimensions.height - baseRefPoint.referencePointY
    switch (horizontalAlignment) {
        case TextAlignHorizontalType.CENTER:
            changes.referencePointX = 0.5 * width
            break
        case TextAlignHorizontalType.RIGHT:
            changes.referencePointX = width - baseInvertRefPointX
            break
        case TextAlignHorizontalType.LEFT:
            changes.referencePointX = baseRefPoint.referencePointX
            break
    }
    switch (verticalAlignment) {
        case TextAlignVerticalType.MIDDLE:
            changes.referencePointY = 0.5 * height
            break
        case TextAlignVerticalType.BOTTOM:
            changes.referencePointY = height - baseInvertRefPointY
            break
        case TextAlignVerticalType.TOP:
            changes.referencePointY = baseRefPoint.referencePointY
            break
    }
}

/**
 * Updates the referencePointY in changes object based on vertical alignment
 * @param {object} changes - The changes object to update with referencePointY
 * @param {number} height - The height value to use for calculations
 * @param {TextAlignVerticalType} verticalAlignment - The vertical alignment type
 * @param {object} baseRefPoint - The base reference point object
 * @param {object} baseDimensions - The base dimensions object
 */
function updateReferencePointYByVerticalAlignment(changes, height, verticalAlignment, baseRefPoint, baseDimensions) {
    const baseInvertRefPointY = baseDimensions.height - baseRefPoint.referencePointY
    switch (verticalAlignment) {
        case TextAlignVerticalType.MIDDLE:
            changes.referencePointY = 0.5 * height
            break
        case TextAlignVerticalType.BOTTOM:
            changes.referencePointY = height - baseInvertRefPointY
            break
        case TextAlignVerticalType.TOP:
            // do nothing
            break
    }
}

/**
 * Updates text element properties for auto-height resizing mode
 * In this mode, width is maintained while height is adjusted based on text content
 *
 * @param {VisualServer} VS - Visual server instance
 * @param {Vector2} originalSize - Original element size
 * @param {number} height - New calculated height from text bounding box
 * @param {Element} element - The text element to update
 * @param {TextAlignHorizontalType} horizontalAlignment - Horizontal alignment of text
 * @param {TextAlignVerticalType} verticalAlignment - Vertical alignment of text
 * @param {object} options - Additional options for the update
 */
function _updateAutoHeightText(VS, originalSize, height, element, horizontalAlignment, verticalAlignment, options) {
    const elementId = element.get('id')

    // Handle design mode updates
    if (VS.dataStore.isDesignMode) {
        // Skip update if height hasn't changed significantly
        if (!hasSignificantHeightChange(originalSize, height)) {
            return
        }

        // Prepare changes with new height and centered vertical reference point
        const changes = {
            height,
            referencePointY: 0.5 * height
        }

        const contentAnchor = element.get('contentAnchor')
        if (originalSize.y >= MIN_SIZE_THRESHOLD){
            changes.contentAnchorY = contentAnchor.y * height / originalSize.y
        }


        // Calculate position adjustments based on alignment
        const newTranslate = _getFixedPositionByChanges(
            VS,
            elementId,
            changes,
            horizontalAlignment,
            verticalAlignment
        )

        // Apply vertical position adjustment if available
        if (newTranslate) {
            changes.translateY = newTranslate.y
            changes.referencePointY = 0.5 * height
        }

        // Apply changes to the element
        // Note: The size changes cannot be undone because the renderer will set the adjusted size back,
        // which could lead to an infinite loop
        element.sets(changes, options)
    }
    // Handle non-design mode updates
    else {
        // Prepare changes with new height only
        const changes = { height }

        // Get base values for reference point calculations
        const baseRefPoint = element.getBaseValue('referencePoint')
        const baseDimensions = element.getBaseValue('dimensions')

        // Update vertical reference point based on alignment
        updateReferencePointYByVerticalAlignment(
            changes,
            height,
            verticalAlignment,
            baseRefPoint,
            baseDimensions
        )

        // Apply changes without interaction flags
        element.sets(changes, NO_INTERACTION)
    }
}


/**
 * Get position fixed by other properties changes, always call this function before apply changes, the change should only contain one type of property
 * @param {VisualServer} VS
 * @param {string} elementId
 * @param {object} changes
 * @param {TextAlignHorizontalType} horizontalAlignment
 * @param {TextAlignVerticalType} verticalAlignment
 * @param {boolean} useBaseValue
 * @returns {import('../math/Vector2').Vector2Like|null} translate | translate.x | translate.y | null
 */
function _getFixedPositionByChanges(VS, elementId, changes, horizontalAlignment, verticalAlignment, useBaseValue = false) {
    // Get node and validate
    const node = _getAndValidateNode(VS, elementId, changes)
    if (!node) return null

    // Extract transform properties
    const transform = useBaseValue ? node.baseTransform : node.transform
    const { contentAnchor, referencePoint, size, translate, skew, scale, rotation } = _extractTransformProperties(transform)

    // Determine updated values
    const updatedValues = _determineUpdatedValues(changes, contentAnchor, referencePoint)

    // Calculate size offsets
    const offsets = _calculateOffsetsForZeroSize(changes, size)

    // Calculate pivot offsets
    const { newPivotOffset, originPivotOffset } = _calculatePivotOffsets(
        updatedValues,
        contentAnchor,
        referencePoint,
        size,
        changes,
        horizontalAlignment,
        verticalAlignment
    )

    // Calculate position adjustment
    return _calculatePositionAdjustment(
        translate,
        rotation,
        skew,
        scale,
        newPivotOffset,
        originPivotOffset,
        offsets
    )
}

/**
 * Cleans up any orphaned paths in a DinoTree
 * @param {DinoTree} dinoTree - The DinoTree to clean up
 */
export const cleanupDinoTreePaths = (dinoTree) => {
    console.log("cleanupDinoTreePaths", dinoTree.path_id)
    // Clean up main path if it exists
    if (dinoTree.path_id) {
        dino().destroyPath(dinoTree.path_id)
        dinoTree.path_id = 0
    }

    // Clean up base path if it exists
    if (dinoTree.base_path_id) {
        dino().destroyPath(dinoTree.base_path_id)
        dinoTree.base_path_id = 0
    }

    // Clean up paths in fills
    if (dinoTree.fills && dinoTree.fills.children) {
        for (let i = 0; i < dinoTree.fills.children.length; i++) {
            const fillSubTree = dinoTree.fills.children[i]
            if (fillSubTree && fillSubTree.path_id) {
                dino().destroyPath(fillSubTree.path_id)
                fillSubTree.path_id = 0
            }
        }
    }

    // Clean up paths in strokes
    if (dinoTree.strokes && dinoTree.strokes.children) {
        for (let i = 0; i < dinoTree.strokes.children.length; i++) {
            const strokeSubTree = dinoTree.strokes.children[i]
            if (strokeSubTree && strokeSubTree.path_id) {
                dino().destroyPath(strokeSubTree.path_id)
                strokeSubTree.path_id = 0
            }
        }
    }
}

/**
 * Get and validate node for position fixing
 * @param {VisualServer} VS
 * @param {string} elementId
 * @param {object} changes
 * @returns {RenderItem|null} The node or null if invalid
 */
function _getAndValidateNode(VS, elementId, changes) {
    const node = VS.getRenderItem(elementId)
    if (!node) return null

    // Skip position fixing if no relevant changes
    const hasRelevantChanges = changes.contentAnchorX !== undefined ||
        changes.contentAnchorY !== undefined ||
        changes.width !== undefined ||
        changes.height !== undefined

    if (!hasRelevantChanges) return null

    return node
}

/**
 * Extract transform properties from node transform
 * @param {Transform} transform
 * @returns {object} Extracted transform properties
 */
function _extractTransformProperties(transform) {
    return {
        contentAnchor: transform.contentAnchor,
        referencePoint: transform.referencePoint,
        size: { x: transform.size.x, y: transform.size.y },
        translate: { x: transform.translate.x, y: transform.translate.y },
        skew: transform.skew,
        scale: transform.scale,
        rotation: transform.rotation
    }
}

/**
 * Determine updated values from changes
 * @param {object} changes
 * @param {object} contentAnchor
 * @param {object} referencePoint
 * @returns {object} Updated values
 */
function _determineUpdatedValues(changes, contentAnchor, referencePoint) {
    return {
        contentAnchorX: changes.contentAnchorX ?? contentAnchor.x,
        contentAnchorY: changes.contentAnchorY ?? contentAnchor.y,
        referencePointX: changes.referencePointX ?? referencePoint.x,
        referencePointY: changes.referencePointY ?? referencePoint.y
    }
}

/**
 * Calculate offsets for near-zero size cases
 * @param {object} changes
 * @param {object} size
 * @returns {object} X and Y offsets
 */
function _calculateOffsetsForZeroSize(changes, size) {
    let offsetX = 0
    let offsetY = 0

    // Calculate X offset for near-zero width cases
    if (changes.width !== undefined) {
        const isNewWidthZero = changes.width !== 0 && changes.width < MIN_SIZE_THRESHOLD
        const isCurWidthZero = size.x !== 0 && size.x < MIN_SIZE_THRESHOLD

        if (isNewWidthZero && !isCurWidthZero) {
            offsetX = -changes.width * 0.5
        } else if (isCurWidthZero && !isNewWidthZero) {
            offsetX = size.x * 0.5
        }
    }

    // Calculate Y offset for near-zero height cases
    if (changes.height !== undefined) {
        const isNewHeightZero = changes.height !== 0 && changes.height < MIN_SIZE_THRESHOLD
        const isCurHeightZero = size.y !== 0 && size.y < MIN_SIZE_THRESHOLD

        if (isNewHeightZero && !isCurHeightZero) {
            offsetY = -changes.height * 0.5
        } else if (isCurHeightZero && !isNewHeightZero) {
            offsetY = size.y * 0.5
        }
    }

    return { offsetX, offsetY }
}

/**
 * Calculate pivot offsets based on alignment
 * @param {object} updatedValues
 * @param {object} contentAnchor
 * @param {object} referencePoint
 * @param {object} size
 * @param {object} changes
 * @param {TextAlignHorizontalType} horizontalAlignment
 * @param {TextAlignVerticalType} verticalAlignment
 * @returns {object} New and original pivot offsets
 */
function _calculatePivotOffsets(updatedValues, contentAnchor, referencePoint, size, changes, horizontalAlignment, verticalAlignment) {
    const { contentAnchorX, contentAnchorY, referencePointX, referencePointY } = updatedValues

    const newPivotOffset = new Vector2(
        contentAnchorX + referencePointX,
        contentAnchorY + referencePointY
    )

    const originPivotOffset = new Vector2(
        contentAnchor.x + referencePoint.x,
        contentAnchor.y + referencePoint.y
    )

    // Adjust horizontal pivot offset based on alignment
    switch (horizontalAlignment) {
        case TextAlignHorizontalType.CENTER:
            newPivotOffset.x = (contentAnchorX + referencePointX) - (changes.width ? changes.width : size.x) * 0.5
            originPivotOffset.x -= size.x * 0.5
            break
        case TextAlignHorizontalType.RIGHT:
            newPivotOffset.x = (contentAnchorX + referencePointX) - (changes.width ? changes.width : size.x)
            originPivotOffset.x -= size.x
            break
        // LEFT alignment is the default case, no changes needed
    }

    // Adjust vertical pivot offset based on alignment
    switch (verticalAlignment) {
        case TextAlignVerticalType.MIDDLE:
            newPivotOffset.y = (contentAnchorY + referencePointY) - (changes.height ? changes.height : size.y) * 0.5
            originPivotOffset.y -= size.y * 0.5
            break
        case TextAlignVerticalType.BOTTOM:
            newPivotOffset.y = (contentAnchorY + referencePointY) - (changes.height ? changes.height : size.y)
            originPivotOffset.y -= size.y
            break
        // TOP alignment is the default case, no changes needed
    }

    return { newPivotOffset, originPivotOffset }
}

/**
 * Calculate position adjustment using transformation matrices
 * @param {object} translate
 * @param {number} rotation
 * @param {object} skew
 * @param {object} scale
 * @param {Vector2} newPivotOffset
 * @param {Vector2} originPivotOffset
 * @param {object} offsets
 * @returns {object} Adjusted position
 */
function _calculatePositionAdjustment(translate, rotation, skew, scale, newPivotOffset, originPivotOffset, offsets) {
    const { offsetX, offsetY } = offsets
    const newTranslate = new Vector2(translate.x, translate.y)

    // Create transformation matrix for new state
    const newLocal = new Transform2D()
        .translate_right(newTranslate.x, newTranslate.y)
        .rotate_right(rotation)
        .skew_right(fixInfiniteSkew(skew.x), fixInfiniteSkew(skew.y))
        .scale_right(scale.x, scale.y)
        .translate_right(-newPivotOffset.x, -newPivotOffset.y)

    // Create transformation matrix for original state
    const originLocal = new Transform2D()
        .translate_right(translate.x, translate.y)
        .rotate_right(rotation)
        .skew_right(fixInfiniteSkew(skew.x), fixInfiniteSkew(skew.y))
        .scale_right(scale.x, scale.y)
        .translate_right(-originPivotOffset.x, -originPivotOffset.y)

    // Calculate position difference between original and new transformations
    const newAffinedLocalPos = newLocal.get_origin()
    const desireAffinedLocalPos = originLocal.get_origin()

    // Return the adjusted position
    return {
        x: offsetX + newTranslate.x + (desireAffinedLocalPos.x - newAffinedLocalPos.x),
        y: offsetY + newTranslate.y + (desireAffinedLocalPos.y - newAffinedLocalPos.y)
    }
}
